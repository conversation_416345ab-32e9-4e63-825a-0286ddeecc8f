"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggprism)

library(ggpattern)

#构建数据集
pattern_bar <- data.frame(
  name=c("A","B","C","D","E") ,  
  value=c(2,5,8,12,20))

									

####################################图4-1-23（a）单数据柱形图绘制示例（升序排列）
ggplot(data = pattern_bar,aes(x = name,y = value)) +
  ggpattern::geom_bar_pattern(stat= "identity",aes(pattern=name,pattern_fill=name),
                              width=0.7,pattern_density = 0.2,pattern_spacing = 0.025,
                              fill="white",colour="black",)+
  scale_pattern_fill_grey() +
  scale_y_continuous(expand = c(0, 0),limits = c(0,20),
                     breaks = seq(0,20,5)) +
  labs(x="Name",y="Values") +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                 hjust = 0.3),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-23 ggpattern和Excel的纹理填充柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-23 ggpattern和Excel的纹理填充柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)