"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggridges)


#读取数据

group_data <- readr::read_csv("\\第3章 单变量图表绘制\\山脊图数据.csv")



####################################图3-2-3（a）使用ggridges包绘制的“山脊”图示例1 
ggplot(group_data, aes(x = depth, y = color)) +
  geom_density_ridges(size=.8) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-3 使用ggridges包绘制的“山脊”图示例_a.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-3 使用ggridges包绘制的“山脊”图示例_a.pdf",
       width =5, height = 5,device = cairo_pdf)


####################################图3-2-3（b）使用ggridges包绘制的“山脊”图示例2
ggplot(group_data, aes(x = depth, y = color)) +
  geom_density_ridges(stat = "binline",size=.8,bins=10) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-3 使用ggridges包绘制的“山脊”图示例_b.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-3 使用ggridges包绘制的“山脊”图示例_b.pdf",
       width =5, height = 5,device = cairo_pdf)
	   
	   
