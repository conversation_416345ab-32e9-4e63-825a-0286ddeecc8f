
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(openair)

#######################################图7-13-6 （a）使用polarPlot() 函数绘制示例1

png(file="\\第7章 其他类型图的绘制\\图7-13-6 使用polarPlot()函数绘制示例_a.png",
    width = 4000, height = 4200,res=1000)
par(mar = rep(2,4))
# weighted mean SO2 concentrations
polarPlot(mydata, pollutant = "nox",cols = parula(100),key.header = "mean nox (ug/m3)", 
          key.position = "bottom",
          key.footer = NULL)
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-13-6 使用polarPlot()函数绘制示例_a.pdf",
    width = 4, height = 4.2)
par(mar = rep(2,4))
# weighted mean SO2 concentrations
polarPlot(mydata, pollutant = "nox",cols = parula(100),key.header = "mean nox (ug/m3)", 
          key.position = "bottom",
          key.footer = NULL)
dev.off()

#######################################图7-13-6 （b）使用polarPlot() 函数绘制示例1

png(file="\\第7章 其他类型图的绘制\\图7-13-6 使用polarPlot()函数绘制示例_b.png",
    width = 4000, height = 4200,res=1000)
par(mar = rep(2,4))
#数据处理
mydata <- mutate(mydata, ratio = so2 / nox)
polarPlot(filter(mydata, ratio < 0.1), pollutant = "ratio",cols = parula(100),
          key.position = "bottom",key.header = "so2/nox ratio",key.footer = NULL)
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-13-6 使用polarPlot()函数绘制示例_b.pdf",
    width = 4, height = 4.2)
par(mar = rep(2,4))
#数据处理
mydata <- mutate(mydata, ratio = so2 / nox)
polarPlot(filter(mydata, ratio < 0.1), pollutant = "ratio",cols = parula(100),
          key.position = "bottom",key.header = "so2/nox ratio",key.footer = NULL)
dev.off()


