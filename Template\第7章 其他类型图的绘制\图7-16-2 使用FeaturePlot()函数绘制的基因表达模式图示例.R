
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

SeuratData::InstallData("pbmc3k")

library(Seurat)
library(SeuratData)
library(ggplot2)
library(patchwork)
library(pbmc3k.SeuratData)
data("pbmc3k.final")
pbmc3k.final$groups <- sample(c("group1", "group2"), size = ncol(pbmc3k.final), replace = TRUE)
features <- c("LYZ", "CCL5", "IL32", "PTPRCAP", "FCGR3A", "PF4")
pbmc3k.final

FeaturePlot(pbmc3k.final, features = c("MS4A1", "PTPRCAP"), min.cutoff = "q10", 
            max.cutoff = "q90")
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-16-2 使用FeaturePlot()函数绘制的基因表达模式图示例.png",
       width =8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-16-2 使用FeaturePlot()函数绘制的基因表达模式图示例.pdf",
       width =8, height = 4.5,device = cairo_pdf)
	   
	   