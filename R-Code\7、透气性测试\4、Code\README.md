# MVTR计算工具使用说明

## 功能描述
这个MATLAB工具用于计算材料的水蒸气透过率（MVTR），单位为 kg/(m²·24h)。

## 文件说明
- `calculate_MVTR.m` - 主要计算函数
- `example_data.csv` - 示例数据文件
- `README.md` - 使用说明

## CSV文件格式要求
CSV文件必须包含以下4列（可以有表头，也可以没有）：
1. **材料名称** - 材料的标识名称
2. **初始重量** - 实验开始时的重量（克）
3. **实验时间** - 实验持续时间（天）
4. **结束重量** - 实验结束时的重量（克）

## 使用方法

### 基本用法
```matlab
% 使用默认半径14mm计算MVTR
calculate_MVTR('your_data.csv');
```

### 指定半径
```matlab
% 指定样品半径为15mm
calculate_MVTR('your_data.csv', 15);
```

### 保存结果到文件
```matlab
% 计算并保存结果到CSV文件
calculate_MVTR('your_data.csv', 14, 'results.csv');
```

### 示例
```matlab
% 使用示例数据
calculate_MVTR('example_data.csv', 14, 'example_results.csv');
```

## 输出说明
函数会输出：
1. **控制台显示** - 详细的计算过程和结果
2. **工作空间变量** - `mvtr_results` 表格变量
3. **结果文件** - 如果指定了输出文件路径

## 计算公式
```
MVTR = |重量变化| / (有效面积 × 时间)
```

其中：
- 重量变化 = |结束重量 - 初始重量| (克)
- 有效面积 = π × (半径/1000)² (平方米)
- 时间 = 实验天数 (天)
- 最终单位：kg/(m²·24h)

## 注意事项
1. 确保CSV文件编码为UTF-8
2. 重量单位必须为克(g)
3. 时间单位必须为天
4. 半径单位为毫米(mm)
5. 函数会自动计算重量变化的绝对值

## 错误处理
- 文件不存在时会报错
- 数据列数不足时会报错
- 自动处理数值转换问题
