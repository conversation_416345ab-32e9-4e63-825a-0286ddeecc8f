"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggside)


#读取数据
penguins <- read.csv("\\第4章 双变量图形的绘制\\penguins.csv")
tips <- read.csv("\\第4章 双变量图形的绘制\\tips.csv")
planets <- read.csv("\\第4章 双变量图形的绘制\\planets.csv")

###################################图4-2-37 (a) 使用ggside绘制的边际组合图样式1
ggplot(data = penguins,aes(x=bill_length_mm,y=bill_depth_mm,fill=species)) +
  geom_point(shape=21,size=3,stroke=0.5) +
  ggside::geom_xsidedensity(alpha = 0.4, position = "stack",show.legend = FALSE) +
  ggside::geom_ysidedensity(alpha = 0.4,position = "stack") +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="X Values",y="Y Values") +
  theme_classic() +
  theme(legend.position = "top",
        legend.title = element_blank(),
        ggside.panel.border = element_rect(NA, "black", linewidth = 0.5),
        ggside.axis.text = element_blank(),
        ggside.axis.ticks = element_blank(),
        #legend.background = element_blank(),
        #legend.position = c(0.8,0.8),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-2-37 (b) 使用ggside绘制的边际组合图样式2
ggplot(data = tips,aes(x=total_bill,y=tip,fill=sex)) +
  geom_point(shape=21,size=3,stroke=0.5) +
  ggside::geom_ysidehistogram(colour="black",show.legend = FALSE) +
  ggside::geom_xsidehistogram(colour="black") +
  ggsci::scale_fill_aaas() +
  labs(x="X Values",y="Y Values") +
  theme_classic() +
  theme(legend.position = "top",
        legend.title = element_blank(),
        ggside.panel.border = element_rect(NA, "black", linewidth = 0.5),
        ggside.axis.text = element_blank(),
        ggside.axis.ticks = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-2-37 (c) 使用ggside绘制的边际组合图样式3
ggplot(data = tips,aes(x=total_bill,y=tip,fill=sex)) +
  geom_point(shape=21,size=3,stroke=0.5) +
  ggside::geom_ysideboxplot(orientation = "x",show.legend = FALSE) +
  ggside::geom_xsideviolin(orientation = "y",show.legend = FALSE) +
  ggsci::scale_fill_jco() +
  labs(x="X Values",y="Y Values") +
  theme_classic() +
  theme(legend.position = "top",
        legend.title = element_blank(),
        ggside.panel.border = element_rect(NA, "black", linewidth = 0.5),
        ggside.axis.text = element_blank(),
        ggside.axis.ticks = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-2-37 (d) 使用ggside绘制的边际组合图样式4
ggplot(data = planets,aes(x=year,y=distance)) + 
   geom_bin_2d(bins=20) +
   ggside::geom_ysidehistogram(fill="gray60",colour="black") +
   ggside::geom_xsidehistogram(fill="gray60",colour="black") +
   scale_y_log10() +
   scale_fill_gradientn(colours = parula(100),
                        guide = guide_colorbar(
                            ticks = FALSE,
                            barwidth = unit(0.4, "cm"),
                            barheight =unit(2.5, "cm"),
                            frame.colour = "black"
                        )) +
   labs(x="X Values",y="Y Values") +
  theme_classic() +
  theme(legend.position = c(0.15,0.72),
        legend.title = element_blank(),
        ggside.panel.border = element_rect(NA, "black", linewidth = 0.5),
        ggside.axis.text = element_blank(),
        ggside.axis.ticks = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-37 ggside包不同边际组合图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)