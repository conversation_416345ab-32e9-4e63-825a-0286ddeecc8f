"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)


#读取数据
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\line_plot_group.xlsx"
group_line = read_excel(file)
group_line$Week <- as.factor(group_line$Week)

#统计计算

df.summary <- group_line %>%
  group_by(Week,Treatment) %>%
  summarise(
      sd = sd(Tumor_size, na.rm = TRUE),
      mean = mean(Tumor_size),
      .groups = 'drop'
  )	

#或者使用rstatix包中的get_summary_stats()函数进行计算  

group_line_stat <- group_line %>% group_by(Treatment, Week) %>% 
           rstatix::get_summary_stats(type = "mean_sd")

colors <- c("#2E58A4","#B69D71")

												  
####################################图4-2-6（a）ggplot2 分组误差折线图绘制示例

ggplot(data = df.summary,aes(x = Week,y = mean,group=Treatment)) +
  geom_line(aes(color=Treatment),linewidth=1) +
  geom_errorbar(aes(color=Treatment,ymin=mean-sd,ymax=mean+sd),
                linewidth=0.8,width = 0.5)+
  geom_point(aes(fill=Treatment,shape=Treatment),size=5.5,stroke=1) +
  annotate("text",x=7,y=3,label="(a)",size=8,family='serif',
         fontface='bold') +
  scale_color_manual(values = colors) +
  scale_fill_manual(values = colors) +
  scale_shape_manual(values=c(21,22)) +
  guides(fill=guide_legend(override.aes = list(size=3.5))) +
  scale_y_continuous(limits = c(0,30),breaks = seq(0,30,5),expand = c(0,0)) +
  labs(y = "Means Values") +
  theme_bw() +
  theme(legend.position = c(0.2,0.88),
        legend.margin=margin(1,1,-5,1),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-6 分组误差折线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-6 分组误差折线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-6（b）ggplot2 分组误差折线图绘制示例（ggpubr）

ggpubr::ggline(data = group_line,x = "Week",y="Tumor_size",shape = "Treatment",
               color="Treatment",group = "Treatment",add = "mean_sd",
               palette=c("#2E58A4","#B69D71"),point.size=3.5) +
 
 annotate("text",x=7,y=3,label="(a)",size=8,family='serif',
         fontface='bold') +
 scale_y_continuous(limits = c(0,30),breaks = seq(0,30,5),expand = c(0,0)) +
 labs(y = "Means Values") +
 theme_bw() +
 theme(legend.position = c(0.2,0.88),
        legend.margin=margin(1,1,-5,1),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-6 分组误差折线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-6 分组误差折线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
   