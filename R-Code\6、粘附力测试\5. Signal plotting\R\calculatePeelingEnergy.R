# =============================================================================
# 剥离能量计算模块
#
# 功能：计算粘附力测试中的剥离能量
# 对应MATLAB的calculatePeelingEnergy.m函数
#
# 依赖：无外部依赖，纯R基础函数
#
# 主要函数：
# - calculatePeelingEnergy(force, displacement): 计算剥离能量
#   输入：force (N), displacement (mm)
#   输出：剥离能量 (J/m²)
# - trapz(x, y): 梯形积分法（对应MATLAB的trapz函数）
#   输入：x坐标向量，y坐标向量
#   输出：积分值
#
# 计算逻辑：
# 1. 基底面积 = π × (直径/2)² (直径5cm)
# 2. 位移单位转换：mm → m
# 3. 数值积分：∫ F(x) dx (梯形积分法)
# 4. 剥离能量 = 功 / 基底面积 × 1e4 (J/m²)
# =============================================================================

#' 计算剥离能量
#' 
#' @param force 力数据向量 (N)
#' @param displacement 位移数据向量 (mm)
#' @return 剥离能量值 (J/m^2)
calculatePeelingEnergy <- function(force, displacement) {
  
  # 检查输入数据的有效性
  if (any(is.na(force)) || any(is.na(displacement))) {
    stop("Force or displacement data contains NA values")
  }
  
  if (all(force == 0) || all(displacement == 0)) {
    stop("Force or displacement data contains only zeros")
  }
  
  if (length(force) != length(displacement)) {
    stop("Force and displacement vectors must have the same length")
  }
  
  # 计算基底的圆面积，直径为5厘米
  diameter <- 5  # 单位为厘米
  radius <- diameter / 2
  substrateArea <- pi * radius^2  # 基底的面积，单位为平方厘米
  
  # 将位移从 mm 转换为 m
  displacement_m <- displacement * 1e-3  # mm 转换为 m
  
  # 使用梯形积分法计算力-位移曲线下的面积（即剥离能量的数值积分）
  # 对应MATLAB的trapz函数
  workDone <- trapz(displacement_m, force)  # 剥离能量，单位为 N·m
  
  # 将剥离能量除以基底面积，得到单位面积的剥离能量，单位为 N/cm^2
  peelingEnergy_cm2 <- workDone / substrateArea
  
  # 单位转换：从 N/cm^2 转换为 J/m^2 (1 N/cm^2 = 10^4 J/m^2)
  peelingEnergy <- peelingEnergy_cm2 * 1e4
  
  return(peelingEnergy)
}

#' 梯形积分函数
#' 对应MATLAB的trapz函数
#' 
#' @param x x坐标向量
#' @param y y坐标向量
#' @return 积分值
trapz <- function(x, y) {
  if (length(x) != length(y)) {
    stop("x and y must have the same length")
  }
  
  if (length(x) < 2) {
    return(0)
  }
  
  # 梯形积分公式
  n <- length(x)
  integral <- 0
  
  for (i in 1:(n-1)) {
    h <- x[i+1] - x[i]  # 步长
    integral <- integral + h * (y[i] + y[i+1]) / 2  # 梯形面积
  }
  
  return(integral)
}
