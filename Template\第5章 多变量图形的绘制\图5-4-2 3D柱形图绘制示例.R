"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library(plot3D)

# X,Y and Z values
x = c(1,2,3,4,5)
y = c(1,2,3,4,5)

zval = c(15, 10, 8, 11, 10,
         5,  6,  14, 6,  24,
         15, 15, 9,  9,  14,
         8,  14, 5,  6,  19,
         21, 11, 11, 3,  14 )
# Convert Z values into a matrix.
z = matrix(zval, nrow=5, ncol=5, byrow=TRUE)

####################################图5-4-2（a）单一颜色3D柱形图绘制示例

png(file="\\第5章 多变量图形的绘制\\图5-4-2 3D柱形图绘制示例_a.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-2 3D柱形图绘制示例_a.pdf",
    width = 6, height = 5,family = "serif")	
	
par(family = "serif",mar = rep(2, 4))
hist3D(x,y,z, bty = "b2",col = "#2796EC", border = "black",cex.axis = 1.2,cex.lab = 1.5,
       shade = 0, ltheta = 90,phi = 20,space = 0.4, ticktype = "detailed", d = 2)
dev.off()

####################################图5-4-2（b））渐变色3D柱形图绘制示例

color <- parula(100)

png(file="\\第5章 多变量图形的绘制\\图5-4-2 3D柱形图绘制示例_b.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-2 3D柱形图绘制示例_b.pdf",
    width = 6, height = 5,family = "serif")	
par(family = "serif",mar = rep(2, 4))
hist3D(x,y,z, bty = "b2", border = "black",col = color,cex.axis = 1.2,cex.lab = 1.5,
       shade = 0, ltheta = 90,phi = 20,space = 0.4, ticktype = "detailed", d = 2,
       colkey = list(length = 0.6))
dev.off()