function calculate_MVTR(csv_file_path, radius_mm, output_file)
% CALCULATE_MVTR 计算材料的水蒸气透过率 (MVTR)
%
% 输入参数:
%   csv_file_path - CSV文件路径，包含材料数据
%   radius_mm     - 测试样品的半径 (毫米)，默认为14mm
%   output_file   - 输出结果文件路径 (可选)
%
% CSV文件格式:
%   第1列: 材料名称
%   第2列: 初始重量 (g)
%   第3列: 实验时间 (天)
%   第4列: 结束重量 (g)
%
% 输出:
%   MVTR值，单位为 kg/(m²·24h)
%
% 示例:
%   calculate_MVTR('test_data.csv', 14)
%   calculate_MVTR('test_data.csv', 14, 'results.csv')

% 设置默认参数
if nargin < 2
    radius_mm = 14; % 默认半径14mm
end

if nargin < 3
    output_file = '';
end

% 检查输入文件是否存在
if ~exist(csv_file_path, 'file')
    error('CSV文件不存在: %s', csv_file_path);
end

try
    % 读取CSV文件
    fprintf('正在读取CSV文件: %s\n', csv_file_path);
    data = readtable(csv_file_path, 'Encoding', 'UTF-8');
    
    % 检查数据列数
    if width(data) < 4
        error('CSV文件必须包含至少4列数据');
    end
    
    % 获取列名（如果没有表头，使用默认名称）
    if isnumeric(data{1,1})
        % 没有表头，添加默认列名
        data.Properties.VariableNames = {'Material', 'Initial_Weight_g', 'Time_days', 'Final_Weight_g'};
    end
    
    % 提取数据
    materials = data{:,1};
    initial_weights = data{:,2};
    time_days = data{:,3};
    final_weights = data{:,4};
    
    % 转换为数值（如果需要）
    if iscell(initial_weights)
        initial_weights = cell2mat(initial_weights);
    end
    if iscell(time_days)
        time_days = cell2mat(time_days);
    end
    if iscell(final_weights)
        final_weights = cell2mat(final_weights);
    end
    
    % 计算有效透湿面积 (m²)
    radius_m = radius_mm / 1000; % 转换为米
    area_m2 = pi * radius_m^2;
    
    fprintf('测试参数:\n');
    fprintf('  样品半径: %.1f mm\n', radius_mm);
    fprintf('  有效面积: %.6f m²\n', area_m2);
    fprintf('  样品数量: %d\n', length(materials));
    fprintf('\n');
    
    % 初始化结果数组
    num_samples = length(materials);
    mvtr_results = zeros(num_samples, 1);
    weight_loss = zeros(num_samples, 1);
    
    % 计算每个材料的MVTR
    fprintf('计算结果:\n');
    fprintf('%-20s %-12s %-12s %-12s %-15s\n', '材料', '重量损失(g)', '时间(天)', 'MVTR(g/m²/24h)', 'MVTR(kg/m²/24h)');
    fprintf('%s\n', repmat('-', 1, 80));
    
    for i = 1:num_samples
        % 计算重量变化（绝对值）
        weight_change = abs(final_weights(i) - initial_weights(i));
        weight_loss(i) = weight_change;
        
        % 计算MVTR (kg/(m²·24h))
        mvtr_g = weight_change / (area_m2 * time_days(i)); % g/(m²·24h)
        mvtr_kg = mvtr_g / 1000; % kg/(m²·24h)
        mvtr_results(i) = mvtr_kg;
        
        % 显示结果
        material_name = materials{i};
        if isnumeric(material_name)
            material_name = sprintf('Material_%d', material_name);
        end
        
        fprintf('%-20s %-12.3f %-12.1f %-12.1f %-15.6f\n', ...
            material_name, weight_change, time_days(i), mvtr_g, mvtr_kg);
    end
    
    % 创建结果表格
    if iscell(materials)
        result_materials = materials;
    else
        result_materials = cellstr(string(materials));
    end
    
    results_table = table(result_materials, initial_weights, final_weights, ...
                         time_days, weight_loss, mvtr_results, ...
                         'VariableNames', {'Material', 'Initial_Weight_g', ...
                         'Final_Weight_g', 'Time_days', 'Weight_Loss_g', 'MVTR_kg_m2_24h'});
    
    % 显示统计信息
    fprintf('\n统计信息:\n');
    fprintf('  平均MVTR: %.6f kg/(m²·24h)\n', mean(mvtr_results));
    fprintf('  最大MVTR: %.6f kg/(m²·24h)\n', max(mvtr_results));
    fprintf('  最小MVTR: %.6f kg/(m²·24h)\n', min(mvtr_results));
    fprintf('  标准差:   %.6f kg/(m²·24h)\n', std(mvtr_results));
    
    % 保存结果到文件
    if ~isempty(output_file)
        writetable(results_table, output_file, 'Encoding', 'UTF-8');
        fprintf('\n结果已保存到: %s\n', output_file);
    end
    
    % 在工作空间中保存结果
    assignin('base', 'mvtr_results', results_table);
    fprintf('\n结果已保存到工作空间变量: mvtr_results\n');
    
catch ME
    fprintf('错误: %s\n', ME.message);
    rethrow(ME);
end

end
