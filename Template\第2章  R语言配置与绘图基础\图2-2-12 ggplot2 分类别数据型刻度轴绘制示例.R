"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

library(readxl)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\scale_data.csv"
scale_data <- readr::read_csv(data_file)


###############################################图2-2-12（a）分类刻度柱形图示例

ggplot(data = diamonds,aes(x=cut)) +
  geom_bar(width = .75,fill="gray") +
  labs(x="Class", y = "Values") +
  geom_hline(yintercept = 1, color = "gray40",size=.8)+
  theme(plot.background = element_rect(fill = "white"),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank(),
        panel.grid.major.y = element_line(linetype = "dashed",colour = "gray60"),
        text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        axis.ticks = element_blank())
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-12 ggplot2 分类别数据型刻度轴绘制示例_a.png",
       width = 5., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-12 ggplot2 分类别数据型刻度轴绘制示例_a.pdf",
       width = 5., height = 4,device = cairo_pdf)

###############################################图2-2-12（b）分类刻度箱线图示例

ggplot(data = diamonds,aes(x=cut,y = x)) +
  geom_boxplot(fill="gray") +
  labs(x="Class", y = "Values") +
  geom_hline(yintercept = 0, color = "gray40",size=.8)+
  theme(plot.background = element_rect(fill = "white"),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank(),
        panel.grid.major.y = element_line(linetype = "dashed",colour = "gray60"),
        text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        axis.ticks = element_blank())
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-12 ggplot2 分类别数据型刻度轴绘制示例_b.png",
       width = 5., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-12 ggplot2 分类别数据型刻度轴绘制示例_b.pdf",
       width = 5., height = 4,device = cairo_pdf)
	   
	   
