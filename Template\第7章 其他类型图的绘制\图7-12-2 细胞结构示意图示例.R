
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(gganatogram)
library(viridis)
library(gridExtra)

ppng(file="\\第7章 其他类型图的绘制\\图7-12-2 细胞结构示意图示例.png",
    width = 8000, height = 4000,res=1000)
cell01 <- gganatogram(data=cell_key[['cell']], outline = T, fillOutline='steelblue', 
            organism="cell", fill="colour")  +theme_void() + coord_fixed()
cell02 <- gganatogram(data=cell_key[['cell']], outline = T, fillOutline='lightgray', organism="cell", fill="value")  + 
        theme_void() +  
        coord_fixed() +  
        scale_fill_viridis()
grid.arrange(cell01, cell02,ncol=2)
dev.off()