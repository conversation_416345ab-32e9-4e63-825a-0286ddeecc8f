"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据集
group_bar <- read_excel("\\第4章 双变量图形的绘制\\分组误差柱形图数据.xlsx")
#数据处理
group_bar_stat <- group_bar %>% group_by(order,class) %>%  #注意：两个分组依据
           rstatix::get_summary_stats(type = "common")
#改变绘图属性顺序
group_bar_stat$order <- factor(group_bar_stat$order, 
                        levels=c("one","two","three"))									

####################################图4-1-26（a）使用ggplot2绘制的分组标准差误差柱形图
#SD
ggplot(data = group_bar_stat,aes(x = order,y = mean,fill=class)) +
  geom_bar(stat="identity",width=0.65,
           colour="black",position=position_dodge(),linewidth=0.2) +
  geom_errorbar(aes(ymin=mean-sd, ymax=mean+sd),width=0.2,linewidth=0.3,
               position=position_dodge(0.6)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,35),
                     breaks = seq(0,35,5)) +
  ggsci::scale_fill_jco() +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-26（）使用ggplot2绘制的分组置信区间误差柱形图
#CI
ggplot(data = group_bar_stat,aes(x = order,y = mean,fill=class)) +
  geom_bar(stat="identity",width=0.65,
           colour="black",position=position_dodge(),linewidth=0.2) +
  geom_errorbar(aes(ymin=mean-ci, ymax=mean+ci),width=0.2,linewidth=0.3,
               position=position_dodge(0.6)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,30),
                     breaks = seq(0,30,5)) +
  ggsci::scale_fill_jco() +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-26（）使用ggpubr绘制的分组标准差误差柱形图

#SD
ggbarplot(data = group_bar,x="order",y="value",fill="class",add="mean_sd",
            position = position_dodge(0.7),palette="aaas",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.2,size=0.3),
            ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,35),
                     breaks = seq(0,35,5)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-26（d）使用ggpubr绘制的分组置信区间误差柱形图

#CI
ggbarplot(data = group_bar,x="order",y="value",fill="class",add="mean_ci",
            position = position_dodge(0.7),palette="aaas",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.2,size=0.3),
            ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,35),
                     breaks = seq(0,35,5)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-26 不同方法绘制的分组误差柱形图示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)