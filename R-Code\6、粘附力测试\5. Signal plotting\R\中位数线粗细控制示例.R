# ==========================================
# 文件名：中位数线粗细控制示例.R
# 主要功能：展示如何控制箱线图中位数线的粗细
# 依赖文件：plotBoxPlot.R
# 创建日期：2025-01-01
# ==========================================

# 加载改进后的箱线图函数
source("plotBoxPlot.R")

# 示例数据
group1_data <- c(20.3319, 25.7963, 21.8242, 19.5, 23.1, 22.8)
group2_data <- c(15.2, 18.7, 16.9, 17.3, 19.1, 14.8)

# ==========================================
# 箱线图各部分说明
# ==========================================
cat("箱线图各部分含义：\n")
cat("1. 中间横线 = 中位数（median）\n")
cat("2. 箱子下边界 = 第一四分位数（Q1，25%）\n")
cat("3. 箱子上边界 = 第三四分位数（Q3，75%）\n")
cat("4. 上下须线 = 数据范围（1.5倍IQR范围内）\n")
cat("5. 圆点 = 异常值（outliers）\n\n")

# ==========================================
# 示例1：细线条（line_width = 0.3）
# ==========================================
p1 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  line_width = 0.3  # 细线条
)

print("示例1：细线条（line_width = 0.3）")
print(p1)

# ==========================================
# 示例2：标准线条（line_width = 0.5，默认）
# ==========================================
p2 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  line_width = 0.5  # 标准线条
)

print("示例2：标准线条（line_width = 0.5，默认）")
print(p2)

# ==========================================
# 示例3：粗线条（line_width = 0.8）
# ==========================================
p3 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  line_width = 0.8  # 粗线条
)

print("示例3：粗线条（line_width = 0.8）")
print(p3)

# ==========================================
# 示例4：很粗线条（line_width = 1.2）
# ==========================================
p4 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  line_width = 1.2  # 很粗线条
)

print("示例4：很粗线条（line_width = 1.2）")
print(p4)

# ==========================================
# 示例5：使用专用函数控制线条粗细
# ==========================================
p5 <- plotPeelingEnergyBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  show_points = FALSE,
  show_mean = TRUE,
  line_width = 0.7  # 自定义线条粗细
)

print("示例5：专用函数控制线条粗细")
print(p5)

# ==========================================
# 线条粗细建议
# ==========================================
cat("\n线条粗细建议：\n")
cat("• 0.3 - 很细，适合密集图表\n")
cat("• 0.5 - 标准粗细（默认），适合大多数情况\n")
cat("• 0.7 - 稍粗，适合演示或打印\n")
cat("• 1.0 - 粗线条，适合海报或大尺寸图表\n")
cat("• 1.2+ - 很粗，适合特殊强调\n\n")

# ==========================================
# 保存不同粗细的图片示例
# ==========================================
# 取消注释以下代码来保存图片
# ggsave("boxplot_thin_lines.png", p1, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_standard_lines.png", p2, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_thick_lines.png", p3, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_very_thick_lines.png", p4, width = 6, height = 4, dpi = 300, bg = "white")

print("所有线条粗细示例已完成！")
