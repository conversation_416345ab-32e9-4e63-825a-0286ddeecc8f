"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

map_fig02_type <- map_fig02 %>% dplyr::mutate(fill_type=case_when(
     stringr::str_detect(country,"JACK|JAY") ~ "Typology 1",
     stringr::str_detect(country,"EELIN|RON") ~ "Typology 2",
     TRUE ~ "Typology 3"
))


####################################图6-6-1（a）类型地图绘制样式

ggplot() +
  geom_sf(data = map_fig02_type,aes(fill=fill_type),colour="black",linewidth=0.35) +
  scale_fill_manual(values = c("#458B74","#CDCD00","#F5DEB3")) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",fill="Typology Type")+
  theme_classic() +
  theme(legend.position = c(0.95,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.size = unit(0.5, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 22, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-6-1（b）类型地图添加注释文本绘制样式

ggplot(data = map_fig02_type) +
  geom_sf(aes(fill=fill_type),colour="black",linewidth=0.35) +
  ggrepel::geom_text_repel(aes(label=country,geometry = geometry),stat = "sf_coordinates",
                            size=3.5,family="serif") +
  scale_fill_manual(values = c("#458B74","#CDCD00","#F5DEB3")) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",fill="Typology Type")+
  theme_classic() +
  theme(legend.position = c(0.95,0.2),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.size = unit(0.5, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 22, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-6-1（c）类型地图添加注释文本绘制样式
ggplot(data = map_fig02_type) +
  geom_sf(aes(fill=fill_type),colour="black",linewidth=0.35) +
  ggrepel::geom_text_repel(aes(label=country,geometry = geometry),stat = "sf_coordinates",
                            size=3.5,family="serif",bg.colour = "white", bg.r = .2) +
  scale_fill_manual(values = c("#458B74","#CDCD00","#F5DEB3")) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",fill="Typology Type")+
  theme_classic() +
  theme(legend.position = c(0.95,0.2),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.size = unit(0.5, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 22, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图6-6-1（d）类型地图添加注释文本绘制样式	  

library(ggpattern)

ggplot(data = map_fig02_type,) +
  geom_sf(aes(fill=fill_type),colour="black",linewidth=0.35) +
  ggpattern::geom_sf_pattern(aes(pattern_type = fill_type,fill=fill_type),
                              pattern    = 'magick',
                              pattern_fill  = 'black',
                              pattern_aspect_ratio = 1.75,
                             ) +
  scale_fill_manual(values = c("#458B74","#CDCD00","#F5DEB3")) +
  scale_pattern_type_discrete(choices = gridpattern::names_magick) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",fill="Typology Type",
       pattern_type="Typology Type")+
  theme_classic() +
  theme(legend.position = c(0.95,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.size = unit(0.5, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 22, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-6-1 类型地图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)


 
	   