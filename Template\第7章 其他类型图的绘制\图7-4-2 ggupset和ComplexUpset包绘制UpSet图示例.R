
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)
library(ggupset)

####################################图7-4-2（a）ggupset包绘制UpSet图示例

pl <- tidy_movies %>%
  distinct(title, year, length, .keep_all=TRUE) %>%
  ggplot(aes(x=Genres)) +
    geom_bar() +
    geom_text(stat='count', aes(label=after_stat(count)), vjust=-1) +
    scale_x_upset(order_by = "degree", n_sets = 5) +
    theme_classic2()
pl
ggsave(pl,filename = "\\第7章 其他类型图的绘制\\图7-4-2 ggupset和ComplexUpset包绘制UpSet图示例_a.png",
       width =12, height = 6, bg="white",dpi = 900,device=png)
ggsave(pl,filename = "\\第7章 其他类型图的绘制\\图7-4-2 ggupset和ComplexUpset包绘制UpSet图示例_a.pdf",
       width =12, height = 6,device = cairo_pdf)
	   
####################################图7-4-2（b）ComplexUpset包绘制UpSet图示例	   
library(ComplexUpset)

#数据处理
movies = as.data.frame(ggplot2movies::movies)
genres = colnames(movies)[18:24]
movies[genres] = movies[genres] == 1 
movies[movies$mpaa == '', 'mpaa'] = NA
movies = na.omit(movies) 

png(file="\\第7章 其他类型图的绘制\\图7-4-2 ggupset和ComplexUpset包绘制UpSet图示例_b.png",
    width = 12000, height = 6000,res=1200)
ComplexUpset::upset(movies, genres, name='genre', width_ratio=0.1)
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-4-2 ggupset和ComplexUpset包绘制UpSet图示例_b.pdf",
    width = 12, height = 6)
ComplexUpset::upset(movies, genres, name='genre', width_ratio=0.1)
dev.off()
