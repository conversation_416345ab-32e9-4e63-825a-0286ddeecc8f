# 《科研论文配图绘制指南--基于R语言》Git仓库忽略文件

# ===== 数据文件 =====
# CSV数据文件
*.csv

# Excel数据文件
*.xlsx
*.xls

# R数据文件
*.rds
*.RData
*.rda

# ===== 输出图像文件 =====
# PDF输出文件
*.pdf

# PNG图像文件
*.png

# SVG矢量图文件（保留Template/数据可视化图标/中的SVG图标）
*.svg


# ===== 压缩包和安装文件 =====
# Zip压缩包
*.zip

# ===== 字体和样式文件 =====
# 字体文件
*.ttf
*.otf
*.woff
*.woff2
*.eot

# CSS样式表文件
*.css

# ===== 中间文件和临时文件 =====
# R生成的临时文件
.Rhistory
.RData
.Ruserdata
.Rapp.history

# RStudio文件
.Rproj.user/
*.Rproj

# 临时文件
*~
*.tmp
*.temp

# ===== 系统文件 =====
# Windows系统文件
Thumbs.db
Desktop.ini

# macOS系统文件
.DS_Store
.AppleDouble
.LSOverride

# Linux系统文件
*~

# ===== 说明文档（保留重要说明文件） =====
# 保留重要说明文件
!Template/重要，必看.txt

# ===== 其他需要保留的文件 =====
# 保留所有R代码文件
!*.R

# 保留README和说明文档
!*.md
!*.txt

# 保留项目根目录的必要文件
!.gitignore
!README.md
