"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
library(readxl)
denity_data <- read_excel("\\第4章 双变量图形的绘制\\相关性散点密度图.xlsx")
num <- nrow(denity_data)
rmse = sqrt(mean((denity_data$A - denity_data$B)^2))
											  
####################################图4-2-16（a）使用ggpointdensity 绘制的散点密度图示例1

ggplot(data = denity_data,aes(x = A,y = B)) +
  #geom_point(size=1) +
  ggpointdensity::geom_pointdensity(size = 0.8)+ 
  geom_smooth(method = "lm", colour="red",se=FALSE, formula = y ~ x,linewidth=.8) +
  #绘制对角线:最佳拟合线
  #geom_abline(aes(intercept=0, slope=1),alpha=1, linewidth=.8) + 
  ggpubr::stat_regline_equation(label.x = -4.5,label.y = 8.5,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(after_stat(rr.label), after_stat(p.label), sep = "~`,`~")),
                   label.x = -4.5, label.y = 7,size=6,
                   r.accuracy = 0.001,p.accuracy = 0.001,
                   family='serif',fontface='bold') +
  annotate("text",x=-4.5,y=5,label=paste("RMSE = ",round(rmse,3)),size=6,
            family='serif',fontface="italic",hjust = 0) +
  #修改坐标轴刻度
  scale_x_continuous(limits = c(-5,5),breaks = seq(-5,5,1),expand = c(0,0)) +
  scale_y_continuous(limits = c(-10,10),breaks = seq(-10,10,2.5),expand = c(0,0)) +
  labs(x="A Values", y = "B Values") +
  scale_color_gradientn(colours = parula(100))+
  theme_bw() +
  theme(legend.position = "inside",
        legend.position.inside = c(.85,.25),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-16 使用ggpointdensity 包绘制的散点密度图示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-16 使用ggpointdensity 包绘制的散点密度图示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
	   
####################################图4-2-16（b）使用ggpointdensity 绘制的散点密度图示例2

ggplot(data = denity_data,aes(x = A,y = B)) +
  ggpointdensity::geom_pointdensity(method = "kde2d",size = 0.8)+ 
  geom_smooth(method = "lm", colour="red",se=FALSE, formula = y ~ x,linewidth=.8) +
  ggpubr::stat_regline_equation(label.x = -4.5,label.y = 8.5,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(after_stat(rr.label), after_stat(p.label), sep = "~`,`~")),
                   label.x = -4.5, label.y = 7,size=6,
                   r.accuracy = 0.001,p.accuracy = 0.001,
                   family='serif',fontface='bold') +
  annotate("text",x=-4.5,y=5,label=paste("RMSE = ",round(rmse,3)),size=6,
            family='serif',fontface="italic",hjust = 0) +
  #修改坐标轴刻度
  scale_x_continuous(limits = c(-5,5),breaks = seq(-5,5,1),expand = c(0,0)) +
  scale_y_continuous(limits = c(-10,10),breaks = seq(-10,10,2.5),expand = c(0,0)) +
  labs(x="A Values", y = "B Values") +
  scale_color_gradientn(colours = parula(100))+
  theme_bw() +
  theme(legend.position = c(.85,.25),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-16 使用ggpointdensity 包绘制的散点密度图示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-16 使用ggpointdensity 包绘制的散点密度图示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)