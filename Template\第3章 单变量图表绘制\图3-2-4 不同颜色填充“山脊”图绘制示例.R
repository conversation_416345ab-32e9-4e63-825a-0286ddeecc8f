"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggridges)


#读取数据

group_data <- readr::read_csv("\\第3章 单变量图表绘制\\山脊图数据.csv")

####################################图3-2-4（a）不同颜色填充“山脊”图示例1
ggplot(group_data, aes(x = depth, y = color)) +
  geom_density_ridges(fill="#B93F2B",size=.8) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  #scale_fill_manual(values = parula(7)) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_a.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_a.pdf",
       width =5, height = 5,device = cairo_pdf)


####################################图3-2-4（b）不同颜色填充“山脊”图示例2
library(pals)
ggplot(group_data, aes(x = depth, y = color,fill=color)) +
  geom_density_ridges(size=.8) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  scale_fill_manual(values = parula(7)) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_b.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_b.pdf",
       width =5, height = 5,device = cairo_pdf)
	   
####################################图3-2-4（c）不同颜色填充“山脊”图示例3
library(RColorBrewer)
Colormap<- colorRampPalette(rev(brewer.pal(11,'Spectral')))(32)

ggplot(group_data, aes(x = depth, y = color,fill=after_stat(x))) +
  geom_density_ridges_gradient(size=0.8) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  scale_fill_gradientn(name="Values",colours = Colormap) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_c.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_c.pdf",
       width =5, height = 5,device = cairo_pdf)

####################################图3-2-4（d）不同颜色填充“山脊”图示例4
ggplot(group_data, aes(x = depth, y = color,fill=after_stat(density))) +
  geom_density_ridges_gradient(size=0.8) +
  scale_y_discrete(expand = c(0.01, 0)) +
  scale_x_continuous(expand = c(0.01, 0)) +
  scale_fill_gradientn(name="Values",colours = Colormap) +
  theme_minimal(base_family = "serif",base_size = 17) +
  theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_d.png",
       width = 5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-4 不同颜色填充“山脊”图绘制示例_d.pdf",
       width =5, height = 5,device = cairo_pdf)


   