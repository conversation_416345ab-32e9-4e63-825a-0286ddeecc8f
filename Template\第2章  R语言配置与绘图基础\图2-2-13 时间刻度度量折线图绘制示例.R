"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)



###############################################图2-2-13（a）笛卡儿坐标系时间刻度度量示例

ggplot(economics, aes(date, psavert)) + 
  geom_line(na.rm = TRUE) +
  geom_hline(yintercept = 0, color = "gray40",size=.8)+
  labs(x = "Years", y = "Values") +
  theme(plot.background = element_rect(fill = "white"),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank(),
        panel.grid.major.y = element_line(linetype = "dashed",colour = "gray60"),
        text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        axis.ticks = element_blank())
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-13 时间刻度度量折线图绘制示例_a.png",
       width = 5., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-13 时间刻度度量折线图绘制示例_a.pdf",
       width = 5., height = 4,device = cairo_pdf)

###############################################图2-2-13（b）极坐标系时间刻度度量示例

ggplot(economics, aes(date, psavert)) + 
  geom_line(na.rm = TRUE) +
  labs(x = "Years", y = "Values") +
  geomtextpath::coord_curvedpolar()+ 
  theme(plot.background = element_rect(fill = "white"),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major.x = element_blank(),
        panel.grid.minor.y = element_blank(),
        panel.grid.major.y = element_line(linetype = "dashed",colour = "gray60"),
        text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        axis.ticks = element_blank())
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-13 时间刻度度量折线图绘制示例_b.png",
       width = 5., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-13 时间刻度度量折线图绘制示例_b.pdf",
       width = 5., height = 4,device = cairo_pdf)

