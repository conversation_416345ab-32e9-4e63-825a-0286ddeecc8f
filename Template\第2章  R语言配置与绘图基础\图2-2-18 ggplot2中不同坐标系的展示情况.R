"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
se <- function(x) sqrt(var(x)/length(x)) 
# set seed for reproducible results
set.seed(9876) 
# create toy data
df <- data.frame(variable = as.factor(1:10),
                 value = sample(10, replace = TRUE))


###############################################图2-2-18（a）直角坐标系下的柱形图

ggplot(df, aes(variable, value, fill = variable)) +
  geom_bar(width = 1, stat = "identity", color = "black") +
  ggsci::scale_fill_jco()+
  hrbrthemes::theme_ipsum_pub(base_family = "serif",base_size = 12,
                              plot_margin = margin(10, 10, 10, 10),
                              axis_title_size = 15)
  
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-18 ggplot2中不同坐标系的展示情况_a.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-18 ggplot2中不同坐标系的展示情况_a.pdf",
       width = 4.2, height = 4,device = cairo_pdf)

###############################################图2-2-18（b）极坐标系下的柱形图

ggplot(df, aes(variable, value, fill = variable)) +
  geom_bar(width = 1, stat = "identity", color = "black") +
  ggsci::scale_fill_jco()+
  coord_polar() +
  hrbrthemes::theme_ipsum_pub(base_family = "serif",base_size = 12,
                              plot_margin = margin(10, 10, 10, 10),
                              axis_title_size = 15)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-18 ggplot2中不同坐标系的展示情况_b.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-18 ggplot2中不同坐标系的展示情况_b.pdf",
       width = 4.2, height = 4,device = cairo_pdf)


