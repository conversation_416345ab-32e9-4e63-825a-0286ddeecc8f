
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)


#读取数据
Bland <- read_xlsx("\\第7章 其他类型图的绘制\\Bland_Altman_data.xlsx")
#计算测量值的差异
#创建测量差异新列
Bland$avg <- rowMeans(Bland) 
#create new column for difference in measurements
Bland$diff <- Bland$A - Bland$B

#计算平均差和置信区间
#find average difference
mean_diff <- mean(Bland$diff)
#find lower 95% confidence interval limits
lower <- mean_diff - 1.96*sd(Bland$diff)
#find upper 95% confidence interval limits
upper <- mean_diff + 1.96*sd(Bland$diff)

upper_la <- paste0("+1.96SD\n",round(upper,2))
lower_la <- paste0(round(lower,2),"\n-1.96SD")
mean_la <- paste0("Mean\n",round(mean_diff,2))		 	 
  

####################################图7-1-1（a）使用ggplot2绘制的Bland-Altman图样式

ggplot(Bland, aes(x = avg, y = diff)) +
  geom_point(shape=21,size=4,fill="red") +
  geom_hline(yintercept = 0,color="blue",linewidth=0.8) +
  geom_hline(yintercept = mean_diff,linewidth=0.8) +
  geom_hline(yintercept = lower, color = "red", linetype="dashed") +
  geom_hline(yintercept = upper, color = "red", linetype="dashed") +
  annotate("text",x=1000,y=lower,label=lower_la,size=5,hjust = 0.8) +
  annotate("text",x=1000,y=upper,label=upper_la,size=5,hjust = 0.8) +
  annotate("text",x=1000,y=mean_diff,label=mean_la,size=5,hjust = 0.8) +
  #ggtitle("Bland-Altman Plot") +
  labs(x="Average",y="Difference") +
  scale_x_continuous(limits = c(0,1000), breaks = seq(0, 1000, 200),
                     guide = "prism_offset") +
  scale_y_continuous(limits = c(-125,60), breaks = seq(-125, 60, 25),
                     guide = "prism_offset") +
  ggprism::theme_prism(base_size = 16)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-1-1 Bland-Altman图绘制示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-1-1 Bland-Altman图绘制示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)


####################################图7-1-1（b）使用blandr绘制的Bland-Altman图样式

library(blandr)

Bland <- read_xlsx("\\第7章 其他类型图的绘制\\Bland_Altman_data.xlsx")


bland_plot <- blandr.draw(Bland$A,Bland$B,plotTitle = "")

bland_plot + 
  annotate("text",x=1000,y=lower,label=lower_la,size=5,hjust = 0.8) +
  annotate("text",x=1000,y=upper,label=upper_la,size=5,hjust = 0.8) +
  annotate("text",x=1000,y=mean_diff,label=mean_la,size=5,hjust = 0.8) +
  scale_x_continuous(limits = c(0,1000), breaks = seq(0, 1000, 200),
                     guide = "prism_offset") +
  scale_y_continuous(limits = c(-125,65), breaks = seq(-125, 65, 25),
                     guide = "prism_offset") +
  ggprism::theme_prism(base_size = 16)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-1-1 Bland-Altman图绘制示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-1-1 Bland-Altman图绘制示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)


