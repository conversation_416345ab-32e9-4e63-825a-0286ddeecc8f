"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(rstatix)


#读取数据
heatmap_data <- read_excel("\\第4章 双变量图形的绘制\\相关性热力图_P值.xlsx")

cor.mat <- heatmap_data %>% rstatix::cor_mat()
cor_df <- cor.mat %>% cor_gather()

cor_df <-  cor_df %>% mutate(p_sym = case_when((p <= 0.001) ~ "***",
                                     (p > 0.001 & p <= 0.01) ~ "**",
                                     (p > 0.01 & p <= 0.05) ~ "*",
                                     (p > 0.05) ~ "",
                                     FALSE ~ as.character(p)))
				  
####################################图4-2-22（a）相关性矩阵热力图P值绘制示例1

ggplot(data = cor_df,aes(x = var1, y=var2, fill=cor)) +
  geom_tile(colour="black",linewidth=.2) +
  #添加显著性水平数值
  geom_text(aes(label=p_sym),size=4,fontface="bold",
            position=position_nudge(y=-0.1),family = "serif") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_a.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_a.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-22（b）相关性矩阵热力图P值绘制示例2

ggplot(data = cor_df,aes(x = var1, y=var2, fill=cor)) +
  geom_tile(colour="black",size=.2) +
  geom_text(aes(label=cor),size=3,fontface="bold",
            position=position_nudge(y=0.2),family = "serif") +
  #添加显著性水平数值
  geom_text(aes(label=p_sym),size=4,fontface="bold",
            position=position_nudge(y=-0.2),family = "serif") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_b.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_b.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-22（c）相关性矩阵热力图P值绘制示例3（lapaz色系）
library(scico)

ggplot(data = cor_df,aes(x = var1, y=var2, fill=cor)) +
  geom_tile(colour="black",size=.2) +
  geom_text(aes(label=cor),size=3,fontface="bold",
            position=position_nudge(y=0.2),family = "serif") +
  #添加显著性水平数值
  geom_text(aes(label=p_sym),size=4,fontface="bold",
            position=position_nudge(y=-0.2),family = "serif") +
  labs(x="", y = "") +
  scico::scale_fill_scico(palette = "lapaz",na.value="NA") +


  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_c.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_c.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

####################################图4-2-22（d）相关性矩阵热力图P值绘制示例4（roma色系）

library(scico)

ggplot(data = cor_df,aes(x = var1, y=var2, fill=cor)) +
  geom_tile(colour="black",size=.2) +
  geom_text(aes(label=cor),size=3,fontface="bold",
            position=position_nudge(y=0.2),family = "serif") +
  #添加显著性水平数值
  geom_text(aes(label=p_sym),size=4,fontface="bold",
            position=position_nudge(y=-0.2),family = "serif") +
  labs(x="", y = "") +
  scico::scale_fill_scico(palette = "roma",na.value="NA") +


  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_d.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-22 相关性矩阵热力图P值绘制示例_d.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

