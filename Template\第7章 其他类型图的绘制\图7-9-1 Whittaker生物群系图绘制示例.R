
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(viridis)

library(plotbiomes)

Whittaker_point <- readr::read_csv("\\第7章 其他类型图的绘制\\Whittaker_point.csv")

ggplot() +
  # add biome polygons
  geom_polygon(data = Whittaker_biomes,
               aes(x = temp_c,y = precp_cm,fill = biome),colour = "gray98",
               linewidth   = 1.2) +
  geom_point(data = Whittaker_point, aes(x = temperature, y = precipitation), 
             size = 4,color="white") +
  geom_point(data = Whittaker_point, aes(x = temperature, y = precipitation,color=Elevation), 
             size = 2.5) +
  labs(x="Annual Temperature(℃)",y="Annual Precipitation(cm)",
       color="Elevation\n(point color)") +
  scale_color_viridis(option="magma",breaks=seq(0,3000,500),
                        labels=paste(seq(0,3000,500),"m"))  +
  scale_fill_manual(name = "Whittaker biomes",
                    breaks = names(Ricklefs_colors),
                    labels = names(Ricklefs_colors),
                    values = Ricklefs_colors) +
  guides(color = guide_colorbar(order=2),
         fill = guide_legend(order=1)) +
  theme_bw() +
  theme(legend.position = c(0.3,0.6),legend.background = element_blank(),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black",size = 16),
        axis.title = element_text(colour = "black",face = "bold",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-9-1 Whittaker生物群系图绘制示例.png",
       width =6.5, height = 7, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-9-1 Whittaker生物群系图绘制示例.pdf",
       width =6.5, height = 7,device = cairo_pdf)