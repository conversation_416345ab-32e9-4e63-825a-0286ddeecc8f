此文件夹中包含《科研论文配图绘制指南--基于R语言》一书的配套练习数据和代码！！！

针对购买正版书籍的读者朋友在运行本本书代码之前请先阅读以下内容：

✅：书籍配套代码文件(.R文件)，为使用较新版本的R语言(4.4.0)、tidyverse(2.0.0)以及绘图工具ggplot2(3.5.1)

✅：之所以是和书籍前言中介绍的工具使用版本不同，是因为我在调试代码过程中使用最新版，可避免代码滞后性等问题。

✅：书籍中提供的代码为绘图核心步骤代码，和最新、完整代码在行数、介绍信息等方面有所精简(书籍篇幅等限制)，大家以此套代码为主即可。

✅：提供的.R文件代码可直接在RStudio中进行运行，不熟悉该工具的同学可自行学习相关资料。

✅：本书针对针对R语言绘图0基础的同学，可能存在一定门槛，可加入本书的学习圈子(书籍扉面直接微信扫码)。

✅：针对想快速学习R语言绘图的同学，可加入本书作者创建的书籍学习圈子，圈子中包括：
 
   🔷书籍配套讲解视频(原作者讲解)
   
   🔷更为详细便捷的Jupyter Notebook代码文件
   
   🔷更多R语言科研绘图技巧和增强版书籍代码
   
 🧡🧡🧡添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容🧡🧡🧡🧡


✅：书籍中代码已完成调试，但不可避免的还会出现勘误，读者可根据书籍封面提示加入读者交流群进行反馈，作者会统一进行回复🧡

✅：大家可以关注作者视频号DataCharm,定制直播答疑书籍中出现的问题和介绍一些优秀的可视化技巧及学习资源🔷









