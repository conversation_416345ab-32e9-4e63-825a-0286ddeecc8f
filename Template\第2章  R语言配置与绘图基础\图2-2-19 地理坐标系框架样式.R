"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)

#获取绘图数据
projections <- c("+proj=eqc",
                 "+proj=eqearth",
                 "+proj=ortho",
                 "+proj=aea +lat_1=29.5 +lat_2=42.5",
                 "+proj=laea", 
                 "+proj=igh",
                 "+proj=sinu",
                 "+proj=larr",
                 "+proj=moll", 
                 "+proj=rpoly")

func <- function(projection){
  world <- sf::st_graticule(lon=seq(-180,180, 15),
                            lat = seq(-90,90, 7.5),
                            ndiscr = 5000,
                            margin = 0.00000000001) %>% st_transform(projection)
  world$crs <- str_split(projection, " ")[[1]][1]
  world}

###############################################图2-2-19（a）等距圆柱投影 

eqc  <- func("+proj=eqc")

ggplot() +
  geom_sf(data = eqc, size = 0.3, color = "#424242", alpha = 0.6) +
  theme(panel.grid.major = element_blank(),
        text=element_text(family="serif"),
        panel.background = element_rect(fill= "white")
        )
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_a.png",
       width = 7, height = 5.5, dpi = 900,bg="white",device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_a.pdf",
       width = 7, height = 5.5,device = cairo_pdf)


###############################################图2-2-19（b）兰勃特方位等积投影

laea  <- func("+proj=laea")

ggplot() +
  geom_sf(data = laea, size = 0.3, color = "#424242", alpha = 0.6) +
  theme(panel.grid.major = element_blank(),
        text=element_text(family="serif"),
        panel.background = element_rect(fill= "white"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_b.png",
       width = 6, height = 5.5, dpi = 900,bg="white",device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_b.pdf",
       width = 6, height = 5.5,device = cairo_pdf)

###############################################图2-2-19（c）平等地球投影

EqualEarth <- func("+proj=eqearth")

ggplot() +
  geom_sf(data = EqualEarth, size = 0.3, color = "#424242", alpha = 0.6) +
  theme(panel.grid.major = element_blank(),
        text=element_text(family="serif"),
        panel.background = element_rect(fill= "white"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_c.png",
       width = 7, height = 5.5, dpi = 900,bg="white",device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-19 地理坐标系框架样式_c.pdf",
       width = 7, height = 5.5,device = cairo_pdf)
	   
	   
