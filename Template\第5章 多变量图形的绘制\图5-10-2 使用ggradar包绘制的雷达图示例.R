"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(ggradar)


test_data <- data.frame(group = c('Name01','Name02','Name03'),
                        Biology = c(7.9, 3.9, 9.4),
                        Physics = c(10, 20, 0),
                        Maths = c(3.7, 11.5, 2.5),
                        Sport = c(8.7, 20, 4),
                        English = c(7.9, 7.2, 12.4),
                        Art = c(2.4, 0.2, 9.8),
                        Music = c(20, 20, 20))
lcols <- parula(3)

ggradar(test_data,font.radar = "serif",values.radar = seq(0,20,10),
        grid.min = 0, grid.mid = 10, grid.max = 20,group.colours = lcols,
        background.circle.colour = "NA",axis.label.size = 4,grid.label.size = 4,
        gridline.min.linetype = "solid",
        gridline.mid.linetype = "solid",
        gridline.max.linetype = "solid",
        plot.legend=FALSE) +
  facet_wrap(~group,ncol = 3) +
  #theme_void() +
  theme(legend.position = "none",
        strip.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 15),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-10-2 使用ggradar包绘制的雷达图示例.png",
       width =9, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-10-2 使用ggradar包绘制的雷达图示例.pdf",
       width =9, height = 5,device = cairo_pdf)