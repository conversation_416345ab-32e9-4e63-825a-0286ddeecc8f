"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

values01 <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_City.csv")

merger01 <- sp::merge(x = map_fig02,y=values01,by =c("country","SP_ID"),
                      duplicateGeoms=FALSE)

####################################图6-2-6（a）气泡地图的单维度数值映射（气泡大小）

ggplot(data = merger01) +
  geom_sf(fill="#9CCA9C",alpha=0.8,linewidth=0.3) +
  geom_point(aes(x=long,y = lat,size=orange),shape=21,fill="#F7D826") + 
  labs(x="Longitude",y="Latitude",size="Orange")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.85,0.8),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.title= element_text(size = 12),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-6 单、双维度数值映射气泡地图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-6 单、双维度数值映射气泡地图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-2-6（b）气泡地图的双维度数值映射（气泡大小、颜色）

ggplot(data = merger01) +
  geom_sf(fill="#9CCA9C",alpha=0.8,linewidth=0.3) +
  geom_point(aes(x=long,y = lat,size=orange,fill=apple),shape=21) + 
  labs(x="Longitude",y="Latitude",size="Orange",fill="Apple")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  scale_fill_gradientn(colors = parula(100),guide = guide_colorbar(frame.colour = "black",
                                                                   ticks.colour = "black")) +
  theme(legend.position = c(0.9,0.5),
        legend.title= element_text(size = 12),
        legend.text = element_text(size = 10),
        legend.key.height=unit(0.5, "cm"),
        legend.key.width=unit(0.4, "cm"),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-6 单、双维度数值映射气泡地图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-6 单、双维度数值映射气泡地图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
	   