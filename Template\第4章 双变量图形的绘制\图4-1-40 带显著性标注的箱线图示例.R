"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)


#构建数据集

box_data <- read_excel("\\第4章 双变量图形的绘制\\箱线图_pro.xlsx")

									

####################################图4-1-40（a）未添加P值箱线图示例
ggplot(data = box_data,aes(x=variable,y=value)) +
  stat_boxplot(geom = "errorbar",width = 0.4,linewidth=0.4) + 
  geom_boxplot(aes(fill=variable),linewidth=0.4) +
  ggprism::scale_fill_prism(palette = "waves") +
  scale_y_log10()+
  labs(x="",y="Values") +
  theme_classic() +
  theme(legend.position = "none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x =element_text(angle = 25,hjust = 0.5,vjust = 0.5),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-40 带显著性标注的箱线图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-40 带显著性标注的箱线图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-40（b）添加P值箱线图示例

ggplot(data = box_data,aes(x=variable,y=value)) +
  stat_boxplot(geom = "errorbar",width = 0.4,linewidth=0.4) + 
  geom_boxplot(aes(fill=variable),linewidth=0.4) +
  ggpubr::stat_compare_means(comparisons = list(c("Vac+/Inf+", "Vac+/Inf-"),
                                                c("Vac-/Inf+","Vac-/Inf-")),
                            tip.length=0,
                            family="serif",label = "p.signif",size=5) +
  ggprism::scale_fill_prism(palette = "waves") +
  scale_y_log10(limits=c(-0.1,80000))+
  labs(x="",y="Values") +
  theme_classic() +
  theme(legend.position = "none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x =element_text(angle = 25,hjust = 0.5,vjust = 0.5),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-40 带显著性标注的箱线图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-40 带显著性标注的箱线图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   