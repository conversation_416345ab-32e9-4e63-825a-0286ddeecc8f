"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
ToothGrowth$dose <- as.factor(ToothGrowth$dose)
#head(ToothGrowth)

###############################################图2-2-6（a）未添加统计变换效果

ggplot(ToothGrowth, aes(x=dose, y=len)) + 
    geom_violin(trim=FALSE,fill="#CD534C") + 
    #theme_classic() + 
    labs(x="X Label", y = "Y Label")+
    #定制化刻度样式
    scale_y_continuous(expand = c(0, 0), limits = c(-3, 40),
                     breaks = seq(0, 40, by = 10)) +
    theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_a.png",
       width = 4.3, height = 4, dpi = 900,device = png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_a.pdf",
       width = 4.3, height = 4,device = cairo_pdf)

###############################################图2-2-6（b）stat_summary() 绘制效果

ggplot(ToothGrowth, aes(x=dose, y=len)) + 
   geom_violin(trim=FALSE,fill="#CD534C") + 
   stat_summary(fun.data=mean_sd,
                 geom="pointrange",size=1) +
   labs(x="X Label", y = "Y Label")+
    #theme_classic() + 
    #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(-3, 40),
                     breaks = seq(0, 40, by = 10)) +
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_b.png",
       width = 4.3, height = 4, dpi = 900,device = png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_b.pdf",
       width = 4.3, height = 4,device = cairo_pdf)


###############################################图2-2-6（c）几何对象函数设置stat参数效果

ggplot(ToothGrowth, aes(x=dose, y=len)) + 
    geom_violin(trim=FALSE,fill="#CD534C") + 
    geom_pointrange(stat = "summary",fun.data=mean_sd,size=1,colour="gray80") +
#     geom_crossbar(stat = "summary",fun.data=mean_sd,width=0.2)+
# #     stat_summary(fun.data=mean_sd,geom="pointrange",
# #                  colour = "black", size = 1) +
    labs(x="X Label", y = "Y Label")+
    scale_y_continuous(expand = c(0, 0), limits = c(-3, 40),
                     breaks = seq(0, 40, by = 10)) +
    theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_c.png",
       width = 4.3, height = 4, dpi = 900,device = png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-6 使用统计变换函数和几何对象函数绘制的图形示例_c.pdf",
       width = 4.3, height = 4,device = cairo_pdf)