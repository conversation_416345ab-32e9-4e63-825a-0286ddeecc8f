"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library(plot3D)

scatter_3d <- read_excel("\\第5章 多变量图形的绘制\\scatter_3d.xlsx")
x <- scatter_3d$x
y <- scatter_3d$y
z <- scatter_3d$z
color_var <- scatter_3d$d
color <- parula(100)
####################################图5-4-1（a）plot3D 3D 散点图示例1

png(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_a.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_a.pdf",
    width = 6, height = 5,family = "serif")	
	
par(family = "serif",mar = rep(2, 4))
scatter3D(x, y, z,colvar=color_var,col = color,bty = "f",pch = 16,ticktype = "detailed",
          colkey = list(length = 0.65),phi = 30,cex = 2,xlab = "X",ylab ="Y", zlab = "Z",
          clab="Cbar_values")
dev.off()

####################################图5-4-1（b）plot3D 3D 散点图示例2

colors <- rev(RColorBrewer::brewer.pal(11, "Spectral"))

png(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_b.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_b.pdf",
    width = 6, height = 5,family = "serif")	
	
par(family = "serif",mar = rep(2, 4))
scatter3D(x, y, z,colvar=color_var,col = colors,bty = "f",pch = 16,ticktype = "detailed",
          colkey = list(length = 0.6),type = "h", phi = 30,cex = 2,
          clab="Cbar_values")
dev.off()

####################################图5-4-1（c）使用scatterplot3d 绘制的3D气泡图
library(scatterplot3d)

png(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_c.png",
    width = 6000, height = 5000,res=1000)
	
pdf(file="\\第5章 多变量图形的绘制\\图5-4-1 R语言基础3D散点图系列绘制示例_c.pdf",
    width = 6, height = 5,family = "serif")	
	
par(family = "serif",mar = rep(2, 4))
# Create a 3D scatter plot
s3d <- scatterplot3d(x, y, z, type="n")
# Add bubbles
symbols(x = s3d$xyz.convert(x, y, z)$x, y = s3d$xyz.convert(x, y, z)$y, 
  circles = color_var, inches = 0.15, add = TRUE, 
  fg = "black", bg = "#E80809", xpd = TRUE)
dev.off()