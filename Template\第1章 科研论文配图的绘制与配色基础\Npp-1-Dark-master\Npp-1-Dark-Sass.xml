<!-- Sass Syntax Highlighting for Npp-1-Dark -->
<NotepadPlus>
    <UserLang name="Npp-1Dark-Sass" ext="sass scss" udlVersion="2.1">
        <Settings>
            <Global caseIgnored="yes" allowFoldOfComments="yes" foldCompact="yes" forcePureLC="0" decimalSeparator="0" />
            <Prefix Keywords1="no" Keywords2="no" Keywords3="no" Keywords4="yes" Keywords5="yes" Keywords6="yes" Keywords7="yes" Keywords8="yes" />
        </Settings>
        <KeywordLists>
            <Keywords name="Comments">00// 01 02 03/* 03/*! 04*/ 04*/</Keywords>
            <Keywords name="Numbers, prefix1"></Keywords>
            <Keywords name="Numbers, prefix2"></Keywords>
            <Keywords name="Numbers, extras1"></Keywords>
            <Keywords name="Numbers, extras2"></Keywords>
            <Keywords name="Numbers, suffix1"></Keywords>
            <Keywords name="Numbers, suffix2"></Keywords>
            <Keywords name="Numbers, range"></Keywords>
            <Keywords name="Operators1">&apos; , : ;</Keywords>
            <Keywords name="Operators2"></Keywords>
            <Keywords name="Folders in code1, open">{</Keywords>
            <Keywords name="Folders in code1, middle"></Keywords>
            <Keywords name="Folders in code1, close">}</Keywords>
            <Keywords name="Folders in code2, open"></Keywords>
            <Keywords name="Folders in code2, middle"></Keywords>
            <Keywords name="Folders in code2, close"></Keywords>
            <Keywords name="Folders in comment, open">/*</Keywords>
            <Keywords name="Folders in comment, middle"></Keywords>
            <Keywords name="Folders in comment, close">*/</Keywords>
            <Keywords name="Keywords1">a abbr acronym address applet area article aside audio b base basefont bdo bdi big blockquote body br button canvas caption center cite code col colgroup command datalist dd del details dfn dir div dl dt em embed fieldset figcaption figure font footer form frame frameset h1 h2 h3 h4 h5 h6 head header hgroup hr html i iframe img input ins keygen kbd abel legend li link map mark menu meta meter nav noframes noscript object ol optgroup option output p param pre progress q rp rt ruby s samp script section select small source span strike strong style sub summary sup table tbody td textarea tfoot th thead time title tr track tt u ul var video wbr xmp label</Keywords>
            <Keywords name="Keywords2">active after before checked disabled empty enabled first first-child first-letter first-line first-of-type focus hover lang last-child last-of-type left link not only-child only-of-type nth-child right root target visited</Keywords>
            <Keywords name="Keywords3">@font-face @font-feature-values @keyframes align-content align-items align-self animation animation-delay animation-direction animation-duration animation-fill-mode animation-iteration-count animation-name animation-play-state animation-timing-function azimuth backface-visibility background background-attachment background-clip background-color background-image background-origin background-position background-repeat background-size border border-bottom border-bottom-color border-bottom-left-radius border-bottom-right-radius border-bottom-style border-bottom-width border-collapse border-color border-image border-image-outset border-image-repeat border-image-slice border-image-source border-image-width border-left border-left-color border-left-style border-left-width border-radius border-right border-right-color border-right-style border-right-width border-spacing border-style border-top border-top-color border-top-left-radius border-top-right-radius border-top-style border-top-width border-width bottom box-decoration-break box-shadow box-sizing break-after break-before break-inside caption-side clear clip color column-count column-fill column-gap column-rule column-rule-color column-rule-style column-rule-width columns column-span column-width content counter-increment counter-reset cue cue-after cue-before cursor decoration direction display elevation empty-cells family filter flex flex-basis flex-direction flex-flow flex-grow flex-shrink flex-wrap float font font-family font-feature-setting font-kerning font-language-override font-size font-size-adjust font-stretch font-style font-synthesis font-variant font-variant-alternates font-variant-caps font-variant-east-asian font-variant-ligatures font-variant-numeric font-variant-position font-weight hanging-punctuation height hyphens icon image image-orientation image-rendering image-resolution ime-mode indent justify-content left letter-spacing line-break line-height list-style list-style-image list-style-position list-style-type margin margin-bottom margin-left margin-right margin-top mark mark-after mark-before marker-offset marks marquee-direction marquee-play-count marquee-speed marquee-style mask mask-type max-height max-width min-height min-width nav-down nav-index nav-left nav-right nav-up object-fit object-position opacity order orphans outline outline-color outline-offset outline-style outline-width overflow overflow-wrap overflow-x overflow-y padding padding-bottom padding-left padding-right padding-top page page-break-after page-break-before page-break-inside pause pause-after pause-before perspective perspective-origin phonemes pitch pitch-range play-during position quotes repeat resize rest rest-after rest-before richness right size speak speak-header speak-numeral speak-punctuation speech-rate stress table-layout tab-size text-align text-align-last text-combine-horizontal text-decoration text-decoration-color text-decoration-line text-decoration-style text-indent text-justify text-orientation text-overflow text-rendering text-shadow text-transform text-underline-position top transform transform-origin transform-style transition transition-delay transition-duration transition-property transition-timing-function unicode-bidi vertical-align visibility voice-balance voice-duration voice-family voice-pitch voice-pitch-range voice-rate voice-stress voice-volume volume white-space widows width word-break word-spacing word-wrap writing-mode z-index</Keywords>
            <Keywords name="Keywords4">&amp;</Keywords>
            <Keywords name="Keywords5">! @ $ *</Keywords>
            <Keywords name="Keywords6">#</Keywords>
            <Keywords name="Keywords7">.</Keywords>
            <Keywords name="Keywords8">-webkit- -moz- -ms- -o-</Keywords>
            <Keywords name="Delimiters">00&apos; 01 02&apos; 03&quot; 04 05&quot; 06( 07 08) 09[ 10 11] 12 13 14 15 16 17 18 19 20 21 22 23</Keywords>
        </KeywordLists>
        <Styles>
            <WordsStyle name="DEFAULT" fgColor="98C379" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="COMMENTS" fgColor="5C6370" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="LINE COMMENTS" fgColor="5C6370" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="NUMBERS" fgColor="D19A66" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS1" fgColor="E06C75" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS2" fgColor="56B6C2" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS3" fgColor="ABB2BF" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS4" fgColor="C078DD" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS5" fgColor="E06C75" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS6" fgColor="61AFEF" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS7" fgColor="D19A66" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="KEYWORDS8" fgColor="56B6C2" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="OPERATORS" fgColor="5C6370" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="FOLDER IN CODE1" fgColor="5C6370" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="FOLDER IN CODE2" fgColor="56B6C2" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="FOLDER IN COMMENT" fgColor="5C6370" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS1" fgColor="98C379" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS2" fgColor="98C379" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS3" fgColor="D19A66" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS4" fgColor="56B6C2" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS5" fgColor="000000" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS6" fgColor="000000" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS7" fgColor="000000" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
            <WordsStyle name="DELIMITERS8" fgColor="000000" bgColor="282C34" fontName="Consolas" fontStyle="0" fontSize="11" nesting="0" />
        </Styles>
    </UserLang>
</NotepadPlus>
