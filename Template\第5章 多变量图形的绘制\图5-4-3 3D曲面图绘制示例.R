"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library(plot3D)

xSeq = seq(-10, 10,length.out=20)
ySeq = seq(-10, 10,length.out=20)
Z <- outer(xSeq,ySeq,
           function(X,Y) X+Y)
M <- mesh(xSeq,ySeq)
x=M$x
y=M$y		   


####################################图5-4-3（a）3D曲面图（单一颜色样式）

png(file="\\第5章 多变量图形的绘制\\图5-4-3 3D曲面图绘制示例_a.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-3 3D曲面图绘制示例_a.pdf",
    width = 6, height = 5,family = "serif")	
par(family = "serif",mar = rep(2, 4))
surf3D(x = x, y = y, z = Z,bty = "b2",col = "#2796EC", border = "black",ticktype = "detailed",
      ltheta = 90,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0)
dev.off()

####################################图5-4-3（b）3D曲面图（渐变色样式）

color <- parula(100)

png(file="\\第5章 多变量图形的绘制\\图5-4-3 3D曲面图绘制示例_b.png",
    width = 6000, height = 5000,res=1000)
	
pdf(file="\\第5章 多变量图形的绘制\\图5-4-3 3D曲面图绘制示例_b.pdf",
    width = 6, height = 5,family = "serif")

par(family = "serif",mar = rep(2, 4))
surf3D(x = x, y = y, z = Z,bty = "b2",col =color,border = "black",ticktype = "detailed",
      ltheta = 90,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0,
      colkey = list(length = 0.6))
dev.off()