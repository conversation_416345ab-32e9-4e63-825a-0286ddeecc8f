"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(psych)

library(ggcorrplot2)


#读取数据
heatmap_data <- read_excel("\\第4章 双变量图形的绘制\\相关性热力图_P值.xlsx")

corr <- round(cor(heatmap_data), 2)

ct <- corr.test(heatmap_data, adjust = "none")
p.mat <- ggcorrplot::cor_pmat(heatmap_data)

				  
####################################图4-2-24（a）使用ggcorrplot2 绘制的相关性矩阵热力图示例（circle）

ggcorrplot2::ggcorrplot(corr,method = "circle") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_a.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_a.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-24（b）使用ggcorrplot2 绘制的相关性矩阵热力图示例（square）

ggcorrplot2::ggcorrplot(corr,method = "square") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_b.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_b.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-24（c）使用ggcorrplot2 绘制的相关性矩阵热力图示例（ellipse）
ggcorrplot2::ggcorrplot(corr,method = "ellipse") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))


ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_c.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_c.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

####################################图4-2-24（d）使用ggcorrplot2 绘制的相关性矩阵热力图示例（ellipse+upper）
# the upper triangle
ggcorrplot2::ggcorrplot(corr,method = "ellipse",type = "upper") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_d.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_d.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

####################################图4-2-24（e）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+lower）

# the upper triangle
ggcorrplot2::ggcorrplot(corr,method = "ellipse",type = "lower") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_e.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_e.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-24（f）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+number）	
# default: upper = "ellipse", lower = "number"

# the upper triangle
ggcorrplot2::ggcorrplot.mixed(corr,upper = "ellipse", lower = "number") +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_f.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_f.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)  
	   
####################################图4-2-24（g）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+number+P）	
# Combine correlogram with the significance test
# ----------------------------------------------
# Insignificant coefficients according to the default significant level 
# (sig.lvl = 0.05) are indicated by X by default.

ggcorrplot2::ggcorrplot.mixed(corr, upper = "ellipse", lower = "number", 
                              p.mat = p.mat) +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_g.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_g.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

	   
####################################图4-2-24（h）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+number+P_sig）

# Label significant coefficients with asterisks (*, default) denoting the significance level

ggcorrplot2::ggcorrplot.mixed(corr, upper = "ellipse", lower = "number", p.mat = p.mat, 
                 insig = "label_sig", sig.lvl = c(0.05, 0.01, 0.001)) +
    theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_h.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_h.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-24（i）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+number）	
# Label significant coefficients with varying number of + denoting the significance level
p <- ggcorrplot.mixed(corr, upper = "ellipse", lower = "number", p.mat = p.mat, 
                 insig = "label_sig", sig.lvl = c(0.05, 0.01, 0.001), pch = "+", 
                 pch.cex = 4) +
 theme(text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
p
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_i.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_i.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-24（j）使用ggcorrplot2绘制的相关性矩阵热力图 示例（ellipse+number）	
col1 <- colorRampPalette(c("#7F0000", "red", "#FF7F00", "yellow", "white",
                           "cyan", "#007FFF", "blue", "#00007F"))

# Change the colorbar direction to horizontal and place it at the bottom
# As mixed methods are used, there are two scales: color filled in ellipse and 
# number color
p <- p + scale_fill_gradientn(colours = col1(10), limits = c(-1, 1),
                                guide = guide_colorbar(
                                  direction = "horizontal",
                                  title = "",
                                  nbin = 1000,
                                  ticks.colour = "black",
                                  frame.colour = "black",
                                  barwidth = 15,
                                  barheight = 1.5)) +
  scale_colour_gradientn(colours = col1(10), limits = c(-1, 1),
                       guide = guide_colorbar(
                         direction = "horizontal",
                         title = "",
                         nbin = 1000,
                         ticks.colour = "black",
                         frame.colour = "black",
                         barwidth = 15,
                         barheight = 1.5)) +
  theme(legend.position = "bottom",
        text = element_text(face='bold',size = 12),
         plot.background = element_rect(fill = "white",colour="white"),
         axis.text = element_text(colour = "black",face='bold',size = 15))
p

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_j.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-24 使用ggcorrplot2绘制的相关性矩阵热力图示例_j.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)						   
	   