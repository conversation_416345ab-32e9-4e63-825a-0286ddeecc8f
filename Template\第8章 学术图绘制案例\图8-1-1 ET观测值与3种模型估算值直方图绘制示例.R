"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


library(extrafont) 
font_import(pattern = "lmroman*")


#构建数据
scatter_data <- read_excel("\\第8章 学术图绘制案例\\scatter_data.xlsx")

									  												 												  
####################################图8-1-1（a）ET观测值直方图

ggplot(data = scatter_data,aes(x=obser)) +
  geom_bar(width = 0.85,fill="#5C5C5C",color="black",
           linewidth=0.5) +
  scale_x_binned(breaks = seq(-5,90,10),limits = c(-7,85),
                 expand = c(0, 0)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,6000),
                     breaks = seq(0,6000,1000)) +
  labs(x="ET Values",y="Number of Cases") +
  theme_classic() +
  theme(legend.position = "none",
           text = element_text(family = "LM Roman 10",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 16),
           axis.ticks.length=unit(0.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_a.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_a.pdf",
       width =5.5, height = 5,device = cairo_pdf)
	   
####################################图8-1-1（b）DNN模型估算值直方图

ggplot(data = scatter_data,aes(x=DNN)) +
  geom_bar(width = 0.85,fill="#5C5C5C",color="black",
           linewidth=0.5) +
  scale_x_binned(breaks = seq(-5,90,10),limits = c(-7,85),
                 expand = c(0, 0)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,6200),
                     breaks = seq(0,6100,1000)) +
  labs(x="ET Values",y="Number of Cases") +
  theme_classic() +
  theme(legend.position = "none",
           text = element_text(family = "LM Roman 10",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 16),
           axis.ticks.length=unit(0.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_b.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_b.pdf",
       width =5.5, height = 5,device = cairo_pdf)

####################################图8-1-1（c）LR模型估算值直方图
ggplot(data = scatter_data,aes(x=LR)) +
  geom_bar(width = 0.85,fill="#5C5C5C",color="black",
           linewidth=0.5) +
  scale_x_binned(breaks = seq(-5,90,10),expand = c(0, 0)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,6000),
                     breaks = seq(0,6000,1000)) +
  labs(x="ET Values",y="Number of Cases") +
  theme_classic() +
  theme(legend.position = "none",
           text = element_text(family = "LM Roman 10",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 16),
           axis.ticks.length=unit(0.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_c.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_c.pdf",
       width =5.5, height = 5,device = cairo_pdf)

####################################图8-1-1（d）SVR模型估算值直方图  

ggplot(data = scatter_data,aes(x=SVR)) +
  geom_bar(width = 0.85,fill="#5C5C5C",color="black",
           linewidth=0.5) +
  scale_x_binned(breaks = seq(-5,90,10),limits = c(-7,85),
                 expand = c(0, 0)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,6000),
                     breaks = seq(0,6000,1000)) +
  labs(x="ET Values",y="Number of Cases") +
  theme_classic() +
  theme(legend.position = "none",
           text = element_text(family = "LM Roman 10",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 16),
           axis.ticks.length=unit(0.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_d.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-1 ET观测值与3种模型估算值直方图绘制示例_d.pdf",
       width =5.5, height = 5,device = cairo_pdf)