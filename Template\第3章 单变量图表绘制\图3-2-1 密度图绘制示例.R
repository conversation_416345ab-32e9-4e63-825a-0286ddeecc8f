"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#读取数据

dens_data <- readr::read_csv("\\第3章 单变量图表绘制\\density_data.csv")


####################################图3-2-1（a）geom_density() 函数密度图 （纵轴为density）
ggplot(data = desi_data,aes(x = data_01)) +
  geom_density(fill="#0073C2",linewidth=0.4) +
  geom_rug(length = unit(0.05, "npc")) +
  xlim(c(0,25)) +
  scale_y_continuous(expand = c(0, 0)) +
  labs(x="values")+
  theme_classic()+
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_a.png",
       width = 5, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)


####################################图3-2-1（b）geom_density() 函数密度图 （纵轴为count）

ggplot(data = desi_data) +
  geom_density(aes(x = data_01,after_stat(count)),
               fill="#EFC000",linewidth=0.4) +
  geom_rug(aes(x = data_01),length = unit(0.05, "npc")) +
  xlim(c(0,25)) +
  scale_y_continuous(expand = c(0, 0)) +
  labs(x="values")+
  theme_classic()+
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_b.png",
       width = 5, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_b.pdf",
       width =5, height = 4,device = cairo_pdf)

####################################图3-2-1（c）geom_density() 函数密度图 （渐变色填充）
ggplot(data = data.frame(x = dens$x, y = dens$y)) +
  geom_segment(aes(x = x,xend=x,y = y,yend=0,color=x))+
  geom_density(data = desi_data,aes(x = data_01),color="black",
               linewidth=0.4) +
  geom_rug(data = desi_data,aes(x = data_01),
           length = unit(0.05, "npc")) +
  scale_color_gradientn(name="values",colours = parula(100)) +
  scale_y_continuous(expand = c(0, 0)) +
  labs(x="values",y="density")+
  theme_classic()+
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_c.png",
       width = 5, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-2-1 密度图绘制示例_c.pdf",
       width =5, height = 4,device = cairo_pdf)


