"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-22（a）ggthemes 包可视化结果

library(ggthemes)


ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  ggthemes::geom_rangeframe() +
  labs(x="X Label", y = "Y Label")+
  scale_fill_gradientn(colours = parula(100)) +
  ggthemes::theme_tufte(base_family = "serif") +
  scale_x_continuous(breaks = extended_range_breaks()(tips$total_bill)) +
  scale_y_continuous(breaks = extended_range_breaks()(tips$tip))+
  theme(
        legend.text=element_text(family = "serif",size = 12),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_a.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_a.pdf",
       width = 4, height = 4,device = cairo_pdf)

###############################################图2-2-22（b）hrbrthemes 包可视化结果

library(hrbrthemes)

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  scale_fill_gradientn(colours = parula(100)) +
  hrbrthemes::theme_ipsum_pub(base_family = "serif",base_size = 12,
                              plot_margin = margin(0, 0, 0, 0),
                              axis_title_size = 15) +
  theme(plot.background = element_rect(fill="white",color = "white"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_b.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_b.pdf",
       width = 4, height = 4,device = cairo_pdf)

###############################################图2-2-22（c）envalysis 包可视化结果
library(envalysis)

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  scale_fill_gradientn(colours = parula(100)) +
  envalysis::theme_publish(base_family = "serif") +
  theme(legend.position = "right") +
  theme(plot.background = element_rect(fill="white",color = "white"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_c.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-22 不同拓展工具包绘图主题样式_c.pdf",
       width = 4, height = 4,device = cairo_pdf)

