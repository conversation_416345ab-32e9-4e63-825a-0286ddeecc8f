"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library(plot3D)

xSeq = seq(-2, 2,length.out=100)
ySeq = seq(-1, 2,length.out=100)
M <- mesh(xSeq,ySeq)
X <- M$x
Y <- M$y
Z <- outer(xSeq,ySeq,function(X,Y) X**2 + Y**2)
color <- parula(100)

####################################图5-4-4（a）其他矩阵数据集3D曲面图绘制示例1

png(file="\\第5章 多变量图形的绘制\\图5-4-4 R语言其他矩阵数据集3D曲面图绘制示例_a.png",
    width = 7500, height = 4500,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-4 R语言其他矩阵数据集3D曲面图绘制示例_a.pdf",
    width = 7.5, height = 4,family = "serif")	
	
par(family = "serif",mfrow = c(1, 2), mar = rep(1, 4))
surf3D(x = X, y = Y, z = Z,bty = "b2",ticktype = "detailed",
      ltheta = 90,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0,
      colkey = list(length = 0.3))
surf3D(x = X, y = Y, z = Z,bty = "b2",col = color,ticktype = "detailed",
      ltheta = 90,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0,
      colkey = list(length = 0.3))
dev.off()

####################################图5-4-4（b）其他矩阵数据集3D曲面图绘制示例2
x = np.linspace(-2, 0, 100)
y = np.linspace(0, 2, 100)
[X, Y] = np.meshgrid(x,y)
# Define the function Z = f(X,Y)
Z = 2./np.exp((X-.5)**2+Y**2)-2./np.exp((X+.5)**2+Y**2)

xSeq = seq(-2, 0,length.out=100)
ySeq = seq(0, 2,length.out=100)
XY = expand.grid(X=xSeq,Y=ySeq)

Z <- outer(xSeq,ySeq,function(X,Y) 2./exp((X-.5)**2+Y**2)-2./exp((X+.5)**2+Y**2))

    
png(file="\\第5章 多变量图形的绘制\\图5-4-4 R语言其他矩阵数据集3D曲面图绘制示例_b.png",
    width = 7500, height = 4000,res=1000)
	
pdf(file="\\第5章 多变量图形的绘制\\图5-4-4 R语言其他矩阵数据集3D曲面图绘制示例_b.pdf",
    width = 7.5, height = 4,family = "serif")	
	
par(family = "serif",mfrow = c(1, 2), mar = rep(2, 4))
surf3D(x = X, y = Y, z = Z,bty = "b2",ticktype = "detailed",
      ltheta = 30,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0,
      colkey = list(length = 0.3))
surf3D(x = X, y = Y, z = Z,bty = "b2",col = color,ticktype = "detailed",
      ltheta = 30,phi = 30,cex.axis = 1.2,cex.lab = 1.5,shade = 0,
      colkey = list(length = 0.3))
dev.off()
