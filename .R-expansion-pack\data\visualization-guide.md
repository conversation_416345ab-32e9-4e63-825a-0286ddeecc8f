# R语言数据可视化指南

## ggplot2基础

### 图形语法核心概念
- **数据（Data）**: 要可视化的数据集
- **美学映射（Aesthetics）**: 数据变量到视觉属性的映射
- **几何对象（Geometries）**: 数据的视觉表示方式
- **统计变换（Statistics）**: 数据的统计变换
- **坐标系统（Coordinates）**: 数据到平面的映射
- **分面（Facets）**: 将数据分组显示
- **主题（Themes）**: 控制图形的整体外观

### 基本模板
```r
ggplot(data = <DATA>) +
  <GEOM_FUNCTION>(
    mapping = aes(<MAPPINGS>),
    stat = <STAT>,
    position = <POSITION>
  ) +
  <COORDINATE_FUNCTION> +
  <FACET_FUNCTION> +
  <THEME_FUNCTION>
```

## 常用图表类型

### 散点图
```r
# 基础散点图
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point()

# 添加颜色和大小映射
ggplot(data, aes(x = x_var, y = y_var, color = group, size = value)) +
  geom_point(alpha = 0.7) +
  scale_color_viridis_d()

# 添加趋势线
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point() +
  geom_smooth(method = "lm", se = TRUE)
```

### 线图
```r
# 时间序列线图
ggplot(data, aes(x = date, y = value)) +
  geom_line() +
  scale_x_date(date_labels = "%Y-%m", date_breaks = "1 month")

# 多组线图
ggplot(data, aes(x = x_var, y = y_var, color = group)) +
  geom_line(size = 1) +
  geom_point()
```

### 柱状图
```r
# 基础柱状图
ggplot(data, aes(x = category, y = value)) +
  geom_col()

# 分组柱状图
ggplot(data, aes(x = category, y = value, fill = group)) +
  geom_col(position = "dodge")

# 堆叠柱状图
ggplot(data, aes(x = category, y = value, fill = group)) +
  geom_col(position = "stack")

# 百分比堆叠
ggplot(data, aes(x = category, y = value, fill = group)) +
  geom_col(position = "fill") +
  scale_y_continuous(labels = scales::percent)
```

### 直方图和密度图
```r
# 直方图
ggplot(data, aes(x = continuous_var)) +
  geom_histogram(bins = 30, fill = "skyblue", alpha = 0.7)

# 密度图
ggplot(data, aes(x = continuous_var)) +
  geom_density(fill = "lightblue", alpha = 0.7)

# 组合直方图和密度图
ggplot(data, aes(x = continuous_var)) +
  geom_histogram(aes(y = ..density..), bins = 30, alpha = 0.7) +
  geom_density(color = "red", size = 1)
```

### 箱线图
```r
# 基础箱线图
ggplot(data, aes(x = group, y = value)) +
  geom_boxplot()

# 添加数据点
ggplot(data, aes(x = group, y = value)) +
  geom_boxplot() +
  geom_jitter(width = 0.2, alpha = 0.5)

# 小提琴图
ggplot(data, aes(x = group, y = value)) +
  geom_violin() +
  geom_boxplot(width = 0.1)
```

### 热力图
```r
# 相关性热力图
library(reshape2)
cor_matrix <- cor(data[numeric_columns])
melted_cor <- melt(cor_matrix)

ggplot(melted_cor, aes(Var1, Var2, fill = value)) +
  geom_tile() +
  scale_fill_gradient2(low = "blue", high = "red", mid = "white", 
                       midpoint = 0, limit = c(-1,1)) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))
```

## 高级可视化技巧

### 分面（Faceting）
```r
# 按单个变量分面
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point() +
  facet_wrap(~ group_var)

# 按两个变量分面
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point() +
  facet_grid(var1 ~ var2)

# 自由坐标轴
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point() +
  facet_wrap(~ group_var, scales = "free")
```

### 颜色和填充
```r
# 离散变量颜色
scale_color_manual(values = c("red", "blue", "green"))
scale_color_brewer(palette = "Set1")
scale_color_viridis_d()

# 连续变量颜色
scale_color_gradient(low = "blue", high = "red")
scale_color_viridis_c()

# 自定义调色板
my_colors <- c("#E31A1C", "#1F78B4", "#33A02C")
scale_color_manual(values = my_colors)
```

### 坐标系统
```r
# 翻转坐标轴
coord_flip()

# 极坐标系（饼图）
coord_polar(theta = "y")

# 固定比例
coord_fixed(ratio = 1)

# 对数坐标
scale_x_log10()
scale_y_log10()
```

### 注释和标签
```r
# 添加文本注释
annotate("text", x = 5, y = 10, label = "重要点", size = 4)

# 添加箭头
annotate("segment", x = 1, xend = 2, y = 1, yend = 2, 
         arrow = arrow(length = unit(0.2, "cm")))

# 添加矩形
annotate("rect", xmin = 1, xmax = 3, ymin = 1, ymax = 3, 
         alpha = 0.2, fill = "red")

# 数据标签
geom_text(aes(label = value), vjust = -0.5)
```

## 主题和样式

### 内置主题
```r
theme_minimal()    # 简洁主题
theme_classic()    # 经典主题
theme_bw()         # 黑白主题
theme_dark()       # 深色主题
theme_void()       # 空白主题
```

### 自定义主题
```r
my_theme <- theme(
  plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
  plot.subtitle = element_text(size = 12, hjust = 0.5),
  axis.title = element_text(size = 12, face = "bold"),
  axis.text = element_text(size = 10),
  legend.title = element_text(size = 12, face = "bold"),
  legend.text = element_text(size = 10),
  panel.grid.major = element_line(color = "grey90", size = 0.5),
  panel.grid.minor = element_blank(),
  panel.background = element_rect(fill = "white"),
  plot.background = element_rect(fill = "white")
)
```

### 标题和标签
```r
labs(
  title = "主标题",
  subtitle = "副标题",
  x = "X轴标签",
  y = "Y轴标签",
  color = "图例标题",
  caption = "数据来源或说明"
)
```

## 交互式可视化

### plotly
```r
library(plotly)

# 将ggplot转换为交互式图表
p <- ggplot(data, aes(x = x_var, y = y_var, color = group)) +
  geom_point()
ggplotly(p)

# 原生plotly
plot_ly(data, x = ~x_var, y = ~y_var, color = ~group, type = "scatter")
```

### DT表格
```r
library(DT)
datatable(data, options = list(pageLength = 10, scrollX = TRUE))
```

### leaflet地图
```r
library(leaflet)
leaflet() %>%
  addTiles() %>%
  addMarkers(lng = longitude, lat = latitude, popup = "标记信息")
```

## 专业图表

### 森林图
```r
library(forestplot)
forestplot(labeltext = labels, 
           mean = estimates, 
           lower = lower_ci, 
           upper = upper_ci)
```

### 生存曲线
```r
library(survminer)
ggsurvplot(survfit_object, data = data, 
           pval = TRUE, conf.int = TRUE)
```

### 网络图
```r
library(igraph)
library(ggraph)

ggraph(graph_object, layout = "fr") +
  geom_edge_link() +
  geom_node_point(size = 3) +
  geom_node_text(aes(label = name), repel = TRUE)
```

### 韦恩图
```r
library(VennDiagram)
venn.diagram(list(Set1 = set1, Set2 = set2), 
             filename = "venn.png")
```

## 输出和保存

### 保存图片
```r
# 使用ggsave
ggsave("plot.png", width = 8, height = 6, dpi = 300)
ggsave("plot.pdf", width = 8, height = 6)

# 指定设备
png("plot.png", width = 800, height = 600, res = 150)
print(plot)
dev.off()
```

### 多图布局
```r
library(patchwork)

# 水平排列
plot1 + plot2

# 垂直排列
plot1 / plot2

# 复杂布局
(plot1 + plot2) / plot3

# 使用gridExtra
library(gridExtra)
grid.arrange(plot1, plot2, plot3, ncol = 2)
```

## 可视化最佳实践

### 选择合适的图表类型
- **比较**: 柱状图、点图
- **分布**: 直方图、密度图、箱线图
- **关系**: 散点图、线图
- **组成**: 饼图、堆叠柱状图
- **趋势**: 线图、面积图

### 颜色使用原则
- 使用色盲友好的调色板
- 避免使用过多颜色（建议不超过7种）
- 保持颜色的一致性
- 考虑打印时的效果

### 数据标签和图例
- 提供清晰的轴标签和单位
- 使用有意义的图例标题
- 避免图例遮挡数据
- 考虑直接标注而非图例

### 简化和聚焦
- 移除不必要的网格线和边框
- 突出重要信息
- 避免3D效果和过度装饰
- 保持数据-墨水比例高
