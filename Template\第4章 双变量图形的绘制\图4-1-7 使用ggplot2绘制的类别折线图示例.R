"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(rstatix)


#构建数据
tips <- read_excel("\\第4章 双变量图形的绘制\\tips.xlsx")

line_stat <- tips %>% group_by(day) %>% 
           rstatix::get_summary_stats(type = "common")
		   
line_stat <- line_stat %>% dplyr::filter(variable == "total_bill")

#改变绘图属性顺序
line_stat$day <- factor(line_stat$day, 
                        levels=c("Thur","Fri","Sat","Sun"),ordered=TRUE)
										

####################################图4-1-7（a）基本类别折线图
ggplot(data = line_stat) +
  geom_line(aes(x = day,y=mean,group=1),linewidth=0.8) +
  geom_point(aes(x = day,y=mean),shape=21,fill="gray",size=5,stroke = 1) +
  scale_y_continuous(expand = c(0, 0),limits = c(15,23)) +
  labs(x="serif",y="Values") +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 16),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-7（b）类别折线图+SE估计
ggplot(data = line_stat) +
  geom_errorbar(aes(x = day,y=mean,ymin=mean-se, ymax=mean+se), width=.2) +
  geom_line(aes(x = day,y=mean,group=1),linewidth=0.8) +
  geom_point(aes(x = day,y=mean),shape=21,fill="gray",size=5,stroke = 1) +
  scale_y_continuous(,limits = c(15,23)) +
  labs(x="serif",y="Values") +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 16),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_b_se.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)



####################################图4-1-7（c）类别折线图+SD估计
ggplot(data = line_stat) +
  geom_errorbar(aes(x = day,y=mean,ymin=mean-sd, ymax=mean+sd), width=.2) +
  geom_line(aes(x = day,y=mean,group=1),linewidth=0.8) +
  geom_point(aes(x = day,y=mean),shape=21,fill="gray",size=5,stroke = 1) +
  labs(x="serif",y="Values") +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 16),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
	   
####################################图4-1-7（d）类别折线图+CI估计
ggplot(data = line_stat) +
  geom_errorbar(aes(x = day,y=mean,ymin=mean-sd, ymax=mean+sd), width=.2) +
  geom_line(aes(x = day,y=mean,group=1),linewidth=0.8) +
  geom_point(aes(x = day,y=mean),shape=21,fill="gray",size=5,stroke = 1) +
  labs(x="serif",y="Values") +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 16),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-7 使用ggplot2绘制的类别折线图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
