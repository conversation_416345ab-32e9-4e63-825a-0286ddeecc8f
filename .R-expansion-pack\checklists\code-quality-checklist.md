# R代码质量检查清单

[[LLM: 这是一个R代码质量检查清单。执行时：
1. 逐项检查代码的各个质量方面
2. 为每项提供通过/失败/部分通过的评估
3. 记录发现的问题和改进建议
4. 生成代码质量评估报告]]

## 检查清单概述

**项目名称**: {{project_name}}
**代码版本**: {{code_version}}
**检查日期**: {{check_date}}
**检查者**: {{checker_name}}
**开发者**: {{developer_name}}

---

## 1. 代码风格和格式 (权重: 20%)

[[LLM: 检查代码是否遵循一致的风格和格式规范]]

### 1.1 命名规范
- [ ] **变量命名**: 使用snake_case，名称有意义
- [ ] **函数命名**: 使用snake_case，动词开头
- [ ] **常量命名**: 使用UPPER_CASE
- [ ] **文件命名**: 使用连字符，描述性强

### 1.2 代码格式
- [ ] **缩进一致**: 使用2个空格缩进
- [ ] **行长度**: 每行不超过80字符
- [ ] **空行使用**: 合理使用空行分隔逻辑块
- [ ] **空格使用**: 操作符前后有空格

### 1.3 注释规范
- [ ] **文件头注释**: 每个文件有描述性头注释
- [ ] **函数注释**: 使用roxygen2格式注释函数
- [ ] **行内注释**: 复杂逻辑有解释性注释
- [ ] **注释更新**: 注释与代码保持同步

### 1.4 代码组织
- [ ] **逻辑分组**: 相关代码组织在一起
- [ ] **导入语句**: 包导入语句在文件开头
- [ ] **函数顺序**: 函数按逻辑顺序排列
- [ ] **代码分离**: 配置、函数、主逻辑分离

**第1部分得分**: ___/16 (通过标准: ≥13)

---

## 2. 代码结构和设计 (权重: 25%)

[[LLM: 检查代码的结构设计是否合理]]

### 2.1 函数设计
- [ ] **单一职责**: 每个函数只做一件事
- [ ] **函数长度**: 函数长度适中（通常<50行）
- [ ] **参数数量**: 函数参数不超过5个
- [ ] **返回值**: 函数有明确的返回值

### 2.2 模块化
- [ ] **代码重用**: 避免重复代码，提取公共函数
- [ ] **模块分离**: 不同功能分离到不同文件
- [ ] **依赖管理**: 模块间依赖关系清晰
- [ ] **接口设计**: 模块接口设计合理

### 2.3 数据结构
- [ ] **数据类型**: 使用合适的数据类型
- [ ] **数据验证**: 对输入数据进行验证
- [ ] **数据转换**: 数据转换逻辑清晰
- [ ] **内存效率**: 考虑内存使用效率

### 2.4 控制流
- [ ] **条件语句**: 条件语句简洁明了
- [ ] **循环结构**: 优先使用向量化操作
- [ ] **错误处理**: 适当的错误处理机制
- [ ] **边界条件**: 处理边界和特殊情况

**第2部分得分**: ___/16 (通过标准: ≥13)

---

## 3. 性能和效率 (权重: 20%)

[[LLM: 检查代码的性能和运行效率]]

### 3.1 算法效率
- [ ] **算法选择**: 选择合适的算法
- [ ] **时间复杂度**: 考虑时间复杂度
- [ ] **空间复杂度**: 考虑空间复杂度
- [ ] **向量化**: 使用向量化操作替代循环

### 3.2 内存管理
- [ ] **内存使用**: 避免不必要的内存占用
- [ ] **对象清理**: 及时清理不需要的对象
- [ ] **大数据处理**: 合理处理大数据集
- [ ] **内存泄漏**: 避免内存泄漏

### 3.3 I/O操作
- [ ] **文件读写**: 高效的文件读写操作
- [ ] **数据库连接**: 合理管理数据库连接
- [ ] **网络请求**: 优化网络请求
- [ ] **缓存使用**: 适当使用缓存机制

### 3.4 并行处理
- [ ] **并行机会**: 识别并行处理机会
- [ ] **并行实现**: 正确实现并行处理
- [ ] **资源管理**: 合理管理计算资源
- [ ] **性能测试**: 进行性能基准测试

**第3部分得分**: ___/16 (通过标准: ≥13)

---

## 4. 错误处理和健壮性 (权重: 20%)

[[LLM: 检查代码的错误处理和健壮性]]

### 4.1 输入验证
- [ ] **参数检查**: 验证函数参数的有效性
- [ ] **类型检查**: 检查数据类型
- [ ] **范围检查**: 检查数值范围
- [ ] **空值处理**: 正确处理NULL和NA值

### 4.2 异常处理
- [ ] **try-catch**: 使用tryCatch处理异常
- [ ] **错误信息**: 提供有意义的错误信息
- [ ] **错误恢复**: 适当的错误恢复机制
- [ ] **日志记录**: 记录错误和警告信息

### 4.3 边界条件
- [ ] **空数据**: 处理空数据集
- [ ] **极值处理**: 处理极大或极小值
- [ ] **特殊情况**: 处理特殊输入情况
- [ ] **资源限制**: 考虑系统资源限制

### 4.4 测试覆盖（强制要求）
- [ ] **单元测试**: 使用testthat框架，测试覆盖率 >= 80%
- [ ] **集成测试**: 进行集成测试，所有测试必须通过
- [ ] **边界测试**: 测试边界条件和极值情况
- [ ] **错误测试**: 测试错误处理逻辑和异常情况
- [ ] **R CMD check**: 必须通过R CMD check --as-cran，无ERROR/WARNING
- [ ] **测试自动化**: 测试可以自动运行且结果可重现

**第4部分得分**: ___/18 (通过标准: ≥15)

---

## 5. 可维护性和文档 (权重: 15%)

[[LLM: 检查代码的可维护性和文档质量]]

### 5.1 代码可读性
- [ ] **代码清晰**: 代码逻辑清晰易懂
- [ ] **变量含义**: 变量名表达明确含义
- [ ] **函数目的**: 函数目的明确
- [ ] **复杂度控制**: 控制代码复杂度

### 5.2 文档完整性
- [ ] **README文件**: 有完整的README文档
- [ ] **函数文档**: 所有函数有完整文档
- [ ] **使用示例**: 提供使用示例
- [ ] **更新日志**: 维护更新日志

### 5.3 版本控制
- [ ] **Git使用**: 正确使用Git版本控制
- [ ] **提交信息**: 提交信息清晰有意义
- [ ] **分支管理**: 合理的分支管理策略
- [ ] **标签使用**: 使用标签标记版本

### 5.4 配置管理
- [ ] **配置分离**: 配置与代码分离
- [ ] **环境变量**: 使用环境变量管理配置
- [ ] **配置文档**: 配置项有清晰文档
- [ ] **默认值**: 提供合理的默认配置

**第5部分得分**: ___/16 (通过标准: ≥13)

---

## 6. 总体评估

### 6.1 得分汇总
- 代码风格和格式: ___/16 (20%)
- 代码结构和设计: ___/16 (25%)
- 性能和效率: ___/16 (20%)
- 错误处理和健壮性: ___/18 (20%)
- 可维护性和文档: ___/16 (15%)

**总分**: ___/82
**加权得分**: ___/100

### 6.1.1 质量门控制
**强制通过项目**（任一失败则整体不合格）：
- [ ] R CMD check --as-cran 无ERROR和WARNING
- [ ] 所有testthat测试通过
- [ ] 测试覆盖率 >= 80%
- [ ] 所有导出函数有完整roxygen2文档

### 6.2 质量等级
- **优秀** (90-100分): 代码质量极高，可作为最佳实践
- **良好** (80-89分): 代码质量良好，需要少量改进
- **合格** (70-79分): 代码基本合格，需要一些改进
- **需要改进** (60-69分): 代码有明显问题，需要重大改进
- **不合格** (<60分): 代码质量不达标，需要重构

**本次评估等级**: {{quality_grade}}

### 6.3 主要发现
**代码优点**:
{{REPEAT_START}}
- {{code_strength}}
{{REPEAT_END}}

**需要改进的方面**:
{{REPEAT_START}}
- {{improvement_area}}
{{REPEAT_END}}

### 6.4 改进建议
**高优先级**:
{{REPEAT_START}}
- {{high_priority_improvement}}
{{REPEAT_END}}

**中优先级**:
{{REPEAT_START}}
- {{medium_priority_improvement}}
{{REPEAT_END}}

**低优先级**:
{{REPEAT_START}}
- {{low_priority_improvement}}
{{REPEAT_END}}

### 6.5 行动计划
- [ ] {{action_item_1}}
- [ ] {{action_item_2}}
- [ ] {{action_item_3}}
- [ ] {{action_item_4}}

---

**检查完成时间**: {{completion_time}}
**建议复查时间**: {{review_time}}
**下次检查日期**: {{next_check_date}}

[[LLM: 代码质量检查完成后，生成一个执行摘要，包括：
1. 总体代码质量评级
2. 最关键的问题和风险
3. 优先改进建议
4. 预估改进工作量
5. 是否建议代码发布]]
