"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(psych)

library(linkET)
library(vegan)


#读取数据
data("varespec")
data("varechem")

#进行mantel test并新增连线数据

mantel <- linkET::mantel_test(spec = varespec, env = varechem,
                              spec_select = list(Spec01 = 1:7,
                                                 Spec02 = 8:18,
                                                 Spec03 = 19:37,
                                                 Spec04 = 38:44))  %>% 
   dplyr::mutate(rd = cut(r, breaks = c(-Inf, 0.2, 0.4, Inf),
                  labels = c("< 0.2", "0.2 - 0.4", ">= 0.4")),
                 pd = cut(p, breaks = c(-Inf, 0.01, 0.05, Inf),
                  labels = c("< 0.01", "0.01 <x< 0.05", ">= 0.05")))

colors <- rev(RColorBrewer::brewer.pal(11, "Spectral"))
line_color <- c("#665D86","#499440","#CDCDCA")				  
				  
####################################图4-2-27（a）使用linkET包绘制的Mantel检验相关性矩阵热力图示例1

qcorrplot(correlate(varechem), type = "lower") +
  geom_square() +
  geom_couple(aes(colour = pd, size = rd), 
              data = mantel, label.family="serif",label.size=5,
              label.fontface="bold",
              curvature = nice_curvature()) +
  scale_fill_gradientn(colours = colors,limit=c(-1,1)) +
  scale_size_manual(values = c(0.5, 1, 2)) +
  scale_colour_manual(values = line_color) +
  guides(size = guide_legend(title = "Mantel's r",
                             override.aes = list(colour = "grey35"), 
                             order = 2),
         colour = guide_legend(title = "Mantel's p", 
                               override.aes = list(linewidth = 2), 
                               order = 1),
         fill = guide_colorbar(title = "Pearson's r", order = 3)) +
   theme(
        text = element_text(family = "serif",face='bold',size = 12),
        axis.text = element_text(colour = "black",face='bold',size = 13),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-27 使用Mantel绘制的检验相关性矩阵热力组合图示_a.png",
       width =8.5, height = 6, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-27 使用Mantel绘制的检验相关性矩阵热力组合图示_a.pdf",
       width =8.5, height = 6,device = cairo_pdf)
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-27（b）使用linkET包绘制的Mantel检验相关性矩阵热力图示例2

qcorrplot(correlate(varechem), type = "upper", diag = FALSE) +
  geom_square() +
  geom_couple(aes(colour = pd, size = rd), 
              data = mantel, label.family="serif",label.size=5,
              label.fontface="bold",
              curvature = nice_curvature(by = "from")) +
  scale_fill_gradientn(colours = colors,limit=c(-1,1)) +
  scale_size_manual(values = c(0.5, 1, 2)) +
  scale_colour_manual(values = line_color) +
  guides(size = guide_legend(title = "Mantel's r",
                             override.aes = list(colour = "grey35"), 
                             order = 2),
         colour = guide_legend(title = "Mantel's p", 
                               override.aes = list(linewidth = 2), 
                               order = 1),
         fill = guide_colorbar(title = "Pearson's r", order = 3))+
  theme(
        text = element_text(family = "serif",face='bold',size = 12),
        axis.text = element_text(colour = "black",face='bold',size = 13),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-27 使用Mantel绘制的检验相关性矩阵热力组合图示_b.png",
       width =8.5, height = 6, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-27 使用Mantel绘制的检验相关性矩阵热力组合图示_b.pdf",
       width =8.5, height = 6,device = cairo_pdf)

