"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)
library(ggforce)


#构建数据集

sizes <- c(12, 40, 25, 15,8)
index <- c('A','B','C','D',"E")
pie_data <- data.frame(index,sizes)

#计算占比数值		
pie_data <- pie_data %>% dplyr::mutate(perc = sizes/sum(sizes)) %>% 
   arrange(perc) %>%
   mutate(labels = scales::percent(perc))	

hsize <- 2.5
donut_data <- pie_data %>% mutate(x = hsize)

####################################图4-1-49（a）学术色系环形图
ggplot(donut_data, aes(x = x, y = perc, fill = index)) +
  geom_col(colour="black",linewidth=0.2) +
  geom_text(aes(label = labels),size=5,
            position = position_stack(vjust = 0.5)) +
  ggsci::scale_fill_aaas() +
  coord_polar(theta = "y") +
  xlim(c(0.1, hsize + 0.5)) +
  theme_void()
  
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-49（b）灰色系环形图

ggplot(donut_data, aes(x = x, y = perc, fill = index)) +
  geom_col(colour="black",linewidth=0.2) +
  geom_text(aes(label = labels),size=5,
            position = position_stack(vjust = 0.5)) +
  scale_fill_grey() +
  coord_polar(theta = "y") +
  xlim(c(0.1, hsize + 0.5)) +
  theme_void()
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-49（c）纹理填充样式环形图
library(ggpattern)
colors <- c("white","white","white","white","white")
					 
ggplot(donut_data, aes(x = x, y = perc,fill=index)) +
  geom_col_pattern(aes(pattern = index, pattern_angle = index, 
                       pattern_spacing = index),
                       fill = 'white',colour= 'black', 
                       pattern_density = 0.35, 
                       pattern_fill    = 'black',
                       pattern_colour  = 'black')+
  geom_label(aes(label = labels),size=5,
             position = position_stack(vjust = 0.5),
             show.legend = FALSE) +
  scale_fill_manual(values = colors) +
  coord_polar(theta = "y") +
  xlim(c(0.1, hsize + 0.5)) +
  theme_void()
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-49 不同样式环形图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
	   