
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(ggstatsplot)

###############################图7-10-1 (a) ggcoefstats() 函数模型评估可视化示例1

## model
mod1 <- stats::lm(formula = mpg ~ am * cyl, data = mtcars)
ggcoefstats(mod1,stats.label.args = list(family="serif")) +
  #theme(text = element_text(family = "serif"))
  theme(
        text = element_text(family = "serif",size = 15),
        axis.text = element_text(colour = "black",size = 14),
        axis.title = element_text(colour = "black",face = "bold",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))

ggsave(filename = "\\第7章 其他类型图的绘制\\图7-10-1 ggcoefstats()函数模型评估可视化示例_a.png",
       width =5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-10-1 ggcoefstats()函数模型评估可视化示例_a.pdf",
       width =5, height = 4.5,device = cairo_pdf)

###############################图7-10-1 (b) ggcoefstats() 函数模型评估可视化示例2
#读取数据
file <- "\\第7章 其他类型图的绘制\\ggcoefstats_data.csv"
ggcoefstats_data <- readr::read_csv(file)

ggcoefstats(x = ggcoefstats_data,statistic = "z",
           stats.label.args = list(family="serif")) +
theme(
        text = element_text(family = "serif",size = 15),
        axis.text = element_text(colour = "black",size = 14),
        axis.title = element_text(colour = "black",face = "bold",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))

ggsave(filename = "\\第7章 其他类型图的绘制\\图7-10-1 ggcoefstats()函数模型评估可视化示例_b.png",
       width =5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-10-1 ggcoefstats()函数模型评估可视化示例_b.pdf",
       width =5, height = 4.5,device = cairo_pdf)
