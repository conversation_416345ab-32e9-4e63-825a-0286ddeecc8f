
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(igraph)
library(ggraph)
library(tidygraph)
## the custom function using Color Brewer
cols <- colorRampPalette(RColorBrewer::brewer.pal(11, 'Spectral'))
## make the graph
g <- erdos.renyi.game(50, .1) 
# provide some names
V(g)$name <- 1:vcount(g)
# plot using ggraph
graph_tbl <- g %>% 
  as_tbl_graph() %>% 
  activate(nodes) %>% 
  mutate(degree  = centrality_degree()) 
layout <- create_layout(graph_tbl, layout = 'igraph', algorithm = 'nicely')

###########################################图7-14-3(a) 使用ggraph绘制的网络图示例（alpha）

ggraph(layout) +
  geom_edge_fan(aes(color = as.factor(from), 
                    alpha = after_stat(index)),show.legend = F) +
  geom_node_point(aes(size = degree, fill = as.factor(name)),
                shape=21,show.legend = F) +
  scale_fill_manual(
    limits = as.factor(layout$name),
    values = cols(nrow(layout))) +
  scale_edge_color_manual(
    limits = as.factor(layout$name),
    values = cols(nrow(layout))) +
  coord_equal() 
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-14-3 使用ggraph绘制的不同样式网络图示例_a.png",
       width =6, height = 6, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-14-3 使用ggraph绘制的不同样式网络图示例_a.pdf",
       width =6, height = 6,device = cairo_pdf)


###########################################图7-14-3(b) 使用ggraph绘制的网络图示例（指示箭头样式）

ggraph(layout) +
  geom_edge_arc(aes(color = as.factor(from)), 
             end_cap = circle(2.5, 'mm'),
             arrow = arrow(length =unit(2.5,'mm')),
        strength = 0.1,
        show.legend = F) +
  geom_node_point(aes(size = degree, fill = as.factor(name)),
                shape=21,show.legend = F) +
  scale_fill_manual(
    limits = as.factor(layout$name),
    values = cols(nrow(layout))) +
  scale_edge_color_manual(
    limits = as.factor(layout$name),
    values = cols(nrow(layout))) +
  coord_equal() 
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-14-3 使用ggraph绘制的不同样式网络图示例_b.png",
       width =6, height = 6, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-14-3 使用ggraph绘制的不同样式网络图示例_b.pdf",
       width =6, height = 6,device = cairo_pdf)


