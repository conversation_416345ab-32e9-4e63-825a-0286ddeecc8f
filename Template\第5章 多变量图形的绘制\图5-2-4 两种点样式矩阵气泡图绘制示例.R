"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)


#构建数据
matrix = read_excel("\\第5章 多变量图形的绘制\\矩阵气泡图数据.xlsx")
matrix_long <- matrix %>% reshape2::melt()
											  												 												  
####################################图5-2-4（a）“宽”数据等值线图绘制示例1
colors <- rev(RColorBrewer::brewer.pal(11, "Spectral"))
ggplot(data = matrix_long,aes(x=columns,y=variable)) + 
  geom_point(aes(size=value,fill=value),shape=21) +
  scale_size(range = c(2,8)) +
  scale_fill_gradientn(colours = colors) +
  labs(x='', y='') +
  theme(text = element_text(family = "serif",size = 13),
        axis.text = element_text(colour = "black",size = 12))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-4 两种点样式矩阵气泡图绘制示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-4 两种点样式矩阵气泡图绘制示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
	   
####################################图5-2-4（b）“宽”数据等值线图绘制示例2

ggplot(data = matrix_long,aes(x=columns,y=variable)) + 
  geom_point(aes(size=value,fill=value),shape=22) +
  scale_size(range = c(2,8)) +
  scale_fill_gradientn(colours = parula(100)) +
  labs(x='', y='') +
  theme(text = element_text(family = "serif",size = 13),
        axis.text = element_text(colour = "black",size = 12))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-4 两种点样式矩阵气泡图绘制示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-4 两种点样式矩阵气泡图绘制示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
   