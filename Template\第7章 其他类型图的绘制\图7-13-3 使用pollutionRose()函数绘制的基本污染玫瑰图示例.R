
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(openair)

png(file="\\第7章 其他类型图的绘制\\图7-13-3 使用pollutionRose()函数绘制的基本污染玫瑰图示例.png",
    width = 9500, height = 4000,res=1000)
par(mar = rep(2,4))
pollutionRose(mydata,pollutant = "nox",type = "so2",layout = c(4, 1),cols=parula(8),
              key.position = "bottom")
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-13-3 使用pollutionRose()函数绘制的基本污染玫瑰图示例.pdf",
    width = 9.5, height = 40)
par(mar = rep(2,4))
pollutionRose(mydata,pollutant = "nox",type = "so2",layout = c(4, 1),cols=parula(8),
              key.position = "bottom")
dev.off()
