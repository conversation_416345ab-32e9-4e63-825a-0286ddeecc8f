"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)
library(ggforce)


#构建数据集

sina_data <- read_excel("\\第4章 双变量图形的绘制\\小提琴图数据.xlsx")
								
####################################图4-1-45（a）无密度轮廓的密度缩放抖动图
ggplot(data = sina_data,aes(x=class,y=values)) +
     ggforce::geom_sina(aes(color=class)) +
     ggsci::scale_color_lancet() +
     scale_y_continuous(expand = c(0, 0),limits = c(30,60),
                        breaks = seq(30,60,10)) +
     theme_classic() +
     theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(0.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-45（b）有密度轮廓的密度缩放抖动图

ggplot(data = sina_data,aes(x=class,y=values)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    ggforce::geom_sina(aes(color=class)) +
    scale_y_continuous(expand = c(0, 0),limits = c(30,60),
                       breaks = seq(30,60,10)) +
    ggsci::scale_color_lancet() +
    theme_classic() +
    theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-45（c）带统计P值的密度缩放抖动图
comparisons <- list( c("cluster1", "cluster2"), 
                     c("cluster1", "cluster3"), 
                     c("cluster2", "cluster3"))
					 
ggplot(data = sina_data,aes(x=class,y=values)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    ggforce::geom_sina(aes(color=class)) +
    #添加显著性性P值
    ggpubr::stat_compare_means(aes(label = after_stat(p.signif)),
                               comparisons = comparisons, 
                               label.y = c(62, 66, 70),
                               tip.length=0,size=6,vjust=0.5) +
    ggsci::scale_color_lancet() +
    theme_classic() +
    theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-45 不同样式密度缩放抖动图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)