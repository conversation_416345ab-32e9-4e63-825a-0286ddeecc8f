"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggdendro)
library(patchwork)


#读取数据
cluster_01 <- read_excel("\\第4章 双变量图形的绘制\\cluster_heatmap_01.xlsx")
cluster_01_long  <- cluster_01 %>% 
        tidyr::pivot_longer(!Index,cols_vary = "slowest")
		
cluster_01_wider <- cluster_01_long %>% pivot_wider()  %>% 
   column_to_rownames(var = "Index") 		
		

color_set <- colorRampPalette(c("navy", "white", "red"))(50)

heatmap_gg <- ggplot(data = cluster_01_long,aes(x = Index, y=name, fill=value)) +
  geom_tile(colour="black",linewidth=0.2) +
  #geom_tile(data = rect_data,aes(x=x,y=y,fill=fill)) +
  #scale_fill_gradientn(colours = colors) +
  scale_fill_gradientn(colours = color_set,
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour=NA)) +
  labs(x="",y="")+
  theme_void() +
  theme(#legend.position = "top",
        legend.key.width = unit(0.6, "lines"),
        legend.key.height = unit(0.8, "lines"),
        text = element_text(family = "serif",face='bold',),
        axis.text = element_text(colour = "black",face='bold'),
        axis.text.x = element_text(angle = 90,hjust=1,vjust=0.5),
        axis.text.y = element_text(hjust=1),
        #显示更多刻度内容
        plot.margin = margin(0, 5, 5, 5)) 
		
#聚类模型
model <- cluster_01_wider %>% dist() %>% hclust()

# Rectangular lines
ddata <- dendro_data(model, type = "rectangle")

dendrogram <- ggplot(segment(ddata)) + 
  geom_segment(aes(x = x, y = y, xend = xend, yend = yend),
              linewidth=0.3) +
  scale_x_continuous(expand = c(0, 0.5)) +
  theme_void() +
  #coord_equal() +
  theme(
        #显示更多刻度内容
        plot.margin = margin(5, 0, -10, 5)) 

dendrogram / heatmap_gg + plot_layout(heights = c(1,3))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-30 使用ggplot2绘制的聚类热力图示例.png",
       width =7.5, height = 3.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-30 使用ggplot2绘制的聚类热力图示例.pdf",
       width =7.5, height = 3.5,device = cairo_pdf)
		
