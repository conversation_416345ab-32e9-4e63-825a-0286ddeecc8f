"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-14（a）散点大小（size）度量调整

ggplot(data = tips,aes(x = total_bill,y = tip,size=size))+
  geom_point(shape=21,fill="#0073C2") +
  labs(x="X Label", y = "Y Label")+
  scale_size(name="Size",range = c(1,10),guide = guide_legend(order = 1,nrow = 1,
                                            label.position = "top",
                                            title.position = "top"))+
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = c(.35,.9),
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5,vjust =-2),
        legend.title.align = .5,
        legend.margin = margin(.1, .1, .1, .1),
        legend.direction = "horizontal",
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_a.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_a.pdf",
       width = 4.2, height = 4,device = cairo_pdf)

###############################################图2-2-14（b）散点大小（size）和填充颜色（fill）度量调整

ggplot(data = tips,aes(x = total_bill,y = tip,size=size,fill=size))+
  geom_point(shape=21) +
  labs(x="X Label", y = "Y Label")+
  scale_size(name="Size",range = c(1,8),guide = guide_legend(order = 1,nrow = 1,
                                            label.position = "top"))+
  scale_fill_distiller(name="Size",palette = "Blues",direction = 1,
                       guide=guide_colorbar(label.position = "top",
                                            order = 2))+
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
 theme(legend.position = c(.4,.86),
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5),
        legend.margin = margin(.1, .1, .1, .1),
        legend.spacing.y = unit(.25, "cm"),
        legend.direction = "horizontal",
        legend.key.width = unit(.7, "cm"),
        legend.key.height = unit(.4,"cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_b.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_b.pdf",
       width = 4.2, height = 4,device = cairo_pdf)
	   
###############################################图2-2-14（c）散点填充颜色（fill）和形状（shape）度量调整
	  ggplot(data = tips,aes(x = total_bill,y = tip,fill=factor(day),shape=factor(day)))+
  geom_point(alpha=.9,size=5) +
  labs(x="X Label", y = "Y Label") +
  ggsci::scale_fill_jco(name="Day",guide=guide_legend(label.position = "bottom",
                                                      title.position="left")) +
  scale_shape_manual(name="Day",values=c(21,22,23,24)) +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = c(.35,.85),
        legend.direction = "horizontal",
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =2),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 13),
        legend.title.align = .5,
        #legend.spacing.y = unit(-.2, "cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_c.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_c.pdf",
       width = 4.2, height = 4,device = cairo_pdf) 

###############################################图2-2-14（d）散点大小（size）和填充颜色（fill）度量调整
ggplot(data = tips,aes(x = total_bill,y = tip,size=size,fill=day))+
  geom_point(shape=21) +
  labs(x="X Label", y = "Y Label")+
  scale_size(name="Size",range = c(1,10),guide = guide_legend(order = 1,nrow = 1,
                                                 label.position = "top",
                                                 title.position = "top"))+
  scale_fill_manual(name="Day",values = c("#0073C2","#EFC000","#868686","#CD534C"),
                    guide=guide_legend(label.position = "bottom",
                                                      title.position="left",
                                                      override.aes=list(size=5))) + 
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = c(.4,.86),
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =1),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5),
        legend.margin = margin(.1, .1, .1, .1),
        legend.spacing.y = unit(.25, "cm"),
        legend.direction = "horizontal",
        legend.key.width = unit(.7, "cm"),
        legend.key.height = unit(.4,"cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_d.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-14 不同美学映射度量调整绘制示例_d.pdf",
       width = 4.2, height = 4,device = cairo_pdf)
	   
	   