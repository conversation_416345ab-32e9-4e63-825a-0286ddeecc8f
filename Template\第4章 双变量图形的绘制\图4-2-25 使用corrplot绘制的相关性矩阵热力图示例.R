"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(Cairo)

library(corrplot)


#读取数据
heatmap_data <- read_excel("\\第4章 双变量图形的绘制\\相关性热力图_P值.xlsx")

corr <- round(cor(heatmap_data), 2)
testRes = cor.mtest(heatmap_data, conf.level = 0.95)

				  
####################################图4-2-25（a）使用corrplot绘制的相关性矩阵热力图示例（pie）

Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_a.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot(corr, method = 'pie')
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_a.pdf",
               width = 5.2, height = 4.2)
corrplot(corr, method = 'pie')
dev.off()

	   
####################################图4-2-25（b）使用corrplot绘制的相关性矩阵热力图示例（pie+shade）

Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_b.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot.mixed(corr, lower = 'shade', upper = 'pie', order = 'hclust')
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_b.pdf",
               width = 5.2, height = 4.2)
corrplot.mixed(corr, lower = 'shade', upper = 'pie', order = 'hclust')
dev.off()
	   
####################################图4-2-25（c）使用corrplot绘制的相关性矩阵热力图示例（P值+order）

Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_c.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot(corr, p.mat = testRes$p, sig.level = 0.10, order = 'hclust', 
         addrect = 2)
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_c.pdf",
               width = 5.2, height = 4.2)
corrplot(corr, p.mat = testRes$p, sig.level = 0.10, order = 'hclust', 
         addrect = 2)
dev.off()

####################################图4-2-25（d）使用corrplot绘制的相关性矩阵热力图示例（label_sig）
Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_d.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot(corr, p.mat = testRes$p, method = 'color', 
         sig.level = c(0.001, 0.01, 0.05), pch.cex = 0.9,
         insig = 'label_sig', pch.col = 'grey20', order = 'AOE')
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_d.pdf",
               width = 5.2, height = 4.2)
corrplot(corr, p.mat = testRes$p, method = 'color', 
         sig.level = c(0.001, 0.01, 0.05), pch.cex = 0.9,
         insig = 'label_sig', pch.col = 'grey20', order = 'AOE')
dev.off()

####################################图4-2-25（e）使用corrplot绘制的相关性矩阵热力图示例（confidence interval）

Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_e.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot(corr, lowCI = testRes$lowCI, uppCI = testRes$uppCI, order = 'hclust',
         tl.pos = 'd', rect.col = 'navy', plotC = 'rect', cl.pos = 'n')
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_e.pdf",
               width = 5.2, height = 4.2)
# Visualize confidence interval
corrplot(corr, lowCI = testRes$lowCI, uppCI = testRes$uppCI, order = 'hclust',
         tl.pos = 'd', rect.col = 'navy', plotC = 'rect', cl.pos = 'n')
dev.off()
	   
####################################图4-2-25（f）使用corrplot绘制的相关性矩阵热力图示例（confidence interval+ p.mat）	
Cairo::CairoPNG(filename = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_f.png",
               width = 5.2, height = 4.2, units = "in",dpi = 300)
corrplot(corr, p.mat = testRes$p, lowCI = testRes$lowCI, uppCI = testRes$uppCI,
         addrect = 3, rect.col = 'navy', plotC = 'rect', cl.pos = 'n')
dev.off()

pdf(file = "\\第4章 双变量图形的绘制\\图4-2-25 使用corrplot绘制的相关性矩阵热力图示例_f.pdf",
               width = 5.2, height = 4.2)
# Visualize confidence interval
corrplot(corr, p.mat = testRes$p, lowCI = testRes$lowCI, uppCI = testRes$uppCI,
         addrect = 3, rect.col = 'navy', plotC = 'rect', cl.pos = 'n')
dev.off()
	   
