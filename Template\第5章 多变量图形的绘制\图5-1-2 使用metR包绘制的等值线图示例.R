"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


#构建数据
df <- data.frame(x=rep(seq(0,10,by=.1),each=101),
                  y=rep(seq(10,20,by=.1),serif=101))
df$z <- ((0.1*df$x^2+df$y)-10)/20

											  												 												  
####################################图5-1-2（a）使用metR添加数值文本等值线图示例

ggplot(df, aes(x, y, z = z))+
  geom_tile(aes(fill=z))+
  stat_contour(colour="black") +
  metR::geom_text_contour(nudge_x = 0.5,nudge_y = -0.5,size=5,family="serif") +
  labs(x='X Axis title', y='Y Axis title') +
  scale_fill_gradientn(colours = parula(100)) +
  scale_x_continuous(expand = c(0,0)) +
  scale_y_continuous(expand = c(0,0)) +
  theme_minimal() +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size=12),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-2 使用metR包绘制的等值线图示例_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-2 使用metR包绘制的等值线图示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)
	   
####################################图5-1-2（b）使用geom_contour_fill() 绘制的函数等值线图示例

ggplot(df, aes(x, y, z = z))+
  metR::geom_contour_fill() +
  geom_contour(color = "black", linewidth = 0.5) +
  metR::geom_text_contour(nudge_x = 0.5,nudge_y = -0.5,size=5,family="serif") +
  labs(x='X Axis title', y='Y Axis title') +
  scale_fill_gradientn(colours = parula(100)) +
  scale_x_continuous(expand = c(0,0)) +
  scale_y_continuous(expand = c(0,0)) +
  theme_minimal() +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size=12),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-2 使用metR包绘制的等值线图示例_b.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-2 使用metR包绘制的等值线图示例_b.pdf",
       width =5, height = 4,device = cairo_pdf)
   