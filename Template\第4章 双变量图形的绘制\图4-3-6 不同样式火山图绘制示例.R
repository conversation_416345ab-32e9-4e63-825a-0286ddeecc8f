"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(readxl)
library(ggVolcano)

data(deg_data)

deg_data <- deg_data %>% 
  mutate(
    Expression = case_when(log2FoldChange >= log(2) & padj <= 0.05 ~ "Up-regulated",
                           log2FoldChange <= -log(2) & padj <= 0.05 ~ "Down-regulated",
                           TRUE ~ "Unchanged")
    )


###################################图4-3-6 (a) 使用ggpolot2绘制的基本火山图示例
ggplot(data=deg_data,aes(x = log2FoldChange,y = -log10(padj))) + 
  geom_point(aes(color = Expression)) +
  geom_hline(yintercept = -log10(0.05),
             linetype = "dashed") + 
  geom_vline(xintercept = c(log2(0.5), log2(2)),
             linetype = "dashed") +   
  xlab(expression("log"[2]*"FC")) + 
  ylab(expression("-log"[10]*"FDR")) +
  scale_color_manual(values = c("#3B4992", "gray50", "#EE0000")) +
  theme_bw() +
  theme(legend.position = c(0.2,0.8),
        legend.text = element_text(size = 11),
        legend.title = element_text(size = 13),
        text = element_text(family = "serif",size = 15),
        axis.text = element_text(colour = "black",size = 13),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-6 (b) 使用ggvolcano绘制的基本火山图示例

data <- add_regulate(deg_data, log2FC_name = "log2FoldChange",
                     fdr_name = "padj",log2FC = 1, fdr = 0.05)

ggvolcano(data, x = "log2FoldChange", y = "padj",
          label = "row", label_number = 10, output = FALSE) +
   ggsci::scale_color_aaas()+
   ggsci::scale_fill_aaas() 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-6 (c) 使用ggplot2绘制的渐变火山图示例
colors <- rev(RColorBrewer::brewer.pal(11, "Spectral"))


ggplot(data=deg_data,aes(x = log2FoldChange,y = -log10(padj),)) + 
  geom_point(aes(color=-log10(padj),size=-log10(padj))) +
  geom_hline(yintercept = -log10(0.05),
             linetype = "dashed") + 
  geom_vline(xintercept = c(log2(0.5), log2(2)),
             linetype = "dashed") +   
  xlab(expression("log"[2]*"FC")) + 
  ylab(expression("-log"[10]*"FDR")) +
  scale_color_gradientn(colors = colors) +
  scale_size(range = c(0.5, 4)) +
  guides(size="none") +
  theme_bw() +
  theme(legend.position = c(0.2,0.75),
        legend.text = element_text(size = 11),
        legend.title = element_text(size = 13),
        text = element_text(family = "serif",size = 15),
        axis.text = element_text(colour = "black",size = 13),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid=element_blank(),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-6 (d) 使用plotROC绘制的多ROC曲线示例2（置信区间）
gradual_volcano(deg_data, x = "log2FoldChange", y = "padj",
                label = "row", label_number = 10, output = FALSE) +
  scale_color_gradientn(colours = parula(100)) +
  scale_fill_gradientn(colours = parula(100)) #+
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-6 不同样式火山图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
	   
	   