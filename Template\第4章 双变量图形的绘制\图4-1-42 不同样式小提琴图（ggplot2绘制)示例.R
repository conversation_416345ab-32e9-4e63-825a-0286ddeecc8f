"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)


#构建数据集

violin_data <- read_excel("G:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\小提琴图数据.xlsx")


									

####################################图4-1-42（a）使用ggplot2绘制的基础小提琴图
ggplot(data = violin_data,aes(x=class,y=values,fill=class)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    ggsci::scale_fill_jco() +
#   stat_boxplot(geom = "errorbar",width = 0.4,linewidth=0.4) + 
     theme_classic() +
     theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-42（b）使用ggplot2绘制的误差小提琴图

ggplot(data = violin_data,aes(x=class,y=values,fill=class)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    stat_summary(fun.data = 'mean_sd', geom = "pointrange",
                 size = 1) + 
    ggsci::scale_fill_jco() +
     theme_classic() +
     theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-42（c）使用ggplot2绘制的箱线小提琴图
ggplot(data = violin_data,aes(x=class,y=values,fill=class)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    geom_boxplot(linewidth=0.4,width=0.3,fill="white") +
    ggsci::scale_fill_jco() +
     theme_classic() +
     theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-42（d）使用ggplot2绘制的散点小提琴图
ggplot(data = violin_data,aes(x=class,y=values,fill=class)) +
    geom_violin(trim = FALSE,linewidth=0.3) +
    geom_jitter(width = 0.2,alpha=0.6) +
    ggsci::scale_fill_jco() +
     theme_classic() +
     theme(legend.position = "none",
           text = element_text(family = "serif",face='bold',size = 18),
           axis.text = element_text(colour = "black",face='bold',size = 15),
           axis.ticks.length=unit(.2, "cm"),
           #显示更多刻度内容
           plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-42 不同样式小提琴图（ggplot2绘制)示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   