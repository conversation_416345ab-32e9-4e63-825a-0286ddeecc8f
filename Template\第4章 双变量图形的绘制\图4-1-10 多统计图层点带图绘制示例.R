"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
strip_data_01 <- read_excel("\\第4章 双变量图形的绘制\\strip data 01.xlsx")
#数据转换
strip_01_long <- strip_data_01 %>% 
  tidyr::pivot_longer(cols = everything(),names_to = "Type", values_to = "Values")

palette <- c("#323232","#1B6393","#FCD351","#C7E3CC")
										

####################################图4-1-10（a）带均值柱形点带图样式 
ggplot(data = strip_01_long,aes(x = Type,y=Values)) +
#添加统计柱形图
 stat_summary(aes(colour=Type),fun="mean",fill=NA,width=0.8,
              geom='bar',size=0.8) +
 geom_jitter(aes(fill=Type),shape=21,size=4,stroke = 0.5,width = 0.2) +
 #添加
 stat_summary(fun.data="mean_se",colour="black",width=0.3,
              geom='errorbar',size=0.5) +
  #添加统计图层:均值横线
# ungeviz::geom_hpline(stat = "summary", width = 0.6, size = 1.5)+
  scale_y_continuous(expand = c(0, 0),limits = c(0, 1)) +
  scale_fill_manual(values = palette) +
  scale_colour_manual(values = palette) +
  labs(x="",y="Values") +
  theme_classic()+
  theme(legend.position="none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-10 多统计图层点带图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-10 多统计图层点带图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-10（b）带P值、均值柱形点带图样式
library(ggsignif)

ggplot(data = strip_01_long,aes(x = Type,y=Values)) +
#添加统计柱形图
 stat_summary(aes(colour=Type),fun="mean",fill=NA,width=0.8,
              geom='bar',size=0.8) +
 geom_jitter(aes(fill=Type),shape=21,size=4,stroke = 0.5,width = 0.2) +
 #添加误差线他图层
 stat_summary(fun.data="mean_se",colour="black",width=0.3,
              geom='errorbar',size=0.5) +
  #添加显著性P值
 ggsignif::geom_signif(comparisons = list(c("NS", "SMK"), 
                                          c("SMK", "SMK_abx")),
                       map_signif_level = TRUE,textsize = 6,
                       margin_top = 0.1,step_increase = 0.1,
                       tip_length = 0.0) +
  scale_y_continuous(expand = c(0, 0),breaks = seq(0,1.2,0.2),
                     limits = c(0, 1.2)) +
  scale_fill_manual(values = palette) +
  scale_colour_manual(values = palette) +
  labs(x="",y="Values") +
  theme_classic()+
  theme(legend.position="none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-10 多统计图层点带图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-10 多统计图层点带图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
