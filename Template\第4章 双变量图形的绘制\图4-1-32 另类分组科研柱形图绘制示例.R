"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggsignif)

#构建数据集

paper_group_bar03 <- read_excel("\\第4章 双变量图形的绘制\\paper_group_bar_data 03.xlsx")
paper_group_bar03 <- paper_group_bar03 %>% mutate(index=paste0(name,"_",type))

#修改变量顺序
paper_group_bar03$type <- factor(paper_group_bar03$type, 
                levels=c("SCs","FAPs"))
paper_group_bar03$name <- factor(paper_group_bar03$name, 
                levels=c("ROS low","ROS low +NAC","ROS high","ROS high +NAC"))


paper_group_bar03$index <- factor(paper_group_bar03$index, 
                levels=c('ROS low_SCs','ROS low +NAC_SCs','ROS high_SCs','ROS high +NAC_SCs',
                         'ROS low_FAPs','ROS low +NAC_FAPs','ROS high_FAPs','ROS high +NAC_FAPs'))
									

ggplot(data = paper_group_bar03,aes(x=index,y=value)) +
  #误差线
  stat_summary(fun.data = 'mean_se', geom = "errorbar", 
               width = 0.3,linewidth=0.25)+
  #柱状图
  geom_bar(aes(fill=name),color="black",stat="summary",
           fun=mean,linewidth=0.5,width=0.5) +
  #散点图
  geom_jitter(color="black",size = 2,width = 0.2)+
  ggsignif::geom_signif(y_position = 21,xmin =1,xmax =3,
                       annotation = c("P=0.003"), 
                       tip_length = c(0.85, 0.05),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 18,xmin =2,xmax =3,
                       annotation = c("P=0.070"), 
                       tip_length = c(0.55, 0.05),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 23,xmin =3,xmax =4,
                       annotation = c("P=0.012"), 
                       tip_length = c(0.1, 0.9),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 16,xmin =5,xmax =7,
                       annotation = c("P=0.056"), 
                       tip_length = c(0.6, 0.1),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 12,xmin =6,xmax =7,
                       annotation = c("P=0.052"), 
                       tip_length = c(0.4, 0.1),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 18,xmin =7,xmax =8,
                       annotation = c("P=0.040"), 
                       tip_length = c(0.1, 0.7),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  labs(x="",y="Mean value") +
  guides(fill = guide_legend(nrow = 2,byrow = TRUE)) +
  #颜色
  scale_fill_manual(values = c("#DCDBE1","#74B1E0","#A2A7AA","#87D0BF")) +
  scale_x_discrete(labels = c("","SCs","","",
                                 "","FAPS","","")) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,25),
                     breaks = seq(0,25,5)) +
  theme_classic()+
  theme(legend.position = "bottom",
        legend.title = element_blank(),
        legend.margin = margin(-20, 6, 0, 0),
        legend.text = element_text(size = 12),
        strip.background = element_blank(),
        strip.text = element_text(color = "black",size = 18),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 17,
                                     hjust = -0.2),
        axis.ticks.length=unit(.2, "cm"),
        #删除x轴刻度
        axis.ticks.x = element_blank(),
        #axis.text.x=element_blank(),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-32 另类分组科研柱形图绘制示例.png",
       width =6, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-32 另类分组科研柱形图绘制示例.pdf",
       width =6, height = 4.5,device = cairo_pdf)
	   
	   