function plotDisplacementForce(displacement1, force1, displacement2, force2, displacement3, force3)
    % 创建新的图形窗口
    figure;
    set(gcf, 'Position', [300, 300, 800, 600]);  % 设置图像的位置和大小

    % 保持窗口，绘制多条曲线
    hold on;
    plot(displacement1, force1, '-o', 'DisplayName', 'Group 1');  % 第一组
    plot(displacement2, force2, '-s', 'DisplayName', 'Group 2');  % 第二组
    plot(displacement3, force3, '-d', 'DisplayName', 'Group 3');  % 第三组
    
    % 设置图形属性
    ax = gca;
    ax.FontSize = 18; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [0 4];
    ax.XLim = [0 70];
    yticks([0 1 2 3 4]); % 自定义纵坐标刻度
    xlabel('Distance (mm)', 'FontSize', 20, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Peeling Force (N)', 'FontSize', 20, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title('2733+Silbione', 'FontSize', 20, 'FontName', 'Times New Roman', 'FontWeight', 'bold');

    % 打开上、右边的边框
    box on;

    % legend show;
    % grid on;

    % 完成绘图
    hold off;
end
