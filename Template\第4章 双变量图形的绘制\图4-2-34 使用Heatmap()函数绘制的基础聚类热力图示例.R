"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggplotify)
library(ComplexHeatmap)


#读取数据
cluster_01 <- read_excel("\\第4章 双变量图形的绘制\\cluster_heatmap_01.xlsx")

cluster_01 <- cluster_01 %>% column_to_rownames(var = "Index")
#转置矩阵
cluster_01_mat <- as.matrix.data.frame(cluster_01) %>% t()

col2 <- parula(50)

####################################图4-2-34（a）使用Heatmap()绘制的函数基础聚类热力图示例

Com_hm_gg1<-as.ggplot(
        Heatmap(cluster_01_mat,col = col2,name = "value",
                rect_gp = gpar(col = "black", lwd = 0.5),
                )
         )
Com_hm_gg1
ggsave(Com_hm_gg1,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_a.png",
       width =7.5, height = 3, bg="white",dpi = 900,device=png)
ggsave(Com_hm_gg1,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_a.pdf",
       width =7.5, height = 3,device = cairo_pdf)
	   
####################################图4-2-34（b）在Heatmap()函数中设置column_km参数绘制的聚类热力图示例
Com_hm_gg2<-as.ggplot(
       Heatmap(cluster_01_mat,col = col2,name = "value",
        column_km = 3,
        rect_gp = gpar(col = "black", lwd = 0.5))
         )
ggsave(Com_hm_gg2,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_b.png",
       width =7, height = 3, bg="white",dpi = 900,device=png)
ggsave(Com_hm_gg2,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_b.pdf",
       width =7, height = 3,device = cairo_pdf)

####################################图4-2-34（c）在Heatmap()函数中设置row/column_km参数绘制的聚类热力图示例
Com_hm_gg3<-as.ggplot(
    Heatmap(cluster_01_mat,col = col2,name = "value",
        row_km = 3,column_km = 3,
        rect_gp = gpar(col = "black", lwd = 0.5),)
         )
ggsave(Com_hm_gg3,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_c.png",
       width =7, height = 3, bg="white",dpi = 900,device=png)
ggsave(Com_hm_gg3,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_c.pdf",
       width =7, height = 3,device = cairo_pdf)


####################################图4-2-34（d）在Heatmap()函数中设置row/column_gap绘制的参数聚类热力图示例	   
	   
Com_hm_gg4<-as.ggplot(
  Heatmap(cluster_01_mat,col = col2,name = "value",border = TRUE,
        row_km = 3,column_km = 3,column_gap = unit(3, "mm"),
        row_gap = unit(3, "mm"),
        rect_gp = gpar(col = "black", lwd = 0.5))
         )
ggsave(Com_hm_gg4,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_d.png",
       width =7, height = 3, bg="white",dpi = 900,device=png)
ggsave(Com_hm_gg4,filename = "\\第4章 双变量图形的绘制\\图4-2-34 使用Heatmap()函数绘制的基础聚类热力图示例_d.pdf",
       width =7, height = 3,device = cairo_pdf)


