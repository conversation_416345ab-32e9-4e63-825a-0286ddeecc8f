"""
测试时间：2024年05月10日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)
library(ggforce)
library(ggrain)
library(reshape2)

#构建数据集

sina_data <- read_excel("\\第4章 双变量图形的绘制\\小提琴图数据.xlsx")
								
####################################图4-1-46（a）使用ggrain绘制的云雨图示例1
ggplot(iris, aes(1, Sepal.Width)) +
  geom_rain(fill="gray") +
  theme_classic() +
  coord_flip() +
  theme(legend.position = "none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        axis.title.x = element_blank(), 
        axis.text.x = element_blank(), 
        axis.ticks.x = element_blank(),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-46（b）使用ggrain绘制的云雨图示例2

ggplot(iris, aes(1, Sepal.Width, fill = Species,color = Species)) +
  geom_rain(alpha = 0.6,
            boxplot.args = list(color = "black", 
                                outlier.shape = NA)) +
  ggsci::scale_fill_aaas() +
  ggsci::scale_color_aaas() +
  labs(x="") +
  theme_classic() +
  theme(legend.position = "top",
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-46（c）使用ggrain绘制的云雨图示例3
ggplot(iris, aes(x = Species, y = Sepal.Length, fill = 	Species)) +
   geom_rain(rain.side = 'l',alpha = 0.6) +
   ggsci::scale_fill_aaas() +
   labs(x="") +
   theme_classic() +
   theme(legend.position = "none",
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-46（d）使用ggrain绘制的云雨图示例4	   
ggplot(iris, aes(x = Species, y = Sepal.Length, fill = Species)) +
   geom_rain(alpha = 0.6,
            boxplot.args.pos = list(
              width = 0.05, position = position_nudge(x = 0.13)),
            violin.args.pos = list(
              side = "r",
              width = 0.7, position = position_nudge(x = 0.2))) +
   ggsci::scale_fill_aaas() +
   labs(x="") +
   theme_classic() +
   coord_flip() +
   theme(legend.position = "none",
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-46 不同样式云雨图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   