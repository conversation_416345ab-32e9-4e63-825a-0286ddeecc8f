
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)
library(grafify)


#读取数据
ba_data <- read_excel("\\第7章 其他类型图的绘制\\前后图数据.xlsx")

#数据处理：
ba_data <- ba_data %>% pivot_longer(cols = !Subject)


####################################图7-2-2（a）前后图基础样式绘制

grafify::plot_befafter_colours(data = ba_data,xcol = name,ycol = value,match = Subject,
                              symsize = 5)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_a.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_a.pdf",
       width =5.5, height = 4,device = cairo_pdf)

####################################图7-2-2（b）前后图样式（箱线图）

grafify::plot_befafter_colours(data = ba_data,xcol = name,ycol = value,match = Subject,
                              symsize = 5,Boxplot = TRUE)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_b.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_b.pdf",
       width =5.5, height = 4,device = cairo_pdf)
	   
####################################图7-2-2（c）前后图样式（分面）
plot_befafter_colours(data_2w_Tdeath,Time,PI,Experiment,
                      Genotype,    #facet argument
                      Boxplot = TRUE, 
                      symsize = 3)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_c.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_c.pdf",
       width =5.5, height = 4,device = cairo_pdf)

####################################图7-2-2（d）前后图样式（形状）   
plot_befafter_shapes(data_1w_death,
                     Genotype,
                     Death,
                     Experiment, 
                     symsize = 4)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_d.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-2 使用grafify绘制的前后图示例_d.pdf",
       width =5.5, height = 4,device = cairo_pdf)	   


