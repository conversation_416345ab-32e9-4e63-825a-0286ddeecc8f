# R语言最佳实践指南

## 代码风格和组织

### 命名规范
- **变量名**: 使用snake_case，如 `data_clean`, `model_results`
- **函数名**: 使用snake_case，如 `clean_data()`, `build_model()`
- **常量**: 使用UPPER_CASE，如 `MAX_ITERATIONS`, `DEFAULT_ALPHA`
- **文件名**: 使用连字符，如 `data-analysis.R`, `model-evaluation.R`

### 代码结构
```r
# 1. 加载包
library(tidyverse)
library(ggplot2)

# 2. 设置全局选项
options(stringsAsFactors = FALSE)

# 3. 定义函数
clean_data <- function(data) {
  # 函数实现
}

# 4. 主要分析代码
# 数据加载
# 数据清洗
# 分析和建模
# 结果输出
```

## 数据处理最佳实践

### 数据导入
- 使用`readr`包的函数而非base R的`read.csv()`
- 明确指定列类型：`col_types = cols()`
- 处理缺失值：`na = c("", "NA", "NULL")`

### 数据清洗
- 使用`dplyr`进行数据操作
- 保持原始数据不变，创建清洗后的副本
- 记录所有数据转换步骤

```r
# 推荐的数据清洗流程
data_clean <- raw_data %>%
  filter(!is.na(key_variable)) %>%
  mutate(
    new_variable = case_when(
      condition1 ~ value1,
      condition2 ~ value2,
      TRUE ~ default_value
    )
  ) %>%
  select(relevant_columns)
```

## 统计分析最佳实践

### 探索性数据分析
1. **数据概览**: `summary()`, `str()`, `glimpse()`
2. **缺失值检查**: `VIM::aggr()`, `naniar::vis_miss()`
3. **分布检查**: 直方图、箱线图、Q-Q图
4. **相关性分析**: `corrplot::corrplot()`

### 假设检验
- 检查假设条件（正态性、方差齐性等）
- 选择合适的检验方法
- 报告效应量，不仅仅是p值
- 考虑多重比较校正

### 模型构建
- 划分训练集和测试集
- 使用交叉验证评估模型
- 检查模型假设和诊断图
- 报告模型性能指标

## 可视化最佳实践

### ggplot2原则
- 使用一致的主题：`theme_minimal()`, `theme_classic()`
- 合理的颜色选择：`RColorBrewer`, `viridis`
- 清晰的标签和标题
- 适当的图例位置

```r
# 标准ggplot模板
ggplot(data, aes(x = x_var, y = y_var)) +
  geom_point(alpha = 0.7) +
  labs(
    title = "清晰的标题",
    subtitle = "副标题（如需要）",
    x = "X轴标签",
    y = "Y轴标签",
    caption = "数据来源"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 10)
  )
```

## 项目管理

### 目录结构
```
project/
├── data/
│   ├── raw/          # 原始数据
│   └── processed/    # 处理后数据
├── R/                # R脚本
├── output/
│   ├── figures/      # 图表
│   └── tables/       # 表格
├── docs/             # 文档
└── README.md
```

### 版本控制
- 使用Git进行版本控制
- 编写有意义的提交信息
- 使用`.gitignore`排除不必要的文件
- 定期备份重要数据

### 可重现性
- 使用`renv`管理包版本
- 设置随机种子：`set.seed(123)`
- 记录R和包的版本信息：`sessionInfo()`
- 编写清晰的注释和文档

## 性能优化

### 内存管理
- 及时删除不需要的对象：`rm(object)`
- 使用`gc()`手动垃圾回收
- 监控内存使用：`pryr::mem_used()`

### 代码优化
- 向量化操作优于循环
- 使用`data.table`处理大数据
- 并行计算：`parallel`, `foreach`
- 分析代码性能：`profvis`

## 错误处理

### 调试技巧
- 使用`browser()`设置断点
- `traceback()`查看错误堆栈
- `options(error = recover)`进入调试模式

### 异常处理
```r
# 使用tryCatch处理错误
result <- tryCatch({
  # 可能出错的代码
  risky_operation()
}, error = function(e) {
  message("发生错误: ", e$message)
  return(NULL)
}, warning = function(w) {
  message("警告: ", w$message)
})
```

## 文档和报告

### 代码注释
- 解释为什么这样做，而不是做什么
- 在复杂算法前添加说明
- 使用roxygen2格式注释函数

### R Markdown
- 使用R Markdown创建可重现报告
- 合理组织章节结构
- 包含代码、结果和解释
- 输出多种格式（HTML、PDF、Word）

## 包开发

### 基本结构
- 使用`usethis`包辅助开发
- 遵循CRAN标准
- 编写完整的文档和测试
- 使用持续集成（CI）

### 测试
- 使用`testthat`编写单元测试
- 测试覆盖率应达到80%以上
- 测试边界条件和错误情况
