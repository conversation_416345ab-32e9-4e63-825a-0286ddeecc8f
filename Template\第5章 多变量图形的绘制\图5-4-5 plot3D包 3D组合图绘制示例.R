"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library(plot3D)

#选择数据范围
ii <- which(Hypsometry$x > -50 & Hypsometry$x < -20)
jj <- which(Hypsometry$y >  10 & Hypsometry$y <  40)

color <- parula(100)

####################################图5-4-5（a）3D组合图绘制示例1

png(file="\\第5章 多变量图形的绘制\\图5-4-5 plot3D包 3D组合图绘制示例_a.png",
    width = 6000, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-4-5 plot3D包 3D组合图绘制示例_a.pdf",
    width = 6, height = 5,family = "serif")	
par(family = "serif",mar = rep(2, 4))
zlim <- c(-10000, 0)
#绘制图层坐标
pmat <- perspbox(z = Hypsometry$z[ii, jj], bty = "b2",
                 xlab = "Longitude", ylab = "Latitude", zlab = "Depth", 
                 expand = 0.5, d = 2, zlim = zlim, phi = 20, theta = 30,
                 cex.axis = 1.2,cex.lab = 1.5,
                 colkey = list(side = 1))
persp3D(z = Hypsometry$z[ii,jj], add = TRUE, resfac = 2,col = color,
       contour = list(col = "gray50",side = c("zmin", "z")), 
       zlim = zlim, clab = "Depth(m)", 
       colkey = list(side = 1, length = 0.5, dist = -0.05))
dev.off()

####################################图5-4-5（b）3D组合图绘制示例2

mul_data = read_excel("\\第5章 多变量图形的绘制\\Multiple Surfaces in Same Layer.xlsx")
contour_matrix <- mul_data %>% tibble::column_to_rownames("index") %>% as.matrix()

png(file="\\第5章 多变量图形的绘制\\图5-4-5 plot3D包 3D组合图绘制示例_b.png",
    width = 6000, height = 5000,res=1000)
	
pdf(file="\\第5章 多变量图形的绘制\\图5-4-5 plot3D包 3D组合图绘制示例_b.pdf",
    width = 6, height = 5,family = "serif")	
	
par(family = "serif",mar = rep(2, 4))
zlim <- c(400, 700)
#绘制图层坐标
pmat <- perspbox(z = contour_matrix, bty = "b2",
                 xlab = "Longitude", ylab = "Latitude", zlab = "Depth", 
                 expand = 0.5, d = 2, zlim = zlim, phi = 20, theta = 30,
                 cex.axis = 1.2,cex.lab = 1.5,
                 colkey = list(side = 1))
persp3D(z = contour_matrix, add = TRUE, resfac = 2,col = color,
       contour = list(col = "black"), 
       zlim = zlim, clab = "Depth(m)", 
       colkey = list(side = 1, length = 0.5, dist = -0.05))
dev.off()


    
