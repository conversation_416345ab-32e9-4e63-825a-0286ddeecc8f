"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)


#读取数据
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\面积图构建.xlsx"
area_data = read_excel(file)
area_df <-  area_data %>% pivot_longer(cols = starts_with("Area"),names_to = "type", 
                             values_to = "value",cols_vary="slowest")
											  
												  
												  
####################################图4-2-10（a）使用ggplot2绘制的平滑面积图示例

ggplot(data = area_df,aes(x=day, y=value, fill=type)) +
  #平滑曲线样式
  stat_smooth(geom = "area",method = 'loess',span = 0.5,
              colour="black") +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(0.15,0.88),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-10 平滑/交叉面积图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-10 平滑/交叉面积图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-10（b）使用ggplot2绘制的面积图示例（position = "identity"）

file <- "\\第4章 双变量图形的绘制\\交叉面积图构建.xlsx"
area_data2 = read_excel(file)
area_df2 <-  area_data2 %>% pivot_longer(cols = starts_with("Area"),names_to = "type", 
                             values_to = "value",cols_vary="slowest")

library(ggbraid)

ggplot() +
  geom_line(data = area_df2,aes(x = day,y=value,group=type,colour=type),
           linewidth=0.5) +
  ggbraid::geom_braid(data = area_data2,aes(x=day,ymin = Area_b, 
                                            ymax = Area_a,
                                            fill=Area_b<Area_a),
              alpha=0.8) +
  scale_y_continuous(limits = c(0,40),breaks = seq(0,40,10)) +
  scale_x_continuous(limits = c(0,10),breaks = seq(0,10,2)) +
  ggprism::scale_colour_prism(palette = "waves") +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = "top",
        legend.title = element_blank(),
        legend.margin=margin(1,5,1,2),
        legend.background = element_blank(),
        legend.text = element_text(colour = "black",face='bold',size = 10),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-10 平滑/交叉面积图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-10 平滑/交叉面积图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
   