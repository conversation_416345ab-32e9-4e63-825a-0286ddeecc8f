
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(igraph)
library(ggraph)
library(igraphdata) 
data(karate) 

##################################图7-14-2 (a) igraph网络图示例1

png(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_a.png",
    width = 4000, height = 4000,res=1000)
par(mar = rep(1,4))
plot(karate)
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_a.pdf",
    width = 4, height = 4)
par(mar = rep(1,4))
plot(karate)
dev.off()

##################################图7-14-2 (b) igraph网络图示例2

png(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_b.png",
    width = 4000, height = 4000,res=1000)
par(mar = rep(1,4))
plot(karate,layout=layout.circle) 
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_b.pdf",
    width = 4, height = 4)
par(mar = rep(1,4))
plot(karate,layout=layout.circle) 
dev.off()


##################################图7-14-2 (c) igraph网络图示例3

cfg <- igraph::cluster_fast_greedy(karate) 

png(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_c.png",
    width = 4000, height = 4000,res=1000)
par(mar = rep(1,4))
cfg <- igraph::cluster_fast_greedy(karate) 
plot(cfg, karate)
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-14-2 igraph包网络图绘制示例_c.pdf",
    width = 4, height = 4)
par(mar = rep(1,4))
cfg <- igraph::cluster_fast_greedy(karate) 
plot(cfg, karate)
dev.off()