"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据集

group_bar <- read_excel("\\第4章 双变量图形的绘制\\分组误差柱形图数据.xlsx")
									

####################################图4-1-29（a）geom_jitter() 数据点误差柱形图（CI）
ggpubr::ggbarplot(data = group_bar,x="order",y="value",fill="class",
                  add=c("mean_ci"),
            position = position_dodge(0.7),palette="grey",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.2,size=0.3),
            ggtheme=theme_classic(base_family = "serif")) +
 geom_jitter(aes(order, value, fill = class), colour = "black", shape=21, 
             position = position_jitterdodge(jitter.height = 0.1, 
                                             jitter.width = 0.2)) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,40),
                    breaks = seq(0,40,10)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-29 分组误差柱形图单独数据点绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-29 分组误差柱形图单独数据点绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-29（c）ggbarplot() 数据点误差柱形图（CI）

ggpubr::ggbarplot(data = group_bar,x="order",y="value",color="class",
                  add=c("mean_ci","jitter"),
            position = position_dodge(0.7),palette="aaas",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.3,size=0.5),
            ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,40),
                    breaks = seq(0,40,10)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-29 分组误差柱形图单独数据点绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-29 分组误差柱形图单独数据点绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

   