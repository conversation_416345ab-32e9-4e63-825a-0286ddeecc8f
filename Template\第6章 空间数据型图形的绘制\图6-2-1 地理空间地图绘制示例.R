"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)

#读取数据
map_fig01 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map0.shp")



####################################图6-2-1（a）地理空间图形基本样式

ggplot() +
  geom_sf(data = map_fig01,aes(fill=type)) +
  labs(x="Longitude",y="Latitude")+
    theme(legend.position = c(0.85,0.1),
          legend.background = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 13),
        axis.text = element_text(colour = "black",face='bold',size = 11),
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-1 地理空间地图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-1 地理空间地图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-2-1（b）地理空间地图基本样式（灰色系）

ggplot() +
  geom_sf(data = map_fig01,aes(fill=type)) +
  scale_fill_grey() +
  labs(x="Longitude",y="Latitude")+
    theme(legend.position = c(0.85,0.1),
          legend.background = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 13),
        axis.text = element_text(colour = "black",face='bold',size = 11),
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-1 地理空间地图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-1 地理空间地图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)