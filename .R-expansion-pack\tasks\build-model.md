# 模型构建任务

这个任务指导你构建、训练、评估和优化统计模型或机器学习模型。

## 前提条件

- 数据已完成探索性分析和预处理
- 建模目标和评估指标已明确
- 训练集和测试集已准备就绪
- 必要的R包已安装（caret、randomForest、glmnet等）

## 步骤

### 1. 建模策略规划

收集以下信息：

- **问题类型**: 回归、分类、聚类、时间序列等
- **目标变量**: 连续型、二分类、多分类
- **特征数量**: 高维、低维
- **数据大小**: 样本量和计算资源
- **解释性要求**: 黑盒模型 vs 可解释模型
- **性能要求**: 准确性、速度、稳定性

### 2. 模型选择

根据问题类型选择合适的算法：

#### 回归问题
- **线性回归**: 简单、可解释
- **岭回归/Lasso**: 处理多重共线性
- **随机森林**: 非线性关系
- **支持向量机**: 复杂边界
- **神经网络**: 深度非线性

#### 分类问题
- **逻辑回归**: 基线模型
- **决策树**: 可解释性强
- **随机森林**: 集成方法
- **梯度提升**: 高性能
- **朴素贝叶斯**: 文本分类

#### 无监督学习
- **K-means**: 球形聚类
- **层次聚类**: 树状结构
- **DBSCAN**: 密度聚类
- **PCA**: 降维

### 3. 基线模型构建

建立简单的基线模型：

```r
# 线性回归基线
baseline_lm <- lm(target ~ ., data = train_data)

# 逻辑回归基线
baseline_glm <- glm(target ~ ., data = train_data, family = binomial)

# 简单决策树
baseline_tree <- rpart(target ~ ., data = train_data)
```

### 4. 特征工程

优化特征集：

- **特征选择**: 相关性分析、递归特征消除
- **特征变换**: 多项式特征、交互项
- **特征缩放**: 标准化、归一化
- **编码处理**: 独热编码、标签编码
- **降维**: PCA、t-SNE

### 5. 模型训练

使用交叉验证训练模型：

```r
# 设置交叉验证
ctrl <- trainControl(
  method = "cv",
  number = 10,
  savePredictions = TRUE,
  classProbs = TRUE
)

# 训练模型
model <- train(
  target ~ .,
  data = train_data,
  method = "rf",  # 或其他算法
  trControl = ctrl,
  tuneLength = 10
)
```

### 6. 超参数优化

优化模型性能：

- **网格搜索**: 系统性参数搜索
- **随机搜索**: 随机参数组合
- **贝叶斯优化**: 智能参数搜索
- **遗传算法**: 进化式优化

### 7. 模型评估

#### 回归模型评估
- **RMSE**: 均方根误差
- **MAE**: 平均绝对误差
- **R²**: 决定系数
- **残差分析**: 模型假设检验

#### 分类模型评估
- **准确率**: 整体正确率
- **精确率/召回率**: 类别平衡
- **F1分数**: 综合指标
- **AUC-ROC**: 分类能力
- **混淆矩阵**: 详细分析

### 8. 模型诊断

检查模型质量：

- **过拟合检测**: 训练集vs验证集性能
- **欠拟合检测**: 模型复杂度分析
- **特征重要性**: 变量贡献度
- **预测稳定性**: 扰动测试
- **模型假设**: 残差分析

### 9. 模型集成（可选）

提升模型性能：

- **Bagging**: 自助聚合
- **Boosting**: 梯度提升
- **Stacking**: 模型堆叠
- **Voting**: 投票集成

### 10. 模型解释

提供模型解释：

- **全局解释**: 整体特征重要性
- **局部解释**: 单个预测解释
- **SHAP值**: 特征贡献分解
- **LIME**: 局部线性解释

### 11. 结果验证

使用检查清单验证模型质量：

```bash
*execute-checklist analysis-checklist
```

### 12. 文档生成

创建模型总结报告：

```bash
*create-doc model-summary-tmpl
```

## 输出文件

模型构建完成后应生成：

- **训练脚本** (.R 或 .Rmd)
- **训练好的模型** (.rds)
- **模型评估报告** (.html 或 .pdf)
- **特征重要性图表** (.png)
- **预测结果** (.csv)

## 质量标准

- 模型性能达到预期目标
- 交叉验证结果稳定
- 特征工程有理论依据
- 超参数优化充分
- 模型解释清晰合理

## 常见模型类型

### 时间序列模型
- ARIMA、SARIMA
- 指数平滑
- Prophet
- LSTM神经网络

### 生存分析模型
- Cox比例风险模型
- Kaplan-Meier估计
- 参数生存模型

### 贝叶斯模型
- 贝叶斯线性回归
- 贝叶斯网络
- MCMC采样

## 工具推荐

- **建模框架**: caret, mlr3, tidymodels
- **算法包**: randomForest, glmnet, e1071
- **评估工具**: pROC, ModelMetrics
- **可视化**: ggplot2, plotly
- **模型解释**: DALEX, lime, shap

## 注意事项

- 避免数据泄露
- 确保模型可重现
- 考虑计算资源限制
- 验证模型泛化能力
- 监控模型性能退化
