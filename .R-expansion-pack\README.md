# R语言数据分析扩展包（增强版）

专门用于R语言数据分析、统计建模、可视化和报告生成的BMAD框架扩展包。采用严格的测试驱动开发和质量门控制，确保代码质量和可靠性。

## 概述

这个扩展包为R语言数据科学工作流提供了完整的AI代理支持，包括数据分析、统计建模、可视化和R包开发等各个方面。v2.0版本引入了工业级的质量保证体系，包括强制性测试要求、自动化验证流程和严格的质量门控制。

## 主要功能

### 🎯 核心代理

- **r-data-scientist** - R数据科学家（主协调者）
- **r-statistician** - R统计分析师
- **r-visualizer** - R数据可视化专家
- **r-package-developer** - R包开发专家

### 📊 核心能力

- **数据处理**: 数据清洗、转换、合并和预处理
- **统计分析**: 描述性统计、假设检验、回归分析
- **机器学习**: 监督学习、无监督学习、模型评估
- **数据可视化**: ggplot2、plotly、交互式图表
- **R包开发**: 包结构设计、文档编写、测试驱动开发
- **报告生成**: R Markdown、Shiny应用、科学报告
- **质量保证**: 自动化测试、代码质量检查、依赖管理
- **验证流程**: R CMD check、测试覆盖率、DoD检查清单

## 快速开始

### 1. 环境要求

- R (>= 4.0.0)
- RStudio (推荐)
- 基础统计学知识
- testthat包（测试框架）
- devtools包（开发工具）
- lintr包（代码质量检查）

### 2. 启动主协调者

```bash
npm run agent r-data-scientist
```

### 3. 可用命令

主协调者提供以下命令：

- `*help` - 显示所有可用命令
- `*chat-mode` - 进入对话模式进行数据科学咨询
- `*analyze-data` - 启动数据分析工作流
- `*create-model` - 创建统计或机器学习模型
- `*visualize` - 生成数据可视化
- `*develop-package` - 开发R包（测试驱动）
- `*validate-package` - 验证R包质量
- `*generate-report` - 生成分析报告

## 目录结构

```
R-expansion-pack/
├── config.yml                 # 扩展包配置
├── README.md                  # 说明文档
├── agents/                    # AI代理定义
│   ├── r-data-scientist.md    # 主协调者
│   ├── r-statistician.md      # 统计分析师
│   ├── r-visualizer.md        # 可视化专家
│   └── r-package-developer.md # 包开发专家
├── tasks/                     # 任务定义
│   ├── create-doc.md          # 文档创建
│   ├── execute-checklist.md   # 检查清单执行
│   ├── analyze-dataset.md     # 数据集分析
│   ├── build-model.md         # 模型构建
│   └── create-visualization.md # 可视化创建
├── templates/                 # 模板文件
│   ├── analysis-report-tmpl.md # 分析报告模板
│   ├── model-summary-tmpl.md   # 模型总结模板
│   └── package-structure-tmpl.md # R包结构模板
├── data/                      # 领域知识库
│   ├── r-best-practices.md    # R语言最佳实践
│   ├── statistical-methods.md # 统计方法指南
│   └── visualization-guide.md # 可视化指南
├── utils/                     # 工具函数
│   ├── template-format.md     # 模板格式规范
│   └── workflow-management.md # 工作流管理
├── checklists/               # 质量检查清单
│   ├── analysis-checklist.md # 分析质量检查
│   └── code-quality-checklist.md # 代码质量检查
├── workflows/                # 工作流定义
│   └── data-science-workflow.md # 数据科学工作流
└── agent-teams/             # 团队配置
    └── r-analytics-team.yml # R分析团队配置
```

## 使用场景

- 📈 **学术研究**: 统计分析、假设检验、科学报告
- 🏢 **商业分析**: 业务数据分析、预测建模、仪表板
- 🔬 **科学计算**: 数值计算、模拟、算法实现
- 📦 **包开发**: R包设计、开发、测试、发布

## 贡献

欢迎提交问题和改进建议！

## 许可证

MIT License
