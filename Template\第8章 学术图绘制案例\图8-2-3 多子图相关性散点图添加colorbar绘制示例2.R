"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(ggpmisc)
library(tagger)


library(extrafont) 
font_import(pattern = "lmroman*")

#构建数据
multiple_data = read_excel("\\第8章 学术图绘制案例\\multiple_data2.xlsx")
#计算RMSE
data_stat <- multiple_data %>%
  group_by(type, model)  %>% 
  dplyr::summarise(count=n(),
            rmse = sqrt(mean((value - pred_value)^2)))
			
			
									  												 												  
ggplot(data = multiple_data,aes(x = value,y = pred_value)) +
  geom_bin_2d(bins = 50) + 
  geom_smooth(method = "lm", colour="red",se=FALSE, formula = y ~ x,linewidth=.8) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(intercept=0, slope=1),alpha=1, linewidth=.8) + 
  ggpubr::stat_regline_equation(label.x = -5,label.y = 80,size=3.5,
                                family="LM Roman 10",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(after_stat(rr.label), after_stat(p.label), sep = "~`,`~")),
                   label.x = -5, label.y = 68,size=3.5,
                   r.accuracy = 0.001,p.accuracy = 0.001,
                   family='LM Roman 10',fontface='bold') +
  geom_text(data = data_stat, aes(x = -5, y = 58, label = paste("RMSE =",round(rmse,2))), 
            family="LM Roman 10",fontface="italic",size=3.5,hjust = 0, vjust = 1, 
            show.legend = FALSE) +
  #修改坐标轴刻度
  scale_x_continuous(limits = c(-10,90),breaks = seq(-10,100,20),expand = c(0,0)) +
  scale_fill_gradientn(name="Counts",colours = parula(100),
                      guide = guide_colorbar(barwidth = 1, barheight = 10))+
  theme_bw() +
  #分面操作
  facet_grid(rows = vars(type),cols = vars(model)) +
  tagger::tag_facets(position = "br") +
  theme(text = element_text(family = "LM Roman 10",face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 11),
        strip.text = element_text(family = "LM Roman 10",face='bold',size = 15),
        strip.background = element_rect(fill = NA,colour = NA),
        #panel.grid = element_blank(), #去除网格
        tagger.panel.tag.text = element_text(color = "red", family = "LM Roman 10",size = 14))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-2-3 多子图相关性散点图添加colorbar绘制示例2.png",
       width =9.5, height = 8, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-2-3 多子图相关性散点图添加colorbar绘制示例2.pdf",
       width =9.5, height = 8,device = cairo_pdf)