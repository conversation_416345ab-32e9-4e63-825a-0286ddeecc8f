# ==========================================
# 文件名：plotBoxPlot_improved_example.R
# 主要功能：展示改进后的箱线图绘制功能
# 依赖文件：plotBoxPlot.R
# 创建日期：2025-01-01
# ==========================================

# 加载改进后的箱线图函数
source("plotBoxPlot.R")

# 示例数据
group1_data <- c(20.3319, 25.7963, 21.8242, 19.5, 23.1, 22.8)
group2_data <- c(15.2, 18.7, 16.9, 17.3, 19.1, 14.8)

# ==========================================
# 示例1：基础箱线图（单组数据）
# ==========================================
p1 <- plotBoxPlot(
  group1_data = group1_data,
  group1_label = "2733+Silbione",
  show_points = TRUE,
  show_mean = TRUE,
  color_palette = 1
)

print("示例1：基础单组箱线图")
print(p1)

# ==========================================
# 示例2：双组对比箱线图（默认配色）
# ==========================================
p2 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = TRUE,
  show_mean = TRUE,
  color_palette = 1
)

print("示例2：双组对比箱线图（默认配色）")
print(p2)

# ==========================================
# 示例3：科研期刊风格配色
# ==========================================
p3 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  color_palette = 2
)

print("示例3：科研期刊风格配色")
print(p3)

# ==========================================
# 示例4：缺口箱线图
# ==========================================
p4 <- plotBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = FALSE,
  show_mean = TRUE,
  show_notch = TRUE,
  color_palette = 2
)

print("示例4：缺口箱线图")
print(p4)

# ==========================================
# 示例5：使用专用函数
# ==========================================
p5 <- plotPeelingEnergyBoxPlot(
  group1_data = group1_data,
  group2_data = group2_data,
  show_points = FALSE,
  show_mean = TRUE,
  show_notch = FALSE,
  color_palette = 2
)

print("示例5：专用粘附力测试箱线图")
print(p5)

# ==========================================
# 保存图片示例
# ==========================================
# 取消注释以下代码来保存图片
# ggsave("boxplot_example1.png", p1, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_example2.png", p2, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_example3.png", p3, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_example4.png", p4, width = 6, height = 4, dpi = 300, bg = "white")
# ggsave("boxplot_example5.png", p5, width = 6, height = 4, dpi = 300, bg = "white")

print("所有示例已完成！")
print("改进功能包括：")
print("1. 添加了误差线显示")
print("2. 提供了多种颜色方案选择")
print("3. 支持缺口箱线图")
print("4. 优化了字体和主题样式")
print("5. 保持了原有的所有功能")
