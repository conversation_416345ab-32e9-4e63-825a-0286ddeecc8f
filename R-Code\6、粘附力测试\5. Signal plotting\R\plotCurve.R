# ==========================================
# 文件名：plotCurve.R
# 主要功能：绘制通用多组曲线图（可移植到其他项目）
# 依赖文件：无
# 主要函数：
#   - plotMultiCurve(): 绘制多组X-Y曲线图
# 输入参数：多组X和Y数据向量，图表标题
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================

# 加载必要的包
library(ggplot2)
# Windows系统字体设置
if (.Platform$OS.type == "windows") {
  windowsFonts(times = windowsFont("Times New Roman"))
}
#' 绘制通用多组曲线图
#' 可移植到其他项目使用的通用曲线绘制函数
#'
#' @param x1 第一组X轴数据
#' @param y1 第一组Y轴数据
#' @param x2 第二组X轴数据
#' @param y2 第二组Y轴数据
#' @param x3 第三组X轴数据
#' @param y3 第三组Y轴数据
#' @param title_text 图表标题
#' @return ggplot对象
plotMultiCurve <- function(x1, y1,
                          x2, y2,
                          x3, y3,
                          title_text = "Multi-Curve Plot") {

  # 创建数据框
  data_combined <- data.frame(
    x = c(x1, x2, x3),
    y = c(y1, y2, y3),
    group = factor(c(rep("Group 1", length(x1)),
                    rep("Group 2", length(x2)),
                    rep("Group 3", length(x3))))
  )

  # 计算Y轴最大值
  max_y <- max(c(y1, y2, y3), na.rm = TRUE)

  # 创建ggplot图形
  p <- ggplot(data_combined, aes(x = x, y = y, color = group)) +
    geom_line(linewidth = 1.2) +
    geom_point(aes(shape = group), size = 2) +

    # 坐标轴设置将在后面的专用函数中统一处理

    # 设置颜色和形状
    scale_color_manual(values = c("Group 1" = "#1f77b4",
                                 "Group 2" = "#ff7f0e",
                                 "Group 3" = "#2ca02c")) +
    scale_shape_manual(values = c("Group 1" = 16,  # 圆形
                                 "Group 2" = 15,   # 方形
                                 "Group 3" = 17)) + # 三角形

    # 设置标签
    labs(x = "X Value",
         y = "Y Value") +

    # 设置主题样式，按照规则要求
    theme_bw() +
    theme(
      # 背景设置 - 统一白色背景
      plot.background = element_rect(fill = "white", color = NA),
      panel.background = element_rect(fill = "white", color = NA),

      # 全局字体设置 - 统一Times New Roman
      text = element_text(family = "times", color = "black"),

      # 坐标轴文字设置 - 固定字号和颜色
      axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
      axis.text.y = element_text(size = 10, color = "black"),

      # 坐标轴标题设置 - 粗体，固定字号
      axis.title = element_text(size = 12, face = "bold", color = "black"),

      # 网格线和边框设置 - 完整边框，无网格线
      panel.grid = element_blank(),  # 必须移除所有网格线
      axis.line = element_blank(),   # 移除轴线，避免与边框重复
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),

      # 刻度线设置 - 朝内，统一粗细
      axis.ticks = element_line(color = "black", linewidth = 0.6),
      axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内

      # 边距设置 - 标准边距
      plot.margin = margin(10, 10, 0, 10),  # 上右下左边距

      # 图例设置
      legend.position = "none"
    )

  return(p)
}

# 为了向后兼容性，保留原函数名作为别名
# 在粘附力测试项目中使用时，可以调用此函数
plotDisplacementForce <- function(displacement1, force1,
                                 displacement2, force2,
                                 displacement3, force3,
                                 title_text = "2733+Silbione") {
  # 调用通用的曲线绘制函数，但使用特定的标签和设置
  p <- plotMultiCurve(displacement1, force1,
                     displacement2, force2,
                     displacement3, force3,
                     title_text)

  # 为粘附力测试添加特定的轴标签和范围设置
  p <- p +
    labs(x = "Distance (mm)",
         y = "Peeling Force (N)") +
    # 设置特定的坐标轴范围和刻度
    scale_x_continuous(limits = c(0, 70), expand = c(0, 0), breaks = seq(0, 70, 10)) +
    scale_y_continuous(limits = c(0, 4), expand = c(0, 0), breaks = c(1, 2, 3, 4))

  return(p)
}