"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggsignif)

#构建数据集

group_bar <- read_excel("\\第4章 双变量图形的绘制\\分组误差柱形图数据.xlsx")
data_p_stats <- iris %>%  rstatix::t_test(Sepal.Length~Species)

comparisons <- list(c("setosa", "versicolor"))
									

####################################图4-1-28（a）ggpubr单组P值添加
ggpubr::ggbarplot(data = iris,x="Species",y="Petal.Length",fill="Species",
                  add="mean_sd",,palette="aaas",
                  ylab="Mean value",,legend = "top",
                  add.params= list(width = 0.2,size=0.3),
                  ggtheme=theme_classic(base_family = "serif")) +
 ggpubr::stat_compare_means(label.y = 7.5,family="serif") + # Add global Anova p-value
 ggpubr::stat_compare_means(comparisons = comparisons,
                            label.y = c(6),tip.length=c(0.5,0.1),
                            family="serif",label = "p.signif",size=5) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
#另外一种绘制方法	   
ggpubr::ggbarplot(data = iris,x="Species",y="Petal.Length",fill="Species",
                  add="mean_sd",,palette="aaas",
                  ylab="Mean value",,legend = "top",
                  add.params= list(width = 0.2,size=0.3),
                  ggtheme=theme_classic(base_family = "serif")) +
 #添加显著性P值
 ggsignif::geom_signif(comparisons = list(c("setosa", "versicolor")),
                       map_signif_level = TRUE,textsize = 6,
                       margin_top = 0,step_increase = 0.5,
                       tip_length = c(0.7,0.2)) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_a-1.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_a-1.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   
	   
	   
	   
####################################图4-1-28（b）ggpubr组内P值添加  
library(rstatix)

#进行分组计算
stat_p <- group_bar %>% 
    group_by(order) %>% 
    rstatix::t_test(value ~ class) %>% 
    rstatix::adjust_pvalue() %>%
    rstatix::add_significance()

stat_p <- stat_p %>% rstatix::add_xy_position(x = "order", dodge = 0.8) 

ggpubr::ggbarplot(data = group_bar,x="order",y="value",fill="class",add="mean_ci",
            position = position_dodge(0.7),palette="aaas",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.2,size=0.3),
            ggtheme=theme_classic(base_family = "serif")) +
  ggpubr::stat_pvalue_manual(stat_p,label = "p.adj.signif",tip.length = 0,
                             y.position = c(20,30,35),family="serif",
                             label.size=6)+ # Add significance levels
  ggpubr::stat_compare_means(label.y = 38,family="serif",size=4.2) +  # Add global the p-value 
 scale_y_continuous(expand = c(0, 0),limits = c(0,40),
                     breaks = seq(0,40,10)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-28（c）ggpubr组间P值添加	   

#进行分组计算
stat_p2 <- group_bar %>% 
    group_by(class) %>% 
    rstatix::t_test(value ~ order) %>% 
    rstatix::adjust_pvalue() %>%
    rstatix::add_significance() 
	
stat_p2 <- stat_p2 %>% rstatix::add_xy_position(x = "order", group = "class")	
	
ggpubr::ggbarplot(data = group_bar,x="order",y="value",fill="class",add="mean_ci",
            position = position_dodge(0.7),palette="aaas",
            ylab="Mean value",,legend = "top",
            add.params= list(width = 0.2,size=0.3),
            ggtheme=theme_classic(base_family = "serif")) +
  ggpubr::stat_pvalue_manual(stat_p2,label = "p.adj.signif",color="class",
                             step.group.by = "class",
                             tip.length = 0,step.increase = 0.05,
                             family="serif",
                             label.size=5)+ # Add significance levels
  ggpubr::stat_compare_means(label.y = 55,family="serif",size=4.2) +  # Add global the p-value 
 scale_y_continuous(expand = c(0, 0),limits = c(0,60),
                     breaks = seq(0,60,10)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-28 带P值误差柱形图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)





