# {{package_name}} R包开发计划

[[LLM: 这是一个R包开发模板。执行时：
1. 根据包的功能类型调整结构
2. 确保遵循CRAN标准
3. 包含完整的文档和测试
4. 完成后执行tasks#execute-checklist package-checklist]]

## 包概览

**包名**: {{package_name}}
**版本**: {{package_version}}
**标题**: {{package_title}}
**描述**: {{package_description}}
**作者**: {{author_name}} <{{author_email}}>
**许可证**: {{license_type}}

---

## 1. 包设计

### 1.1 功能概述
{{package_functionality}}

### 1.2 目标用户
{{target_users}}

### 1.3 主要功能模块
{{REPEAT_START}}
- **{{module_name}}**: {{module_description}}
{{REPEAT_END}}

### 1.4 依赖包
**导入包 (Imports)**:
{{REPEAT_START}}
- {{import_package}} (>= {{min_version}})
{{REPEAT_END}}

**建议包 (Suggests)**:
{{REPEAT_START}}
- {{suggest_package}}
{{REPEAT_END}}

---

## 2. 包结构

### 2.1 目录结构
```
{{package_name}}/
├── DESCRIPTION          # 包描述文件
├── NAMESPACE           # 命名空间文件
├── NEWS.md             # 更新日志
├── README.md           # 说明文档
├── LICENSE             # 许可证文件
├── R/                  # R源代码
│   ├── {{main_function}}.R
│   ├── {{helper_function}}.R
│   ├── data.R          # 数据文档
│   └── {{package_name}}-package.R
├── man/                # 帮助文档
├── tests/              # 测试文件
│   ├── testthat.R
│   └── testthat/
├── data/               # 数据文件
├── data-raw/           # 原始数据和处理脚本
├── inst/               # 安装时包含的文件
├── vignettes/          # 长篇文档
└── .Rbuildignore       # 构建时忽略的文件
```

### 2.2 DESCRIPTION文件
```
Package: {{package_name}}
Type: Package
Title: {{package_title}}
Version: {{package_version}}
Author: {{author_name}}
Maintainer: {{author_name}} <{{author_email}}>
Description: {{package_description_detailed}}
License: {{license_type}}
Encoding: UTF-8
LazyData: true
Depends: R (>= {{min_r_version}})
Imports: 
{{REPEAT_START}}
    {{import_package}} (>= {{min_version}}),
{{REPEAT_END}}
Suggests: 
{{REPEAT_START}}
    {{suggest_package}},
{{REPEAT_END}}
    testthat (>= 3.0.0)
Config/testthat/edition: 3
RoxygenNote: {{roxygen_version}}
VignetteBuilder: knitr
URL: {{package_url}}
BugReports: {{bug_reports_url}}
```

---

## 3. 核心函数

### 3.1 主要函数列表
{{REPEAT_START}}
#### {{function_name}}()
**功能**: {{function_purpose}}
**参数**:
- `{{param_name}}`: {{param_description}}
**返回值**: {{return_description}}
**示例**:
```r
{{function_example}}
```
{{REPEAT_END}}

### 3.2 函数实现框架

#### 3.2.1 {{main_function_name}}()
```r
#' {{function_title}}
#'
#' {{function_description}}
#'
#' @param {{param_name}} {{param_description}}
#' @return {{return_description}}
#' @export
#' @examples
#' {{function_examples}}
{{main_function_name}} <- function({{function_parameters}}) {
  # 参数验证
  {{parameter_validation}}
  
  # 主要逻辑
  {{main_logic}}
  
  # 返回结果
  return({{return_value}})
}
```

#### 3.2.2 辅助函数
```r
# 内部辅助函数（不导出）
{{helper_function_name}} <- function({{helper_parameters}}) {
  {{helper_logic}}
}
```

---

## 4. 数据集

### 4.1 包含的数据集
{{REPEAT_START}}
#### {{dataset_name}}
**描述**: {{dataset_description}}
**格式**: {{dataset_format}}
**变量**: {{dataset_variables}}
**来源**: {{dataset_source}}
{{REPEAT_END}}

### 4.2 数据文档
```r
#' {{dataset_title}}
#'
#' {{dataset_description_detailed}}
#'
#' @format A data frame with {{nrow}} rows and {{ncol}} variables:
#' \describe{
{{REPEAT_START}}
#'   \item{{{variable_name}}}{{{variable_description}}}
{{REPEAT_END}}
#' }
#' @source {{dataset_source_detailed}}
"{{dataset_name}}"
```

---

## 5. 测试计划

### 5.1 测试框架
使用`testthat`包进行单元测试

### 5.2 测试用例
{{REPEAT_START}}
#### 测试 {{function_name}}()
```r
test_that("{{test_description}}", {
  # 准备测试数据
  {{test_data_setup}}
  
  # 执行函数
  result <- {{function_name}}({{test_parameters}})
  
  # 验证结果
  expect_{{expectation_type}}(result, {{expected_value}})
})
```
{{REPEAT_END}}

### 5.3 测试覆盖率目标
- 目标覆盖率: {{coverage_target}}%
- 关键函数必须100%覆盖

---

## 6. 文档计划

### 6.1 README.md
```markdown
# {{package_name}}

{{package_description}}

## 安装

```r
# 从CRAN安装
install.packages("{{package_name}}")

# 从GitHub安装开发版本
devtools::install_github("{{github_username}}/{{package_name}}")
```

## 快速开始

```r
library({{package_name}})
{{quick_start_example}}
```

## 主要功能

{{main_features_list}}
```

### 6.2 Vignettes
{{REPEAT_START}}
#### {{vignette_title}}
**文件名**: {{vignette_filename}}.Rmd
**内容**: {{vignette_description}}
{{REPEAT_END}}

---

## 7. 开发计划

### 7.1 开发阶段
1. **阶段1**: 核心函数开发
   - {{phase1_tasks}}
   
2. **阶段2**: 测试和文档
   - {{phase2_tasks}}
   
3. **阶段3**: 优化和发布准备
   - {{phase3_tasks}}

### 7.2 里程碑
{{REPEAT_START}}
- **{{milestone_name}}** ({{milestone_date}}): {{milestone_description}}
{{REPEAT_END}}

---

## 8. 质量控制

### 8.1 代码规范
- 遵循tidyverse风格指南
- 使用roxygen2生成文档
- 所有函数必须有示例

### 8.2 检查清单
- [ ] `R CMD check`无错误无警告
- [ ] 测试覆盖率达到目标
- [ ] 所有函数有完整文档
- [ ] README和vignettes完整
- [ ] 遵循CRAN政策

### 8.3 持续集成
```yaml
# GitHub Actions配置
name: R-CMD-check
on: [push, pull_request]
jobs:
  R-CMD-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: r-lib/actions/setup-r@v1
      - name: Install dependencies
        run: |
          install.packages(c("remotes", "rcmdcheck"))
          remotes::install_deps(dependencies = TRUE)
      - name: Check
        run: rcmdcheck::rcmdcheck(args = "--no-manual", error_on = "error")
```

---

## 9. 发布计划

### 9.1 版本控制
- 使用语义化版本控制 (Semantic Versioning)
- 主版本.次版本.修订版本

### 9.2 发布流程
1. 完成开发和测试
2. 更新NEWS.md
3. 运行`R CMD check`
4. 提交到CRAN
5. 创建GitHub release

### 9.3 维护计划
- 定期更新依赖包
- 响应用户反馈
- 修复bug和安全问题

---

## 10. 使用示例

### 10.1 基本用法
```r
library({{package_name}})

# 基本示例
{{basic_example}}
```

### 10.2 高级用法
```r
# 高级示例
{{advanced_example}}
```

---

**开发者**: {{developer_name}}
**项目开始**: {{project_start_date}}
**预计完成**: {{estimated_completion}}

[[LLM: 包开发计划完成后，提醒用户：
1. 确保包名在CRAN上可用
2. 设置GitHub仓库和CI/CD
3. 准备测试数据和示例
4. 考虑包的长期维护计划
5. 遵循R包开发最佳实践]]
