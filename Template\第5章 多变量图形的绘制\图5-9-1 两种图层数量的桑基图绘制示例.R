"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(ggalluvial)


data <- read.csv("\\第5章 多变量图形的绘制\\sankey_data-修.csv")


data_long <- data %>%
  column_to_rownames("index") %>% 
  rownames_to_column() %>%
  gather(key = 'key', value = 'value', -rowname) %>%
  filter(value > 0)
colnames(data_long) <- c("source", "target", "value")
data_long$target <- paste(data_long$target, " ", sep="")

color_list = c("#EF0000","#18276F","#FEC211","#3BC371","#666699",
               "#134B24","#FF6666","#6699CC","#CC6600","#009999")

####################################图5-9-1（a）桑基图绘制示例（单个图层）

ggplot(data_long,aes(y = value, axis1 = source, axis2 = target)) +
  geom_flow(aes(fill = source),alpha=0.75,width = 0.01)+
  geom_stratum(color="white",fill="black",width = 0.02,size=0.5) +
  scale_x_discrete(expand = c(0, 0)) +
  scale_fill_manual(values = color_list) +
  theme_void() +
  theme( legend.title = element_blank(), 
        text = element_text(family = "serif",face='bold',size = 15),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-9-1 两种图层数量的桑基图绘制示例_a.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-9-1 两种图层数量的桑基图绘制示例_a.pdf",
       width =5.5, height = 4,device = cairo_pdf)
	   
####################################图5-9-1（b）桑基图绘制示例（两个图层）

data(vaccinations)
vaccinations <- transform(vaccinations,
                          response = factor(response, rev(levels(response))))
color2 <- c("#223D6C","#D20A13","#FFD121","#088247")

ggplot(vaccinations,
       aes(x = survey, stratum = response, alluvium = subject,
           y = freq,fill = response, label = response)) +
  scale_x_discrete(expand = c(0.05, 0.05)) +
  scale_fill_manual(values = color2) +
  geom_flow(alpha=0.75,width = 0.05) +
  geom_stratum(alpha = 0.75,width = 0.06) +
  geom_text(stat = "stratum", size = 3,family = "serif",show.legend = FALSE) +
  theme_void() +
  theme( legend.title = element_blank(), 
        text = element_text(family = "serif",face='bold',size = 15),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-9-1 两种图层数量的桑基图绘制示例_b.png",
       width =5.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-9-1 两种图层数量的桑基图绘制示例_b.pdf",
       width =5.5, height = 4,device = cairo_pdf)						  
						  
						  