"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

df_shapes <- data.frame(shape = 0:24)
ggplot(df_shapes, aes(0, 0, shape = shape)) +
  geom_point(aes(shape = shape), size = 6, fill = '#CD534C') +
  scale_shape_identity() +
  facet_wrap(~shape) +
  theme_void() +
  theme(plot.background = element_rect(fill="white",color="white"),
        text = element_text(family = "serif",size = 20))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-8 ggplot2中支持的点的形状.png",
       width = 6, height = 3, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-8ggplot2中支持的点的形状.pdf",
       width = 6, height = 3,device = cairo_pdf)
	   