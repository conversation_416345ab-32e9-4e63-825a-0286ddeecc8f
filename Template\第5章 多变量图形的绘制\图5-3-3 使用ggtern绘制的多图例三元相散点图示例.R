"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)
library(ggtern)


#构建数据
tern_scatter = read_excel("\\第5章 多变量图形的绘制\\ternary_scatter.xlsx")


tern_scatter <- tern_scatter %>% mutate(data_color = case_when(
                                (Size > 0 & Size<= 0.2) ~ "0.0~0.2",
                                (Size > 0.2 & <PERSON><PERSON> <= 0.4) ~ "0.2~0.4",
                                (<PERSON>ze > 0.4 & <PERSON>ze <= 0.6) ~ "0.4~0.6",
                                (Size > 0.6 & Size <= 0.8) ~ "0.6~0.8",
                                (<PERSON><PERSON> > 0.8) ~ "0.8~1.0",
                                FALSE ~ as.character(Size)))

											  												 												  
####################################图5-3-3（a）使用ggtern绘制的多图例三元相散点图示例1
ggtern(data = tern_scatter,aes(x = Variable_1,y = Variable_2,
                               z = Variable_3))+
  geom_point(aes(fill=data_color,size=Size),shape=21,stroke=.8) +
  scale_size(name = "Z Values",breaks = seq(0, 1, by = .2)) +
  scale_fill_manual(name="Value Type",
                    values = c("#2FBE8F","#459DFF",                 
                               "#FF5B9B","#FFCC37","#751DFE"),
              guide = guide_legend(override.aes = list(size = 5)))+
  labs(x="Var 1", y = "Var 2",z="Var 3") +
  theme_bw() +
  theme_showarrows() +
  theme(legend.position = c(.95,.6),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",size = 16),
        axis.title = element_text(size = 18),
        axis.ticks = element_line(size = .5),
        axis.line = element_line(size = .4),
        axis.text = element_text(colour = "black",size = 14),
        #修改刻度长度
        tern.axis.ticks.length.major=unit(3.0,'mm'),
        tern.axis.ticks.length.minor=unit(1.5,'mm'))
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-3 使用ggtern绘制的多图例三元相散点图示例_a.png",
         width = 6.2, height = 5.2,dpi=600)
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-3 使用ggtern绘制的多图例三元相散点图示例_a.pdf",
        width = 6.2, height = 5.2,device = cairo_pdf) 
	   
####################################图5-3-3（b））使用ggtern绘制的多图例三元相散点图示例2

ggtern(data = tern_scatter,aes(x = Variable_1,y = Variable_2,
                               z = Variable_3))+
  geom_point(aes(fill=Size,size=Size),shape=21,stroke=.8) +
  scale_size(name = "Z Values",breaks = seq(0, 1, by = .2)) +
  scale_fill_gradientn(name="Z-Values",colours = parula(100),
                       )+
  labs(x="Var 1", y = "Var 2",z="Var 3") +
  theme_bw() +
  theme_showarrows() +
  theme(
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",size = 16),
        axis.title = element_text(size = 18),
        axis.ticks = element_line(size = .5),
        axis.line = element_line(size = .4),
        axis.text = element_text(colour = "black",size = 14),
        #修改刻度长度
        tern.axis.ticks.length.major=unit(3.0,'mm'),
        tern.axis.ticks.length.minor=unit(1.5,'mm'))
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-3 使用ggtern绘制的多图例三元相散点图示例_b.png",
         width = 6.2, height = 5.2,dpi=600)
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-3 使用ggtern绘制的多图例三元相散点图示例_b.pdf",
        width = 6.2, height = 5.2,device = cairo_pdf) 
		
		