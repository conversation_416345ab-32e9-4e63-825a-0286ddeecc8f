"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(GGally)

data <- iris

####################################图5-5-1（a）未经过缩放处理的平行坐标图示例

ggparcoord(data,columns = 1:4, groupColumn = 5,
           scale = "globalminmax",alphaLines = 0.5) +
 ggsci::scale_color_aaas() +
 labs(x="",y="") +
 theme_minimal() +
 theme(legend.position = "top",
       legend.title = element_blank(),
       text = element_text(family = "serif",size = 14),
       axis.text = element_text(colour = "black",size = 12),
        #显示更多刻度内容
       plot.margin = margin(5, 5 ,5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-5-1 使用ggally包绘制的平行坐标图示例_a.png",
       width =6, height = 3.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-5-1 使用ggally包绘制的平行坐标图示例_a.pdf",
       width =6, height = 3.5,device = cairo_pdf)
	   
####################################图5-5-1（b）经过归一化缩放处理后的平行坐标图示例   
ggparcoord(data,columns = 1:4, groupColumn = 5,
           alphaLines = 0.5,scale = "uniminmax") +
 ggsci::scale_color_aaas() +
 labs(x="",y="") +
 theme_minimal() +
 theme(legend.position = "top",
       legend.title = element_blank(),
       text = element_text(family = "serif",size = 14),
       axis.text = element_text(colour = "black",size = 12),
        #显示更多刻度内容
       plot.margin = margin(5, 5 ,5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-5-1 使用ggally包绘制的平行坐标图示例_b.png",
       width =6, height = 3.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-5-1 使用ggally包绘制的平行坐标图示例_b.pdf",
       width =6, height = 3.5,device = cairo_pdf)

    
