
"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(gstat)
library(pals)
sf_use_s2(FALSE)
			 
		 
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")
point_data <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_huouse.csv")

#设定投影坐标
sf::st_crs(map_fig02) = 4326
#转换成sf对象
point_data_sf <- sf::st_as_sf(point_data,coords = c("long", "lat"),crs = 4326)
#生成400*400的网格
grid_df <- expand.grid(x=seq(from = st_bbox(map_fig02)[1],to = st_bbox(map_fig02)[3],length.out = 400),
                       y=seq(from = st_bbox(map_fig02)[2],to = st_bbox(map_fig02)[4],length.out = 400))
grid_sf <- sf::st_as_sf(grid_df,coords = c("x", "y"),crs = 4326)

IDW <- gstat::idw(formula = value ~ 1, locations = point_data_sf, newdata=grid_sf)
#裁剪操作
IDW_mask_result <- sf::st_intersection(IDW,map_fig02)
		  

####################################图6-7-3（a）利用gstat包进行IDW插值可视化示例

ggplot() +
  geom_sf(data = IDW_mask_result,aes(color=var1.pred),size=0.03) +
  geom_sf(data = map_fig02,fill="NA",color="black",linewidth=0.4) +
  #geom_point(data=point_data,aes(x=long,y=lat,color=value),size=0.5) +
  scale_color_gradientn(colours = parula(100),breaks=seq(500,2500,500)) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(100,140,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",color="IDW Result")+
  theme_classic() +
  theme(
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 12,hjust = 0.5),
        legend.key.height=unit(0.7, "cm"),
        legend.key.width=unit(0.5, "cm"),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 20, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-3 IDW插值可视化结果及添加指北针后的可视化示例_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-3 IDW插值可视化结果及添加指北针后的可视化示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)

####################################图6-7-3（b）利用gstat包进行IDW插值可视化样式（指北针）

library(ggspatial)

ggplot() +
  geom_sf(data = IDW_mask_result,aes(color=var1.pred),size=0.03) +
  geom_sf(data = map_fig02,fill="NA",color="black",linewidth=0.4) +
  annotation_scale(location = "br",text_family = "serif") +
  annotation_north_arrow(location = "tr", which_north = "true",
                         style = north_arrow_fancy_orienteering) +
  scale_color_gradientn(colours = parula(100),breaks=seq(500,2500,500)) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(100,140,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",color="IDW Result")+
  theme_classic() +
  theme(
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 12,hjust = 0.5),
        legend.key.height=unit(0.7, "cm"),
        legend.key.width=unit(0.5, "cm"),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 20, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-3 IDW插值可视化结果及添加指北针后的可视化示例_b.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-3 IDW插值可视化结果及添加指北针后的可视化示例_b.pdf",
       width =5, height = 4,device = cairo_pdf)

