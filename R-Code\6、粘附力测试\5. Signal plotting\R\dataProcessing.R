# =============================================================================
# 数据处理模块
#
# 功能：读取和预处理粘附力测试的CSV数据
# 对应MATLAB中adhesion.m的数据预处理部分
#
# 依赖：无外部依赖，使用R基础函数
#
# 主要函数：
# - readAndProcessData(file_path): 读取CSV文件并预处理
#   输入：CSV文件路径
#   输出：包含三组力和位移数据的列表
# - checkDataQuality(processed_data): 数据质量检查
#   输入：处理后的数据列表
#   输出：打印质量报告
# - saveProcessedData(): 保存处理后的数据（已注释，不自动保存）
#
# 数据格式：
# - CSV文件包含6列：force1, displacement1, force2, displacement2, force3, displacement3
# - 每行代表一个时间点的测量值
# - 自动处理缺失值（NA）和NaN
# =============================================================================

#' 读取和预处理CSV数据
#' 对应MATLAB的readmatrix和数据分组处理
#' 
#' @param file_path CSV文件路径
#' @return 包含三组力和位移数据的列表
readAndProcessData <- function(file_path) {

  # 检查文件是否存在
  if (!file.exists(file_path)) {
    stop(paste("File not found:", file_path))
  }

  # 读取CSV数据，对应MATLAB的readmatrix
  data <- read.csv(file_path, header = FALSE)

  # 检查数据维度
  if (ncol(data) < 6) {
    stop("Data must have at least 6 columns (3 groups × 2 variables each)")
  }
  
  # 将数据分成三组，并处理各组数据的有效长度
  # 对应MATLAB的数据分组和NaN处理
  
  # 第一组：列1和列2
  force1_raw <- data[, 1]
  displacement1_raw <- data[, 2]
  
  # 第二组：列3和列4
  force2_raw <- data[, 3]
  displacement2_raw <- data[, 4]
  
  # 第三组：列5和列6
  force3_raw <- data[, 5]
  displacement3_raw <- data[, 6]
  
  # 处理缺失值，对应MATLAB的~isnan处理
  # 找到每组数据中的有效数据点
  valid_idx1_force <- !is.na(force1_raw)
  valid_idx1_disp <- !is.na(displacement1_raw)
  valid_idx1 <- valid_idx1_force & valid_idx1_disp
  
  valid_idx2_force <- !is.na(force2_raw)
  valid_idx2_disp <- !is.na(displacement2_raw)
  valid_idx2 <- valid_idx2_force & valid_idx2_disp
  
  valid_idx3_force <- !is.na(force3_raw)
  valid_idx3_disp <- !is.na(displacement3_raw)
  valid_idx3 <- valid_idx3_force & valid_idx3_disp
  
  # 提取有效数据
  force1 <- force1_raw[valid_idx1]
  displacement1 <- displacement1_raw[valid_idx1]
  
  force2 <- force2_raw[valid_idx2]
  displacement2 <- displacement2_raw[valid_idx2]
  
  force3 <- force3_raw[valid_idx3]
  displacement3 <- displacement3_raw[valid_idx3]
  
  # 检查数据有效性
  if (length(force1) == 0 || length(force2) == 0 || length(force3) == 0) {
    warning("One or more groups have no valid data points")
  }
  
  # 返回处理后的数据
  result <- list(
    force1 = force1,
    displacement1 = displacement1,
    force2 = force2,
    displacement2 = displacement2,
    force3 = force3,
    displacement3 = displacement3,
    raw_data = data,
    valid_counts = c(length(force1), length(force2), length(force3))
  )
  
  return(result)
}

# 数据质量检查函数（已简化，减少输出）
checkDataQuality <- function(processed_data) {

  groups <- c("Group 1", "Group 2", "Group 3")
  force_vars <- c("force1", "force2", "force3")
  disp_vars <- c("displacement1", "displacement2", "displacement3")

  for (i in 1:3) {
    force_data <- processed_data[[force_vars[i]]]
    disp_data <- processed_data[[disp_vars[i]]]

    # 检查异常值
    force_outliers <- sum(abs(force_data) > 10, na.rm = TRUE)
    disp_outliers <- sum(abs(disp_data) > 100, na.rm = TRUE)

    if (force_outliers > 0) {
      warning(paste(groups[i], "发现", force_outliers, "个力值异常点"))
    }
    if (disp_outliers > 0) {
      warning(paste(groups[i], "发现", disp_outliers, "个位移异常点"))
    }
  }
}

# 保存处理后的数据函数（已注释，不自动保存）
# saveProcessedData <- function(processed_data, output_path = "processed_data.csv") {
#   max_length <- max(processed_data$valid_counts)
#
#   pad_vector <- function(vec, target_length) {
#     if (length(vec) < target_length) {
#       return(c(vec, rep(NA, target_length - length(vec))))
#     }
#     return(vec)
#   }
#
#   save_data <- data.frame(
#     force1 = pad_vector(processed_data$force1, max_length),
#     displacement1 = pad_vector(processed_data$displacement1, max_length),
#     force2 = pad_vector(processed_data$force2, max_length),
#     displacement2 = pad_vector(processed_data$displacement2, max_length),
#     force3 = pad_vector(processed_data$force3, max_length),
#     displacement3 = pad_vector(processed_data$displacement3, max_length)
#   )
#
#   write.csv(save_data, output_path, row.names = FALSE)
# }
