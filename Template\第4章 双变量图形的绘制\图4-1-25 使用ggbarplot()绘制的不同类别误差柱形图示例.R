"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据集
err_bar <- iris %>% select(Species, Sepal.Length) 



									

####################################图4-1-25（a）SE误差柱形图
#SE
ggpubr::ggbarplot(data = err_bar,x="Species",y = "Sepal.Length",
                  fill = "Species",width = .65,ylab="Mean value",
                  add = c("mean_se"),legend = "top",
                  palette="nejm",
                  ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-25（）SD误差柱形图	   
#SD
ggpubr::ggbarplot(data = err_bar,x="Species",y = "Sepal.Length",
                  fill = "Species",width = .65,ylab="Mean value",
                  add = c("mean_sd"),legend = "top",
                  palette="nejm",
                  ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-25（）CI误差柱形图	
#CI
ggpubr::ggbarplot(data = err_bar,x="Species",y = "Sepal.Length",
                  fill = "Species",width = .65,ylab="Mean value",
                  add = c("mean_ci"),legend = "top",
                  palette="nejm",
                  ggtheme=theme_classic(base_family = "serif")) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-25 使用ggbarplot()绘制的不同类别误差柱形图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
   