"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)
library(ggforce)
library(ggrain)


#构建数据集

#构建数据集
set.seed(42) # the magic number
iris_subset <- iris[iris$Species %in% c('versicolor', 'virginica'),]
iris.long <- cbind(rbind(iris_subset, iris_subset, iris_subset), 
                   data.frame(time = c(rep("t1", dim(iris_subset)[1]), rep("t2", dim(iris_subset)[1]), rep("t3", dim(iris_subset)[1])),
                              id = c(rep(1:dim(iris_subset)[1]), rep(1:dim(iris_subset)[1]), rep(1:dim(iris_subset)[1]))))
# adding .5 and some noise to the versicolor species in t2
iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t2"] <- iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t2"] + .5 + rnorm(length(iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t2"]), sd = .2)
# adding .8 and some noise to the versicolor species in t3
iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t3"] <- iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t3"] + .8 + rnorm(length(iris.long$Sepal.Width[iris.long$Species == 'versicolor' & iris.long$time == "t3"]), sd = .2)
# now we subtract -.2 and some noise to the virginica species
iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t2"] <- iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t2"] - .2 + rnorm(length(iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t2"]), sd = .2)
# now we subtract -.4 and some noise to the virginica species
iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t3"] <- iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t3"] - .4 + rnorm(length(iris.long$Sepal.Width[iris.long$Species == 'virginica' & iris.long$time == "t3"]), sd = .2)

iris.long$Sepal.Width <- round(iris.long$Sepal.Width, 1) # rounding Sepal.Width so t2 data is on the same resolution
iris.long$time <- factor(iris.long$time, levels = c('t1', 't2', 't3'))
								

ggplot(iris.long, aes(time, Sepal.Width, fill = Species)) +
  geom_rain(alpha = 0.5, rain.side = 'f', id.long.var = "id", 
            cov = "Sepal.Length",
            boxplot.args = list(outlier.shape = NA, alpha = .8),
            violin.args = list(alpha = .8, color = NA),
            boxplot.args.pos = list(width = .1,
            position = ggpp::position_dodgenudge(x = c(-.13, -.13, # t1 old, t1 young
                                                        -.13, .13, 
                                                         .13, .13))),
            violin.args.pos = list(width = .7,
            position = position_nudge(x = c(rep(-.2, 256*2), rep(-.2, 256*2),# t1
                                             rep(-.2, 256*2), rep(.2, 256*2), # t2
                                             rep(.2, 256*2), rep(.2, 256*2))))) +
  scale_fill_manual(values=c("dodgerblue", "darkorange")) +
  scale_color_viridis_c(option =  "A", direction = -1) +
  theme_classic() +
  theme(legend.title = element_text(family = "serif",face='bold',size = 14),
        legend.text = element_text(family = "serif",face='bold',size = 12),
        text = element_text(family = "serif",face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-47 分类映射和数值映射云雨图绘制示例.png",
       width =7.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-47 分类映射和数值映射云雨图绘制示例.pdf",
       width =7.5, height = 4,device = cairo_pdf)