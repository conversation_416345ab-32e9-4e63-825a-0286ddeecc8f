
"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)
library(ggmapinset)

sf_use_s2(FALSE)
			 
		 
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")
	  

####################################图6-8-1（a）子地图绘制示例1

inset_map <- map_fig02 %>% filter(country=="JAY")
#获取边框
inset_map_bb <- st_as_sfc(st_bbox(inset_map))

map_main <- ggplot() +
  geom_sf(data =map_fig02,fill="#9CCA9C",colour="black",alpha=0.8,linewidth=0.15) +
  geom_sf(data = inset_map,fill="red")+
  geom_sf(data = inset_map_bb, fill = NA,color = "black", linewidth = 0.3) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(100,140,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.97,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_blank(),
        legend.key.size = unit(0.3, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))

inset_plot  <- ggplot() +
  geom_sf(data = inset_map,fill="red",colour="black",linewidth=.5) +
  geom_sf(data = inset_map_bb, fill = NA, 
          color = "black", linewidth = .5) +
  theme_void() 

cowplot::ggdraw() +
  coord_equal(xlim = c(0, 20), ylim = c(0, 20), expand = FALSE) +
  annotation_custom(ggplotGrob(map_main), xmin = 0, xmax = 20, 
                    ymin = 0, ymax = 20) +
  annotation_custom(ggplotGrob(inset_plot), xmin = 16.5, xmax = 20, 
                     ymin = 12, ymax = 20) +
  #添加连接线
  geom_segment(aes(x = 11.5, xend = 19.8, y = 12.2, yend = 14.2), 
               color = "black", linewidth = .3) +
  geom_segment(aes(x = 9.3, xend = 16.7, y = 14.7, yend = 17.8), 
               color = "black", linewidth = .3)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-8-1 两种样式的子地图绘制示例_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-8-1 两种样式的子地图绘制示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)


####################################图6-8-1（b）子地图绘制示例2

library(ggmapinset)
#设定投影坐标
sf::st_crs(map_fig02) = 4326
st_geometry(map_fig02)

ggplot(data = map_fig02) +
  geom_sf(fill="#9CCA9C",colour="black",alpha=0.8) +
  geom_sf_inset(fill="#9CCA9C",colour="black",alpha=0.8,map_base = "none") +
  geom_inset_frame(colour = "red",linewidth=0.3) +
  coord_sf_inset(inset = configure_inset(
      centre = sf::st_sfc(sf::st_point(c(117.5, 53)), crs = 4326),
      scale = 4,translation = c(750, 200), radius = 60,units = "mi")) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,150),breaks = seq(100,150,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.97,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_blank(),
        legend.key.size = unit(0.3, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-8-1 两种样式的子地图绘制示例_b.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-8-1 两种样式的子地图绘制示例_b.pdf",
       width =5, height = 4,device = cairo_pdf)



