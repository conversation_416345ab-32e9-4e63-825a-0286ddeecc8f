# R包验证任务

这个任务提供R包开发的自动化验证流程，确保包符合CRAN标准和质量要求。代理必须按照这些指令进行系统性验证。

## 验证概述

此任务实现类似dev.md代理的严格质量门控制，确保R包在标记完成前通过所有必要的验证步骤。

## 验证流程

### 1. **环境准备**

首先确认验证环境：

```r
# 检查R版本和关键包
R.version.string
packageVersion("devtools")
packageVersion("testthat")
packageVersion("roxygen2")
```

验证要求：
- R版本 >= 4.0.0
- devtools, testthat, roxygen2已安装
- 包目录结构完整

### 2. **代码质量检查**

#### 2.1 代码风格验证
```r
# 使用lintr检查代码风格
library(lintr)
lint_package()
```

**通过标准**：
- 无ERROR级别问题
- WARNING数量 <= 5个
- 所有问题都有合理解释

#### 2.2 代码复杂度检查
```r
# 检查函数复杂度
library(cyclocomp)
cyclocomp_package()
```

**通过标准**：
- 函数圈复杂度 <= 15
- 包整体复杂度合理

### 3. **文档完整性验证**

#### 3.1 roxygen2文档检查
```r
# 更新和检查文档
devtools::document()
devtools::check_man()
```

**通过标准**：
- 所有导出函数有完整文档
- @param, @return, @examples完整
- 文档可以正确生成

#### 3.2 README和vignettes检查
```r
# 检查README
file.exists("README.md")

# 检查vignettes
devtools::build_vignettes()
```

**通过标准**：
- README.md存在且包含基本使用示例
- 至少一个vignette且可以构建

### 4. **测试验证**

#### 4.1 单元测试执行
```r
# 运行所有测试
devtools::test()
```

**通过标准**：
- 所有测试通过（0 failures）
- 测试覆盖率 >= 80%
- 测试运行时间合理

#### 4.2 测试覆盖率检查
```r
# 检查测试覆盖率
library(covr)
coverage <- package_coverage()
print(coverage)
```

**通过标准**：
- 整体覆盖率 >= 80%
- 关键函数覆盖率 >= 90%
- 覆盖率报告清晰

### 5. **R CMD check验证**

#### 5.1 标准检查
```r
# 执行R CMD check
devtools::check()
```

**通过标准**：
- 0 errors
- 0 warnings  
- 0 notes（或已解释的notes）

#### 5.2 CRAN检查
```r
# 执行CRAN级别检查
devtools::check(cran = TRUE)
```

**通过标准**：
- 通过所有CRAN检查
- 示例代码可运行
- 包大小合理

### 6. **依赖验证**

#### 6.1 依赖安全检查
```r
# 检查依赖包状态
tools::package_dependencies("your_package", recursive = TRUE)
```

**验证项目**：
- 所有依赖包可用
- 版本要求合理
- 无已知安全漏洞
- 许可证兼容

#### 6.2 最小依赖测试
```r
# 在最小环境中测试
# 仅安装必需依赖后测试包功能
```

### 7. **功能验证**

#### 7.1 安装测试
```r
# 测试包安装
devtools::install()
library(your_package)
```

#### 7.2 示例代码验证
```r
# 运行所有示例
devtools::run_examples()
```

**通过标准**：
- 包可以成功安装和加载
- 所有示例代码运行无错误
- 主要功能正常工作

### 8. **性能验证**

#### 8.1 基准测试
```r
# 关键函数性能测试
library(microbenchmark)
microbenchmark(your_key_function(test_data))
```

#### 8.2 内存使用检查
```r
# 检查内存使用
library(profvis)
profvis(your_function(large_data))
```

### 9. **验证报告生成**

#### 9.1 自动化报告
```r
# 生成验证报告
validation_report <- list(
  timestamp = Sys.time(),
  r_version = R.version.string,
  lint_results = lint_summary,
  test_results = test_summary,
  check_results = check_summary,
  coverage = coverage_summary
)

# 保存报告
saveRDS(validation_report, "validation_report.rds")
```

## 验证决策矩阵

| 验证项目 | 必须通过 | 可接受风险 | 阻塞发布 |
|---------|---------|-----------|---------|
| R CMD check ERROR | ✓ | ✗ | ✓ |
| R CMD check WARNING | ✓ | ✗ | ✓ |
| 测试失败 | ✓ | ✗ | ✓ |
| 覆盖率 < 80% | ✗ | ✓ | ✗ |
| 文档缺失 | ✓ | ✗ | ✓ |
| lintr ERROR | ✓ | ✗ | ✗ |
| 依赖问题 | ✓ | ✗ | ✓ |

## 验证失败处理

### 阻塞性问题
如果遇到以下问题，必须停止并修复：
- R CMD check ERROR或WARNING
- 测试失败
- 文档生成失败
- 依赖冲突

### 警告性问题
以下问题需要记录但可以继续：
- 测试覆盖率略低
- 轻微的lintr警告
- 性能不是最优

### 修复流程
1. 记录问题详情
2. 分析根本原因
3. 实施修复
4. 重新运行验证
5. 更新验证报告

## 验证完成标准

包验证完成需要满足：
- [ ] 所有阻塞性检查通过
- [ ] 验证报告生成
- [ ] 问题修复记录完整
- [ ] DoD检查清单完成

只有满足所有标准，包开发任务才能标记为完成。
