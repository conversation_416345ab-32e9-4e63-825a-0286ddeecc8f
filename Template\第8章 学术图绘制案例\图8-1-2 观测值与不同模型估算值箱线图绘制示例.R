"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(extrafont) 
font_import(pattern = "lmroman*")


#构建数据
scatter_data <- read_excel("\\第8章 学术图绘制案例\\scatter_data.xlsx")
scatter_data_long <- scatter_data %>% 
       tidyr::pivot_longer(cols = everything(),cols_vary = "slowest")
#改变绘图属性顺序
scatter_data_long$name <- factor(scatter_data_long$name, 
                        levels=c("obser","DNN","GBRT","LR","SVR"))

									  												 												  
####################################图8-1-2（a）不同模型估算值箱线图绘制示例（gray色系）

ggplot(data = scatter_data_long,aes(x=name,y=value)) +
  stat_boxplot(geom = "errorbar",width = 0.4,linewidth=0.5) + 
  geom_boxplot(aes(fill=name)) +
  scale_fill_grey() +
  labs(x="Model",y="ET Values") +
  theme_classic() +
  theme(legend.position = "none",
        text = element_text(family = "LM Roman 10",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-2 观测值与不同模型估算值箱线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-2 观测值与不同模型估算值箱线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图8-1-2（b）不同模型估算值箱线图绘制示例（waves色系）

ggplot(data = scatter_data_long,aes(x=name,y=value)) +
  stat_boxplot(geom = "errorbar",width = 0.4,linewidth=0.5) + 
  geom_boxplot(aes(fill=name)) +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Model",y="ET Values") +
  theme_classic() +
  theme(legend.position = "none",
        text = element_text(family = "LM Roman 10",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-2 观测值与不同模型估算值箱线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第8章 学术图绘制案例\\图8-1-2 观测值与不同模型估算值箱线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

