"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)
library(tricolore)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

values01 <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_City.csv")

merger01 <- sp::merge(x = map_fig02,y=values01,by =c("country","SP_ID"),
                      duplicateGeoms=FALSE)

tric <- tricolore::Tricolore(df = merger01,p1 = "orange",p2 = "apple",p3 = "banana")
merger01$educ_rgb <- tric$rgb

####################################图6-3-5（a）三变量分级统计地图示例1

tri_map <- ggplot(merger01) +
  geom_sf(aes(fill = educ_rgb, geometry = geometry), 
              color = "black", linewidth = 0.3) +
  scale_fill_identity() +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))

tri_legend <- tric$key +
  theme(plot.background = element_blank(),
        panel.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 8),
        axis.text = element_text(colour = "black",face='bold',size = 6),
        plot.margin = margin(0, 10, 0, 10))

# combine map with legend
library(cowplot)
finalPlot <- ggdraw() +
  draw_plot(tri_map, 0, 0, 1, 1) +
  draw_plot(ggplotGrob(tri_legend), 0.62, 0.15, 0.4, 0.4)
finalPlot

ggtern::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-3-5 使用tricolore包绘制的三变量分级统计地图示例_a.png",
       width =4.5, height = 4,dpi = 900)
ggtern::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-3-5 使用tricolore包绘制的三变量分级统计地图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)		
		
	   
####################################图6-3-5（b）三变量分级统计地图示例2

tric_inf <- tricolore::Tricolore(df = merger01,p1 = "orange",p2 = "apple",p3 = "banana",breaks = Inf,
                                 center = NA)
merger01$educ_rgb_inf <- tric_inf$rgb

tri_map_inf <- ggplot(merger01) +
  geom_sf(aes(fill = educ_rgb_inf, geometry = geometry), 
              color = "black", linewidth = 0.3) +
  scale_fill_identity() +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))

tri_legend2 <- tric_inf$key +
  theme(plot.background = element_blank(),
        panel.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 8),
        axis.text = element_text(colour = "black",face='bold',size = 6),
        plot.margin = margin(0, 10, 0, 10))

# combine map with legend
library(cowplot)
finalPlot <- ggdraw() +
  draw_plot(tri_map_inf, 0, 0, 1, 1) +
  draw_plot(ggplotGrob(tri_legend2), 0.62, 0.15, 0.4, 0.4)


ggtern::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-3-5 使用tricolore包绘制的三变量分级统计地图示例_b.png",
       width =4.5, height = 4,dpi = 900)
ggtern::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-3-5 使用tricolore包绘制的三变量分级统计地图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)		
		