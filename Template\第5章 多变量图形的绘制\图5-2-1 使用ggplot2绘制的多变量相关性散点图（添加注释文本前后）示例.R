"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


#构建数据
cor_line <- readxl::read_excel("\\第5章 多变量图形的绘制\\散点图样例数据2.xlsx",
                               sheet = "data02")

											  												 												  
####################################图5-2-1（a）未添加注释文本的多变量相关性散点图示例 

ggplot(data = cor_line,aes(x=values,y=pred_values)) +
  geom_errorbar(aes(ymin = pred_values-y_error, ymax = pred_values+y_error),
               colour="gray40",linewidth=0.2) +
  geom_errorbarh(aes(xmin = values-x_error, xmax = values+x_error),
               colour="gray40",linewidth=0.2) +
  geom_point(shape=21,size=4.5,aes(fill=value_3)) +
  geom_smooth(method = "lm", se=FALSE, colour="red",formula = y ~ x) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(intercept=0, slope=1),alpha=1, size=.5) + 
  scale_fill_gradientn(colours = parula(100),breaks=seq(20,180,40),
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour = "black",
                           title = "Colorbar Label",
                           title.hjust = 0.5,
                           title.position = "right",
                           barwidth = 1, barheight = 14,
                       )) +
  scale_y_continuous(expand = c(0, 0),limits = c(-0.1, 1.8),
                     breaks = seq(0,2,0.2)) +
  scale_x_continuous(limits = c(-0.1,1.8),breaks = seq(0,1.8,0.2),
                     expand = c(0,0)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(legend.title = element_text(size = 14, angle = 90),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-1 使用ggplot2绘制的多变量相关性散点图（添加注释文本前后）示例_a.png",
       width =5.8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-1 使用ggplot2绘制的多变量相关性散点图（添加注释文本前后）示例_a.pdf",
       width =5.8, height = 4.5,device = cairo_pdf)
	   
####################################图5-2-1（b）添加注释文本的多变量相关性散点图示例 

num <- nrow(cor_line)

ggplot(data = cor_line,aes(x=values,y=pred_values)) +
  geom_errorbar(aes(ymin = pred_values-y_error, ymax = pred_values+y_error),
               colour="gray40",linewidth=0.2) +
  geom_errorbarh(aes(xmin = values-x_error, xmax = values+x_error),
               colour="gray40",linewidth=0.2) +
  geom_point(shape=21,size=4.5,aes(fill=value_3)) +
  geom_smooth(method = "lm", se=FALSE, colour="red",formula = y ~ x) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(intercept=0, slope=1),alpha=1, size=.5) + 
  ggpubr::stat_regline_equation(label.x = .02,label.y = 1.65,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(after_stat(rr.label), after_stat(p.label), sep = "~`,`~")),
                   label.x = .02, label.y = 1.45,size=6,
                   r.accuracy = 0.01,p.accuracy = 0.001,
                   family='serif',fontface='bold') +
  annotate("text",x=0.02,y=1.25,label=paste("N = ",num),size=6,
            family='serif',hjust = 0,fontface="italic") +
  scale_fill_gradientn(colours = parula(100),breaks=seq(20,180,40),
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour = "black",
                           title = "Colorbar Label",
                           title.hjust = 0.5,
                           title.position = "right",
                           barwidth = 1, barheight = 14,
                       )) +
  scale_y_continuous(expand = c(0, 0),limits = c(-0.1, 1.8),
                     breaks = seq(0,2,0.2)) +
  scale_x_continuous(limits = c(-0.1,1.8),breaks = seq(0,1.8,0.2),
                     expand = c(0,0)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(legend.title = element_text(size = 14, angle = 90),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-1 使用ggplot2绘制的多变量相关性散点图（添加注释文本前后）示例_b.png",
       width =5.8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-1 使用ggplot2绘制的多变量相关性散点图（添加注释文本前后）示例_b.pdf",
       width =5.8, height = 4.5,device = cairo_pdf)
   