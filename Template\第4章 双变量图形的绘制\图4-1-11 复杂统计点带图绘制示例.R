"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
strip_data_02 <- read_excel("\\第4章 双变量图形的绘制\\strip charts_pro.xlsx")

#修改绘图变量顺序
#改变绘图属性顺序
strip_data_02$variable <- factor(strip_data_02$variable, 
                        levels=c("NS_Smk","SMK_Smk","NS+abx_Smk","SMK+abx_Smk",
                                 "NS_Cess","SMK_Cess","NS+abx_Cess","SMK+abx_Cess"))
										

####################################图4-1-11（a）复杂点带图（带均值横线）样式
ggplot(data = strip_data_02,aes(x = variable,y=value)) +
    # 添加均值横线
    stat_summary(geom = "crossbar",fun = mean,
        colour = "black",linewidth = 0.4, width = 0.8,
        show.legend = FALSE) +
    geom_jitter(aes(fill=type,group=group),shape=21,size=3,stroke = 0.5,
                width = 0.2) +
    #设置图例成2行
    guides(fill = guide_legend(nrow = 2)) +
     scale_x_discrete(labels = c("","Non Contact","","",
                                 "","Contact","","")) +
     scale_y_continuous(expand = c(0, 0),breaks = seq(-1,4,1),
                         limits = c(-1, 4)) +
    ggprism::scale_fill_prism(palette = "waves") +
    labs(x="",y="Values") +
      theme_classic()+
      theme(legend.position=c(0.5,0.96),
            legend.title = element_blank(),
            text = element_text(family = "serif",face='bold',size = 18),
            axis.text = element_text(colour = "black",face='bold',size = 15),
            axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                     hjust = 0.3),
            axis.ticks.length=unit(.2, "cm"),
            #删除x轴刻度
            axis.ticks.x = element_blank(),
            panel.grid = element_blank(), #去除副网格
            #显示更多刻度内容
            plot.margin = margin(10, 10, 10, 10),
            axis.ticks = element_line(colour = "black",linewidth = .4),
            axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-11（b）复杂点带图（带均值横线、类别阴影）样式
#添加阴影区域geom_rect()函数

ggplot(data = strip_data_02,aes(x = variable,y=value)) +
    # 添加阴影区域
    geom_rect(aes(xmin=4.5,xmax=8.5,ymin=-1,ymax=3.2),
              fill="gray90")+
    #添加垂直虚线
    geom_vline(xintercept = 4.5,linetype="longdash",linewidth=0.6) +
    # 添加均值横线
    stat_summary(geom = "crossbar",fun = mean,
        colour = "black",linewidth = 0.4, width = 0.8,
        show.legend = FALSE) +
    geom_jitter(aes(fill=type,group=group),shape=21,size=3,stroke = 0.5,
                width = 0.2) +
    #设置图例成2行
    guides(fill = guide_legend(nrow = 2)) +
     scale_x_discrete(labels = c("","Non Contact","","",
                                 "","Contact","","")) +
     scale_y_continuous(expand = c(0, 0),breaks = seq(-1,4,1),
                         limits = c(-1, 4)) +
    ggprism::scale_fill_prism(palette = "waves") +
    labs(x="",y="Values") +
      theme_classic()+
      theme(legend.position=c(0.5,0.96),
            legend.title = element_blank(),
            text = element_text(family = "serif",face='bold',size = 18),
            axis.text = element_text(colour = "black",face='bold',size = 15),
            axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                     hjust = 0.3),
            axis.ticks.length=unit(.2, "cm"),
            #删除x轴刻度
            axis.ticks.x = element_blank(),
            panel.grid = element_blank(), #去除副网格
            #显示更多刻度内容
            plot.margin = margin(10, 10, 10, 10),
            axis.ticks = element_line(colour = "black",linewidth = .4),
            axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-11（c）复杂点带图（带均值横线+类别阴影+0值线）样式 

ggplot(data = strip_data_02,aes(x = variable,y=value)) +
    # 添加阴影区域
    geom_rect(aes(xmin=4.5,xmax=8.5,ymin=-1,ymax=3.2),
              fill="gray90")+
    #添加垂直虚线
    geom_vline(xintercept = 4.5,linetype="longdash",linewidth=0.6) +
    geom_hline(yintercept = 0,linetype="longdash",linewidth=0.4,alpha=0.8) +
    # 添加均值横线
    stat_summary(geom = "crossbar",fun = mean,
        colour = "black",linewidth = 0.4, width = 0.8,
        show.legend = FALSE) +
    geom_jitter(aes(fill=type,group=group),shape=21,size=3,stroke = 0.5,
                width = 0.2) +
    #设置图例成2行
    guides(fill = guide_legend(nrow = 2)) +
     scale_x_discrete(labels = c("","Non Contact","","",
                                 "","Contact","","")) +
     scale_y_continuous(expand = c(0, 0),breaks = seq(-1,4,1),
                         limits = c(-1, 4)) +
    ggprism::scale_fill_prism(palette = "waves") +
    labs(x="",y="Values") +
      theme_classic()+
      theme(legend.position=c(0.5,0.96),
            legend.title = element_blank(),
            text = element_text(family = "serif",face='bold',size = 18),
            axis.text = element_text(colour = "black",face='bold',size = 15),
            axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                     hjust = 0.3),
            axis.ticks.length=unit(.2, "cm"),
            #删除x轴刻度
            axis.ticks.x = element_blank(),
            panel.grid = element_blank(), #去除副网格
            #显示更多刻度内容
            plot.margin = margin(10, 10, 10, 10),
            axis.ticks = element_line(colour = "black",linewidth = .4),
            axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   

####################################图4-1-11（d）复杂点带图（带显著性P值）样式

library(ggsignif)
library(rstatix)

#计算显著性P值
strip_data_02 <- read_excel("\\第4章 双变量图形的绘制\\strip charts_pro.xlsx")
#改变绘图属性顺序
strip_data_02$variable <- factor(strip_data_02$variable, 
                        levels=c("NS_Smk","SMK_Smk","NS+abx_Smk","SMK+abx_Smk",
                                 "NS_Cess","SMK_Cess","NS+abx_Cess","SMK+abx_Cess"))
strip_data_02 %>%
  group_by(group) %>%
  rstatix::t_test(
    value ~ type, 
    p.adjust.method = "BH", 
    var.equal = TRUE, 
  )								 

ggplot(data = strip_data_02,aes(x = variable,y=value)) +
  # 添加阴影区域
    geom_rect(aes(xmin=4.5,xmax=8.5,ymin=-1,ymax=4),
              fill="gray90")+
    #添加垂直虚线
    geom_vline(xintercept = 4.5,linetype="longdash",linewidth=0.6) +
    geom_hline(yintercept = 0,linetype="longdash",linewidth=0.4,alpha=0.8) +
    # #添加均值横线
    stat_summary(geom = "crossbar",fun = mean,
        colour = "black",linewidth = 0.4, width = 0.8,
        show.legend = FALSE) +
    geom_jitter(aes(fill=type,group=group),shape=21,size=3,stroke = 0.5,
                width = 0.2) +
     #添加显著性P值
     ggsignif::geom_signif(y_position = c(2.6, 2.8,3.1,3.5),
                           xmin = c(1, 3,5,6), xmax = c(2, 4,6,8),
                           annotation = c("****","****","****","NS"), 
                           textsize = 5,tip_length = 0.0,
                           family = "serif") +
    #设置图例成2行
    guides(fill = guide_legend(nrow = 2)) +
     scale_x_discrete(
                      labels = c("","Contact","","","","Non_Contact","","")) +
     scale_y_continuous(expand = c(0, 0),breaks = seq(-1,4,1),
                         limits = c(-1, 4)) +
    ggprism::scale_fill_prism(palette = "waves") +

    labs(x="",y="Values") +
      theme_classic()+
      theme(legend.position="top",
            legend.margin = margin(6, 6, -6, -15),
            legend.title = element_blank(),
            legend.text = element_text(size = 12),
            text = element_text(family = "serif",face='bold',size = 18),
            axis.text = element_text(colour = "black",face='bold',size = 15),
            axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                     hjust = 0.3),
            axis.ticks.length=unit(.2, "cm"),
            #删除x轴刻度
            axis.ticks.x = element_blank(),
            #显示更多刻度内容
            plot.margin = margin(10, 10, 10, 10),
            axis.ticks = element_line(colour = "black",linewidth = .4),
            axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_d.png",
       width =4.5, height = 4.3, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-11 复杂统计点带图绘制示例_d.pdf",
       width =4.5, height = 4.3,device = cairo_pdf)
