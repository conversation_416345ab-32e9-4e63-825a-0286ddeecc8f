"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
tips <- read_excel("\\第4章 双变量图形的绘制\\tips.xlsx")

										
####################################图4-1-6（a）基本类别折线图
order <- c("Thur","Fri","Sat","Sun")
ggpubr::ggline(data=tips,x="day", y="total_bill",order = order,
               size=1,point.size = 3,xlab="Time",ylab="Values",add = "mean") +
 scale_y_continuous(expand = c(0, 0),limits = c(15, 23)) +
 theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-6（b）类别折线图+SE估计
order <- c("Thur","Fri","Sat","Sun")
ggpubr::ggline(data=tips,x="day", y="total_bill",order = order,
               size=0.8,point.size = 3,xlab="Time",ylab="Values",
               add = "mean_se",add.params=list(color="red")) +
 theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-6（c）类别折线图+SD估计 
order <- c("Thur","Fri","Sat","Sun")
ggpubr::ggline(data=tips,x="day", y="total_bill",order = order,
               size=0.8,point.size = 3,xlab="Time",ylab="Values",
               add = "mean_sd",add.params=list(color="red")) +
 theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-6（d）类别折线图+CI估计
order <- c("Thur","Fri","Sat","Sun")
ggpubr::ggline(data=tips,x="day", y="total_bill",order = order,
               size=0.8,point.size = 3,xlab="Time",ylab="Values",
               add = "mean_ci",add.params=list(color="red")) +
 theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-6 利用ggline () 函数绘制的类别折线图示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)
