"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(plotrix)


library(extrafont) 
font_import(pattern = "lmroman*")


#############################################图8-3-1(a) cropland类型模型精度泰勒图


taylor_data <- read_excel("F:\\书籍编写\\R语言-学术图表手册\\第8章 学术图绘制案例\\Taylor_data.xlsx"
                          ,sheet="cropland")

ref <- taylor_data$cropland_obser
model1 <- taylor_data$croplandDNN
model2 <- taylor_data$croplandGBRT
model3 <- taylor_data$croplandLR
model4 <- taylor_data$croplandSVM	
									  												 												  
png(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_a.png",
    family ="LM Roman 10",width = 5100, height = 5300,res=1200)
pdf(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_a.pdf",
    family ="LM Roman 10",width = 5, height = 5.3)	
	
par(family = "LM Roman 10",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 2.5,ref.sd=T,col = "#352A87",main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 2.5,add=TRUE,col="#089BCE")
taylor.diagram(ref,model3,pch = 17,pcex = 2.5,add=TRUE,col="#A3BD6A")
taylor.diagram(ref,model4,pch = 18,pcex = 2.5,add=TRUE,col="#F9FB0E")
legend(x=20,y=20, # 可以改变图例的位置
       legend=c("DNN","GBRT", "LR","SVR"),
       pch=c(15,16,17,18,19),col=c('#352A87','#089BCE','#A3BD6A','#F9FB0E'))
dev.off()

#############################################图8-3-1(b) forest类型模型精度泰勒图
taylor_data <- read_excel("\\第8章 学术图绘制案例\\Taylor_data.xlsx"
                          ,sheet="forest")
ref <- taylor_data$forest_obser
model1 <- taylor_data$forestDNN
model2 <- taylor_data$forestGBRT
model3 <- taylor_data$forestLR
model4 <- taylor_data$forestSVM


png(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_b.png",
    family ="LM Roman 10",width = 5100, height = 5300,res=1200)
	
pdf(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_b.pdf",
    family ="LM Roman 10",width = 5, height = 5.3)
	
par(family = "LM Roman 10",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 2.5,ref.sd=T,col = "#352A87",main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 2.5,add=TRUE,col="#089BCE")
taylor.diagram(ref,model3,pch = 17,pcex = 2.5,add=TRUE,col="#A3BD6A")
taylor.diagram(ref,model4,pch = 18,pcex = 2.5,add=TRUE,col="#F9FB0E")
legend(x=20,y=20, # 可以改变图例的位置
       legend=c("DNN","GBRT", "LR","SVR"),
       pch=c(15,16,17,18,19),col=c('#352A87','#089BCE','#A3BD6A','#F9FB0E'))
dev.off()

#############################################图8-3-1(c) grassland类型模型精度泰勒图
taylor_data <- read_excel("\\第8章 学术图绘制案例\\Taylor_data.xlsx"
                          ,sheet="grassland")
ref <- taylor_data$grassland_obser
model1 <- taylor_data$grasslandDNN
model2 <- taylor_data$grasslandGBRT
model3 <- taylor_data$grasslandLR
model4 <- taylor_data$grasslandSVM

png(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_c.png",
    family ="LM Roman 10",width = 5100, height = 5300,res=1200)
	
pdf(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_c.pdf",
    family ="LM Roman 10",width = 5, height = 5.3)	
	
	
par(family = "LM Roman 10",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 2.5,ref.sd=T,col = "#352A87",main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 2.5,add=TRUE,col="#089BCE")
taylor.diagram(ref,model3,pch = 17,pcex = 2.5,add=TRUE,col="#A3BD6A")
taylor.diagram(ref,model4,pch = 18,pcex = 2.5,add=TRUE,col="#F9FB0E")
legend(x=20,y=20, # 可以改变图例的位置
       legend=c("DNN","GBRT", "LR","SVR"),
       pch=c(15,16,17,18,19),col=c('#352A87','#089BCE','#A3BD6A','#F9FB0E'))
dev.off()						  

#############################################图8-3-1(d) savanna类型模型精度泰勒图

taylor_data <- read_excel("\\第8章 学术图绘制案例\\Taylor_data.xlsx"
                          ,sheet="savanna")
						  
ref <- taylor_data$savanna_obser
model1 <- taylor_data$savannaDNN
model2 <- taylor_data$savannaGBRT
model3 <- taylor_data$savannaLR
model4 <- taylor_data$savannaSVM

png(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_d.png",
    family ="LM Roman 10",width = 5100, height = 5300,res=1200)
	
pdf(file="\\第8章 学术图绘制案例\\图8-3-1 不同地物类型对应模型评价_d.pdf",
    family ="LM Roman 10",width = 5, height = 5.3)	
	
par(family = "LM Roman 10",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 2.5,ref.sd=T,col = "#352A87",main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 2.5,add=TRUE,col="#089BCE")
taylor.diagram(ref,model3,pch = 17,pcex = 2.5,add=TRUE,col="#A3BD6A")
taylor.diagram(ref,model4,pch = 18,pcex = 2.5,add=TRUE,col="#F9FB0E")
legend(x=17,y=16, # 可以改变图例的位置
       legend=c("DNN","GBRT", "LR","SVR"),
       pch=c(15,16,17,18,19),col=c('#352A87','#089BCE','#A3BD6A','#F9FB0E'))
dev.off()
