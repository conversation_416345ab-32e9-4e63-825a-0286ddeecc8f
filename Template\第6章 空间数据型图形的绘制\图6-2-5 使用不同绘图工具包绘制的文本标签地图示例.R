"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)
library(ggrepel)
library(ggsflabel)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")



####################################图6-2-5（a）使用ggrepel绘制的文本标签地图示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  ggrepel::geom_label_repel(data = map_fig02,aes(label=country,geometry = geometry),
                            stat = "sf_coordinates",min.segment.length = 0,
                            size=3,family="serif") +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        legend.background = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-5 使用不同绘图工具包绘制的文本标签地图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-5 使用不同绘图工具包绘制的文本标签地图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-2-5（b）使用ggsflabel绘制的文本标签地图示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  ggsflabel::geom_sf_label_repel(data = map_fig02,aes(label=country,geometry = geometry),
                            size=3,family="serif") +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        legend.background = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-5 使用不同绘图工具包绘制的文本标签地图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-5 使用不同绘图工具包绘制的文本标签地图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)