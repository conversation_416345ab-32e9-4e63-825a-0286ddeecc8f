"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(factoextra)
library(FactoMineR)


data(decathlon2)
decathlon2.active <- decathlon2[1:23, 1:10]

#pca计算
single_data <- decathlon2.active
res.pca <- FactoMineR::PCA(single_data, graph = FALSE)


####################################图5-7-1（a）单组数据主成分分析图绘制示例一

fviz_pca_var(res.pca, col.var = "black",repel = TRUE,
             font.family="serif") +
   theme(
        text = element_text(family = "serif",face='bold',size = 15),
        #axis.title = element_text(size = 7.5),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_a.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_a.pdf",
       width =5.5, height = 5,device = cairo_pdf) 

	   
####################################图5-7-1（b）单组数据主成分分析图绘制示例二(cos2变量)

fviz_pca_var(res.pca, col.var = "cos2",font.family="serif",
             gradient.cols = parula(100),
             repel = TRUE # Avoid text overlapping
             )+
   theme(
        text = element_text(family = "serif",face='bold',size = 15),
        #axis.title = element_text(size = 7.5),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_b.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_b.pdf",
       width =5.5, height = 5,device = cairo_pdf)

####################################图5-7-1（c）单组数据主成分分析图绘制示例三(contrib变量)

colors <- rev(RColorBrewer::brewer.pal(11, "Spectral"))
fviz_pca_var(res.pca, col.var = "contrib",
             font.family="serif",
             gradient.cols = colors,
             repel = TRUE) +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_c.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_c.pdf",
       width =5.5, height = 5,device = cairo_pdf)
	   

  
####################################图5-7-1（d）单组数据主成分分析图绘制示例四(fviz_pca_ind())
	 
fviz_pca_ind(res.pca, col.ind = "contrib",  font.family="serif",
             gradient.cols = parula(100),
             repel = TRUE 
             ) +
theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_d.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_d.pdf",
       width =5.5, height = 5,device = cairo_pdf)  


####################################图5-7-1（e）单组数据主成分分析图绘制示例五(fviz_pca_ind())

fviz_pca_ind(res.pca, pointsize = "cos2", font.family="serif",
             pointshape = 21, fill = "red",repel = TRUE ) +
 theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_e.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_e.pdf",
       width =5.5, height = 5,device = cairo_pdf)


####################################图5-7-1（f）单组数据主成分分析图绘制示例六(fviz_pca_ind())

fviz_pca_ind(res.pca, col.ind = "cos2",pointsize = "contrib",
             gradient.cols = parula(100),font.family="serif",repel = TRUE) +
 
 theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_f.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-1 单组数据主成分分析图绘制示例_f.pdf",
       width =5.5, height = 5,device = cairo_pdf)
