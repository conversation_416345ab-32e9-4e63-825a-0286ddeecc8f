"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


#构建数据
file <- "\\第5章 多变量图形的绘制\\coutour_data.xlsx"

contour_data_matrix <- read_excel(file) %>% tibble::column_to_rownames("index") %>% as.matrix()

colnames(contour_data_matrix) <- seq(1,ncol(contour_data_matrix),by=1) #列名设置
rownames(contour_data_matrix) <- seq(1,ncol(contour_data_matrix),by=1) #行名设置

#转换成长数据类型
mtrx.melt <- reshape2::melt(contour_data_matrix)
											  												 												  
####################################图5-1-4（a）“宽”数据等值线图绘制示例1

ggplot(mtrx.melt, aes(x=Var1,y=Var2,z=value))+
  geom_contour(aes(colour = after_stat(level))) +
  metR::geom_text_contour(size=5,family="serif",stroke = 0.2) +
  labs(x='X Axis title', y='Y Axis title') +
  scale_colour_gradientn(colours = parula(100)) +
  scale_x_continuous(expand = c(0,0)) +
  scale_y_continuous(expand = c(0,0)) +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size=12),
        panel.grid = element_blank(), #去除网格
        axis.ticks.length=unit(0.2, "cm"),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-4 “宽”数据等值线图绘制示例_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-4 “宽”数据等值线图绘制示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)
	   
####################################图5-1-4（b）“宽”数据等值线图绘制示例2

ggplot(mtrx.melt, aes(x=Var1,y=Var2,z=value))+
  geom_tile(aes(fill=value))+
  #metR::geom_text_contour(size=5,family="serif",stroke = 0.2) +
  geomtextpath::geom_textcontour(size=4.5,linecolour="white",textcolour="red",family="serif") +
  labs(x='X Axis title', y='Y Axis title') +
  scale_fill_gradientn(colours = parula(100)) +
  scale_x_continuous(expand = c(0,0)) +
  scale_y_continuous(expand = c(0,0)) +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size=12),
        panel.grid = element_blank(), #去除网格
        axis.ticks.length=unit(0.2, "cm"),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-4 “宽”数据等值线图绘制示例_b.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-1-4 “宽”数据等值线图绘制示例_b.pdf",
       width =5, height = 4,device = cairo_pdf)
   