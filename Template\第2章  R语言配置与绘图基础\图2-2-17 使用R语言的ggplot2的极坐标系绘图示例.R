"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
x <- tibble(x = head(seq(0, 3 * (2 * pi), by = pi/180), -1))
x <- x %>% mutate(sine = sin(3 * x)/3 + 1 + x/180, rads = x%%(2 * pi))

###############################################图2-2-17（a）使用R语言的ggplot2的极坐标系绘图示例2

ggplot(x) + 
 geom_path(aes(x = rads, y = sine)) + 
 ylim(c(0, 1.5)) + 
 labs(x="", y = "")+
 coord_polar(theta = "x") +
 theme_minimal(base_family = "serif") +
 theme(text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        plot.background = element_rect(fill = "white", colour = NA),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major = element_line(colour = "grey50"),
        panel.grid.minor = element_line(colour = "grey50"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-17 使用R语言的ggplot2的极坐标系绘图示例_a.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-17 使用R语言的ggplot2的极坐标系绘图示例_a.pdf",
       width = 4.2, height = 4,device = cairo_pdf)

###############################################图2-2-17（b）使用R语言的ggplot2的极坐标系绘图示例2

x <- tibble(x = head(seq(0, 3 * (2 * pi), by = pi/180), -1))
x <- x %>% mutate(sine = sin(3 * x)/3 + 1 + x/180, rads = x%%(2 * pi))
# Atempt 1
ggplot(x) + geom_line(aes(x = x, y = sine),size=1) + 
  ylim(c(0, 1.5)) + 
  labs(x="", y = "")+
  coord_polar(theta = "x") +
  theme_minimal(base_family = "serif") +
  theme(text = element_text(family = "serif",size = 16),
        axis.text = element_text(colour = "black"),
        plot.background = element_rect(fill = "white", colour = NA),
        panel.background = element_rect(fill = "white", colour = NA),
        panel.grid.major = element_line(colour = "grey50"),
        panel.grid.minor = element_line(colour = "grey50"))
  
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-17 使用R语言的ggplot2的极坐标系绘图示例_b.png",
       width = 4.2, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-17 使用R语言的ggplot2的极坐标系绘图示例_b.pdf",
       width = 4.2, height = 4,device = cairo_pdf)
	   
	   