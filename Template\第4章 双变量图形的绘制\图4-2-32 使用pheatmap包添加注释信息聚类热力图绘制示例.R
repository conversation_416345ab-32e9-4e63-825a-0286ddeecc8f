"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pheatmap)
library(patchwork)


#读取数据
cluster_01 <- read_excel("\\第4章 双变量图形的绘制\\cluster_heatmap_01.xlsx")
cluster_01 <- cluster_01 %>% column_to_rownames(var = "Index")
cluster_01_mat <- as.matrix.data.frame(cluster_01)	
cluster_01_mat <- cluster_01_mat %>% t()


hm_gg2<-as.ggplot(
         pheatmap(cluster_01_mat,scale="row",border_color="black",
         annotation_row = dfv,annotation_legend=FALSE,
         cluster_rows=FALSE,
         color=colorRampPalette(c("navy", "white", "red"))(50))
         )

ggsave(hm_gg2,filename = "\\第4章 双变量图形的绘制\\图4-2-32 使用pheatmap包添加注释信息聚类热力图绘制示例.png",
       width =9, height = 3.5, bg="white",dpi = 900,device=png)
ggsave(hm_gg2,filename = "\\第4章 双变量图形的绘制\\图4-2-32 使用pheatmap包添加注释信息聚类热力图绘制示例.pdf",
       width =9, height = 3.5,device = cairo_pdf)
