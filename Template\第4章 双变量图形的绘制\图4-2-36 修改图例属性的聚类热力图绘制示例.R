"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggplotify)
library(ComplexHeatmap)


#读取数据
cluster_01 <- read_excel("\\第4章 双变量图形的绘制\\cluster_heatmap_01.xlsx")

cluster_01 <- cluster_01 %>% column_to_rownames(var = "Index")
#转置矩阵
cluster_01_mat <- as.matrix.data.frame(cluster_01) %>% t()

col2 <- parula(50)

col_set <- c('Brain'="#6AC1B7",'Heart'="#EA5559",'Kidney'="#8FBF7E",
             'Liver'="#DD0C1A",'Lung'="#A15EA2",
             'Sc'="#EE765C",'Vis'="#5A7EBF")


	   
llibrary(circlize)
set.seed(123)
pvalue = 10^-runif(26, min = 0, max = 3)
is_sig = pvalue < 0.01
pch = rep("*", 26)
pch[!is_sig] = NA
# color mapping for -log10(pvalue)
pvalue_col_fun =  colorRamp2(c(0, 2, 3), c("navy", "white", "red"))

ht_p = Heatmap(cluster_01_mat,col = col2,name = "value",
        rect_gp = gpar(col = "black", lwd = 0.5),
        row_dend_side = "left",
        heatmap_legend_param=list(border = "black",
                                  legend_height = unit(2.5, "cm"),
                                  tick_length = unit(0, "mm")),
        top_annotation = ha_p)

ha_p = HeatmapAnnotation(
    pvalue = anno_simple(-log10(pvalue), 
                         col = pvalue_col_fun, 
                         pch = pch,which="column",
                         pt_size = unit(1, "snpc")*1,
                         gp = gpar(col="black",lwd = 0.5)),
    annotation_name_side = "left")

lgd_pvalue = Legend(title = "p-value", 
                    col_fun = pvalue_col_fun, 
                    at = c(0, 1, 2, 3), 
                    labels = c("1", "0.1", "0.01", "0.001"),
                    border = "black",
                    legend_height = unit(2.5, "cm"),
                    tick_length = unit(0, "mm"))
lgd_sig = Legend(pch = "*", type = "points", labels = "< 0.01",
                 border = "black",
                 background = "white",labels_gp = gpar(fontsize = 11,
                                                     col="red"))

draw(ht_p, annotation_legend_list = list(lgd_pvalue, lgd_sig))


png(file="\\第4章 双变量图形的绘制\\图4-2-36 修改图例属性的聚类热力图绘制示例.png",
    width = 8000, height = 3000,res=1000)
draw(ht_p, annotation_legend_list = list(lgd_pvalue, lgd_sig))
dev.off()

pdf(file="\\第4章 双变量图形的绘制\\图4-2-36 修改图例属性的聚类热力图绘制示例.pdf",
    width = 8, height = 3,)
draw(ht_p, annotation_legend_list = list(lgd_pvalue, lgd_sig))
dev.off()

	
	
