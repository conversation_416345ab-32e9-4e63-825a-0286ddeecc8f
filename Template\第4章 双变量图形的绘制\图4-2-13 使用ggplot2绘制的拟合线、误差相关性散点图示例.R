"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
file <- "\\第4章 双变量图形的绘制\\散点图样例数据.xlsx"
scatter_data <- read_excel(file)

num <- nrow(scatter_data)
											  
											  
####################################图4-2-13（a）使用ggplot2绘制的拟合线相关性散点图示例

ggplot(data = scatter_data,aes(x = values,y = pred_values)) +
  geom_point(shape=22,size=2.5,fill="black") +
  geom_smooth(aes(colour="Fitted Line"),method = "lm", se=FALSE, formula = y ~ x) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(colour="1:1 Line",intercept=0, slope=1),
              alpha=1, size=.5) + 
  ggpubr::stat_regline_equation(label.x = .1,label.y = 1.65,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(..rr.label.., ..p.label.., sep = "~`,`~")),
                   label.x = .1, label.y = 1.45,size=6,
                   r.accuracy = 0.01,p.accuracy = 0.001,
                   family='serif',fontface='bold') +
  #geom_text(x=.1,y=1.25,label=paste("N = ",num),size=6,family='serif',hjust = 0) +
  annotate("text",x=0.1,y=1.2,label=paste("N = ",num),size=6,
            family='serif',hjust = 0) +
#修改坐标轴刻度
  scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  labs(x="X Label", y = "Y Label") +
  #添加不同线图例
  scale_colour_manual(name="", values=c("black","red")) +
  guides(colour = guide_legend(override.aes = list(alpha = 0)))+
  theme_bw() +
  theme(legend.position = c(.8,.15),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-13 使用ggplot2绘制的拟合线、误差相关性散点图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-13 使用ggplot2绘制的拟合线、误差相关性散点图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-13（b）ggplot2 绘制的误差相关性散点图示例

ggplot(data = scatter_data,aes(x = values,y = pred_values)) +
  geom_errorbar(aes(ymin = pred_values-y_error, ymax = pred_values+y_error),
               colour="gray40",size=.2) +
  geom_errorbarh(aes(xmin = values-x_error, xmax = values+x_error),
               colour="gray40",size=.2) +
  geom_point(shape=22,size=2.5,fill="black") +
  geom_smooth(aes(colour="Fitted Line"),method = "lm", se=FALSE, formula = y ~ x) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(colour="1:1 Line",intercept=0, slope=1),
              alpha=1, size=.5) + 
  ggpubr::stat_regline_equation(label.x = .1,label.y = 1.65,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(..rr.label.., ..p.label.., sep = "~`,`~")),
                   label.x = .1, label.y = 1.45,size=6,
                   r.accuracy = 0.01,p.accuracy = 0.001,
                   family='serif',fontface='bold') +
  #geom_text(x=.1,y=1.25,label=paste("N = ",num),size=6,family='serif',hjust = 0) +
  annotate("text",x=0.1,y=1.2,label=paste("N = ",num),size=6,
            family='serif',hjust = 0) +

#修改坐标轴刻度
  scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  labs(x="X Label", y = "Y Label") +
  #添加不同线图例
  scale_colour_manual(name="", values=c("black","red")) +
  guides(colour = guide_legend(override.aes = list(alpha = 0)))+
  theme_bw() +
  theme(legend.position = c(.8,.15),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-13 使用ggplot2绘制的拟合线、误差相关性散点图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-13 使用ggplot2绘制的拟合线、误差相关性散点图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)