"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#读取数据

file <- "\\第3章 单变量图表绘制\\统计直方图.xlsx"
hist_data <- readxl::read_xlsx(file)

breaks <- seq(0,1.5,by = 0.1)

####################################图3-1-1（a）使用ggplot2包的geom_histogram() 函数 绘制的直方图
ggplot() +
  geom_histogram(data = hist_data,aes(hist_data),breaks = breaks,
                 fill="gray",colour="black",linewidth=0.3) +
  scale_y_continuous(expand = c(0, 0), limits = c(0, 2500),
                     breaks = seq(0, 2500, by = 500)) +
  scale_x_continuous(breaks = breaks) +
  theme_classic()+
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.png",
       width = 5.5, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.pdf",
       width = 5.5, height = 4,device = cairo_pdf)


####################################图3-1-1（b）使用ggplot2包的geom_histogram() 函数 绘制的直方图
library(ggstatsplot)

gghistostats(data = hist_data,x = hist_data,binwidth = 0.1,
             normal.curve = TRUE,normal.curve.args = list(color = "red",size = 0.5)) +
 theme(text = element_text(family = "serif"))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-1-1 直方图示例_b.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-1-1 直方图示例_b.pdf",
       width =5, height = 4.5,device = cairo_pdf)
