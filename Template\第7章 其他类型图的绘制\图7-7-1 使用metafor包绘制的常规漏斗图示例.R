
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(metafor)
 
### fit equal-effects model
res <- rma(yi, vi, data=dat.hackshaw1998, measure="OR", method="EE")


png(file="\\第7章 其他类型图的绘制\\图7-7-1 使用metafor包绘制的常规漏斗图示例.png",
    family ="serif",width = 8000, height = 5000,res=1000)
	
par(family = "serif",mfrow=c(2,2),mar = rep(2,4))
### draw funnel plots
funnel(res, main="Standard Error")
funnel(res, yaxis="vi", main="Sampling Variance")
funnel(res, yaxis="seinv", main="Inverse Standard Error")
funnel(res, yaxis="vinv", main="Inverse Sampling Variance")
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-7-1 使用metafor包绘制的常规漏斗图示例.pdf",
    family ="serif",width = 8, height = 5)
par(mfrow=c(2,2),mar = rep(2,4))
### draw funnel plots
funnel(res, main="Standard Error")
funnel(res, yaxis="vi", main="Sampling Variance")
funnel(res, yaxis="seinv", main="Inverse Standard Error")
funnel(res, yaxis="vinv", main="Inverse Sampling Variance")
dev.off()