# R包开发完成定义(DoD)检查清单

## 开发者代理使用说明

在将R包开发任务标记为"Review"之前，请逐项检查此清单。对每项进行状态标记（[x] 完成，[ ] 未完成，[N/A] 不适用）并提供必要的说明。

[[LLM: R包开发DoD验证指令

这是R包开发专用的完成定义检查清单，用于开发者代理自我验证工作质量。

重要原则：诚实评估实际完成情况vs应该完成的内容。发现问题比在审查中被发现更好。

执行方法：
1. 系统性检查每个部分
2. 标记为 [x] 完成、[ ] 未完成或 [N/A] 不适用
3. 为[ ]或[N/A]项目添加简要说明
4. 具体说明实际实现的内容
5. 标记任何关注点或技术债务

目标是质量交付，而非简单勾选框。]]

## 检查清单项目

### 1. **R包结构和配置**

[[LLM: 检查R包的基础结构是否符合CRAN标准]]

- [ ] DESCRIPTION文件完整且符合CRAN要求（包名、版本、标题、描述、作者、许可证）
- [ ] NAMESPACE文件正确生成（通过roxygen2或手动维护）
- [ ] R/目录包含所有R源代码文件
- [ ] man/目录包含所有函数的帮助文档
- [ ] 包结构遵循标准R包布局

### 2. **代码质量和标准**

[[LLM: 代码质量直接影响包的可维护性，仔细检查每项]]

- [ ] 所有代码严格遵循tidyverse编码风格指南
- [ ] 所有函数和变量使用一致的命名规范（snake_case）
- [ ] 代码通过lintr检查，无错误和警告
- [ ] 所有导出函数有完整的roxygen2文档
- [ ] 复杂逻辑有适当的内联注释
- [ ] 没有硬编码路径或配置
- [ ] 代码模块化良好，避免重复

### 3. **测试要求**

[[LLM: 测试证明代码正常工作，诚实评估测试覆盖率]]

- [ ] 使用testthat框架编写单元测试
- [ ] 测试覆盖率达到80%以上
- [ ] 所有导出函数都有对应测试
- [ ] 测试包含边界条件和错误处理场景
- [ ] 所有测试通过（test_that()无失败）
- [ ] 测试数据和fixtures适当组织
- [ ] 测试代码清晰且有意义的描述

### 4. **R CMD check验证**

[[LLM: R CMD check是CRAN的基本要求，必须全部通过]]

- [ ] R CMD check --as-cran 无错误(ERROR)
- [ ] R CMD check --as-cran 无警告(WARNING)  
- [ ] R CMD check --as-cran 无注意事项(NOTE)或已解释
- [ ] 包可以在多个R版本上构建（如果适用）
- [ ] 所有示例代码可以运行
- [ ] vignettes可以正确构建（如果存在）

### 5. **文档完整性**

[[LLM: 良好的文档对用户至关重要，检查文档质量]]

- [ ] README.md包含安装说明和基本使用示例
- [ ] 所有导出函数有完整的roxygen2文档（@param, @return, @examples）
- [ ] 包级别文档存在（package.Rd）
- [ ] 至少有一个vignette展示主要功能
- [ ] NEWS.md记录版本变更
- [ ] 数据集有完整文档（如果包含数据）
- [ ] 文档示例可以运行且有意义

### 6. **依赖管理**

[[LLM: 依赖管理影响包的稳定性和安全性]]

- [ ] 所有依赖包在DESCRIPTION中正确声明
- [ ] 依赖版本要求合理且经过测试
- [ ] 没有引入不必要的重依赖
- [ ] 所有依赖包许可证兼容
- [ ] 依赖包维护状态良好（非孤儿包）
- [ ] 新增依赖已获得批准或有充分理由
- [ ] 没有已知的安全漏洞

### 7. **功能验证**

[[LLM: 实际运行和测试代码，确保功能正常]]

- [ ] 包可以成功安装（install.packages或devtools::install）
- [ ] 包可以正常加载（library()）
- [ ] 所有主要功能经过手动验证
- [ ] 示例代码在新R会话中可以运行
- [ ] 错误处理机制工作正常
- [ ] 边界条件和特殊情况处理得当

### 8. **开发任务管理**

[[LLM: 开发过程的文档化有助于后续维护]]

- [ ] 开发任务文件中所有任务标记为完成
- [ ] 开发过程中的决策和澄清已记录
- [ ] 变更日志正确更新
- [ ] 文件清单完整（所有创建/修改的文件）
- [ ] 任何偏离原始需求的地方已记录说明

### 9. **CRAN准备（如果适用）**

[[LLM: CRAN提交有特殊要求，确保合规]]

- [ ] 包名在CRAN上不冲突
- [ ] 许可证声明正确且CRAN兼容
- [ ] 作者和维护者信息完整
- [ ] 包大小在合理范围内
- [ ] 没有违反CRAN政策的内容
- [ ] cran-comments.md文件准备（如果需要）

### 10. **版本控制和协作**

[[LLM: 版本控制确保代码历史和协作]]

- [ ] 所有代码变更已提交到版本控制
- [ ] 提交信息清晰且有意义
- [ ] 分支策略合理（如果使用分支）
- [ ] 代码审查已完成（如果适用）
- [ ] 标签标记版本（如果发布）

## 最终确认

[[LLM: 最终DoD总结

完成检查清单后：

1. 总结此次开发完成的内容
2. 列出标记为[ ]未完成的项目及解释
3. 识别任何技术债务或后续工作
4. 记录开发过程中的挑战或学习
5. 确认包是否真正准备好进行审查

诚实评估 - 现在发现问题比后续发现更好。]]

- [ ] 我，开发者代理，确认上述所有适用项目都已处理完成。

**包开发总结**：
{{package_summary}}

**未完成项目说明**：
{{incomplete_items}}

**技术债务或后续工作**：
{{technical_debt}}

**开发挑战和学习**：
{{challenges_learned}}

**审查准备状态**：{{ready_for_review}}
