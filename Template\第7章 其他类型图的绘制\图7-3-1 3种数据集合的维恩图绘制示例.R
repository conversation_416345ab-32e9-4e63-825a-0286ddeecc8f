
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)
library(ggVennDiagram)


#读取数据
venn_data = readr::read_table("\\第7章 其他类型图的绘制\\data2.txt")
venn_list <- venn_data %>% as.list()

####################################图7-3-1（a）3个数据集合的维恩图绘制示例

ggVennDiagram(venn_list[1:3]) +
  scale_fill_gradientn(colours = parula(100)) 
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)

####################################图7-3-1（b）4个数据集合的维恩图绘制示例

ggVennDiagram(venn_list[1:4]) +
  scale_fill_gradientn(colours = parula(100)) 
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
	   
####################################图7-3-1（c）5个数据集合的维恩图绘制示例
ggVennDiagram(venn_list[1:5],color="red") +
  scale_fill_gradientn(colours = parula(100)) 
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_c.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-3-1 3种数据集合的维恩图绘制示例_c.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)


