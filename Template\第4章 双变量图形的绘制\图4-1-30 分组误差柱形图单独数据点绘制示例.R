"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggsignif)

#构建数据集

paper_group_bar <- read_excel("\\第4章 双变量图形的绘制\\paper_group_bar_data 01.xlsx")

									

####################################图4-1-30（a）ggpubr 对照实验数据点柱形图 
ggpubr::ggbarplot(data = paper_group_bar,x="Gene",y="Value",fill="Treatment",
                  add="mean_sd",,palette="npg",width=0.8,
                  ylab="Mean value",xlab="",legend = "top",
                  position = position_dodge(0.8),
                  add.params= list(width = 0.2,size=0.3),
                  ggtheme=theme_classic(base_family = "serif")) +
 geom_jitter(aes(x = Gene, y=Value,fill=Treatment), 
              shape=22,size=4,stroke=1,
              position = position_jitterdodge(jitter.width = 0.2, 
                                              dodge.width = 0.8)) +
 scale_y_continuous(expand = c(0, 0),limits = c(0,100),
                     breaks = seq(0,100,20)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "G:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\图4-1-30 分组误差柱形图单独数据点绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "G:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\图4-1-30 分组误差柱形图单独数据点绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-30（b）ggbarplot() 数据点误差柱形图（CI）
comparisons <- list(c("setosa", "versicolor"))

#进行分组计算
paper_stat_1 <- paper_group_bar %>% 
    group_by(Treatment) %>% 
    rstatix::wilcox_test(Value ~ Gene) %>% 
    rstatix::adjust_pvalue() %>%
    rstatix::add_significance() %>% 
    rstatix::add_xy_position(x = "Gene", group = "Treatment")


ggpubr::ggbarplot(data = paper_group_bar,x="Gene",y="Value",fill="Treatment",
                  add="mean_sd",,palette="npg",width=0.8,
                  ylab="Mean value",xlab="",legend = "top",
                  position = position_dodge(0.8),
                  add.params= list(width = 0.2,size=0.3),
                  ggtheme=theme_classic(base_family = "serif")) +
 geom_jitter(aes(x = Gene, y=Value,fill=Treatment), 
              shape=22,size=4,stroke=1,
              position = position_jitterdodge(jitter.width = 0.2, 
                                              dodge.width = 0.8)) +
 ggpubr::stat_pvalue_manual(paper_stat_1,label = "p.adj.signif",
                             tip.length = 0,step.increase = 0.05,
                             family="serif",
                             label.size=6)+ 
 scale_y_continuous(expand = c(0, 0),limits = c(0,100),
                     breaks = seq(0,100,20)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-30 分组误差柱形图单独数据点绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-30 分组误差柱形图单独数据点绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

   