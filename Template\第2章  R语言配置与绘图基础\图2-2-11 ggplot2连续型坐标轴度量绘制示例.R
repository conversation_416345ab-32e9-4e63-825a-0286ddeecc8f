"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-11（a）ggplot2线性坐标轴度量示例

ggplot(cars, aes(x = speed, y = dist)) + 
   geom_point(shape=21,size=6,fill="#0073C2") +
   labs(x="X Label", y = "Y Label")+
   scale_y_continuous(expand = c(0, 0), limits = c(0, 125),
                     breaks = seq(0, 125, by = 25)) +
   scale_x_continuous(expand = c(0, 0),limits = c(0, 30),
                     breaks = seq(0, 30, by = 5)) +
   theme(
        text = element_text(family = "serif",size = 20),
        axis.text = element_text(colour = "red"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_a.png",
       width = 4., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_a.pdf",
       width = 4., height = 4,device = cairo_pdf)

###############################################图2-2-11（b）ggplot2对数刻度度量示例

ggplot(cars, aes(x = speed, y = dist)) + 
   geom_point(shape=21,size=6,fill="#0073C2") +
   labs(x="X Label", y = "Y Label")+
   scale_y_continuous(trans = log2_trans(),
                      breaks = trans_breaks("log2", function(x) 2^x),
                      labels = trans_format("log2", math_format(2^.x))) +
   scale_x_continuous(trans = log2_trans(),
                      breaks = trans_breaks("log2", function(x) 2^x),
                      labels = trans_format("log2", math_format(2^.x)))+
   theme(
        text = element_text(family = "serif",size = 22),
        axis.text = element_text(colour = "red"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_b.png",
       width = 4., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_b.pdf",
       width = 4., height = 4,device = cairo_pdf)

###############################################图2-2-11（c）ggplot2百分比刻度度量示例
library(scales)
ggplot(cars, aes(x = speed, y = dist)) + 
   geom_point(shape=21,size=6,fill="#0073C2") +
   labs(x="X Label", y = "Y Label")+
   scale_y_continuous(labels = percent) +
   scale_x_continuous(labels = percent)+
   theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "red"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_c.png",
       width = 4., height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-11 ggplot2连续型坐标轴度量绘制示例_c.pdf",
       width = 4., height = 4,device = cairo_pdf)