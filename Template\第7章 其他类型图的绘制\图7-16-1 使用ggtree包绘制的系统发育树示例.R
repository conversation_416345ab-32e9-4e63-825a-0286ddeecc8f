
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(ggtree)


# The path of tree file.
trfile <- system.file("extdata", "tree.nwk", package="ggtreeExtra")
# The path of file to plot tip point.
tippoint1 <- system.file("extdata", "tree_tippoint_bar.csv", package="ggtreeExtra")
# The path of first layer outside of tree.
ring1 <- system.file("extdata", "first_ring_discrete.csv", package="ggtreeExtra")
# The path of second layer outside of tree.
ring2 <- system.file("extdata", "second_ring_continuous.csv", package="ggtreeExtra")

# The tree file was imported using read.tree. If you have other tree format files, you can use corresponding functions from treeio package to read it.
tree <- read.tree(trfile)

# This dataset will to be plotted point and bar.
dat1 <- read.csv(tippoint1)

dat2 <- read.csv(ring1)
dat3 <- read.csv(ring2)

###############################图7-16-1 (a) 使用ggtree包绘制的系统发育树示例1

p <- ggtree(tree, layout="fan", open.angle=10, size=0.5)

p2 <- p + 
      geom_fruit(
          data=dat1,
          geom=geom_star,
          mapping=aes(y=ID, fill=Location, size=Length, starshape=Group),
          position="identity",
          starstroke=0.2
      ) + 
      scale_size_continuous(
          range=c(1, 3), # the range of size.
          guide=guide_legend(
                    keywidth=0.5, 
                    keyheight=0.5,
                    override.aes=list(starshape=15),
                    order=2
                )
      ) +
      scale_fill_manual(
          values=c("#F8766D", "#C49A00", "#53B400", "#00C094", "#00B6EB", "#A58AFF", "#FB61D7"),
          guide="none" 
      ) + 
      scale_starshape_manual(
          values=c(1, 15),
          guide=guide_legend(
                    keywidth=0.5,
                    keyheight=0.5,
                    order=1
                )
      )

p3 <- p2 + 
      new_scale_fill() + 
      geom_fruit(
          data=dat2,
          geom=geom_tile,
          mapping=aes(y=ID, x=Pos, fill=Type),
          offset=0.08,   # The distance between external layers, default is 0.03 serif of x range of tree.
          pwidth=0.25 # width of the external layer, default is 0.2 serif of x range of tree.
      ) + 
      scale_fill_manual(
          values=c("#339933", "#dfac03"),
          guide=guide_legend(keywidth=0.5, keyheight=0.5, order=3)
      ) 


# You can also add heatmap layer for continuous values.
p4 <- p3 + 
      new_scale_fill() +
      geom_fruit(
          data=dat3,
          geom=geom_tile,
          mapping=aes(y=ID, x=Type2, alpha=Alpha, fill=Type2),
          pwidth=0.15,
          axis.params=list(
                          axis="x", # add axis text of the layer.
                          text.angle=-45, # the text angle of x-axis.
                          hjust=0  # adjust the horizontal position of text of axis.
                      )
      ) +
      scale_fill_manual(
          values=c("#b22222", "#005500", "#0000be", "#9f1f9f"),
          guide=guide_legend(keywidth=0.5, keyheight=0.5, order=4)
      ) +
      scale_alpha_continuous(
          range=c(0, 0.4), # the range of alpha
          guide=guide_legend(keywidth=0.5, keyheight=0.5, order=5)
      ) 

p5 <- p4 + 
      new_scale_fill() +
      geom_fruit(
          data=dat1,
          geom=geom_col,
          mapping=aes(y=ID, x=Abundance, fill=Location),  #The 'Abundance' of 'dat1' will be mapped to x
          pwidth=0.4,
          axis.params=list(
                          axis="x", # add axis text of the layer.
                          text.angle=-45, # the text size of axis.
                          hjust=0  # adjust the horizontal position of text of axis.
                      ),
          grid.params=list() # add the grid line of the external bar plot.
      ) + 
      scale_fill_manual(
          values=c("#F8766D", "#C49A00", "#53B400", "#00C094", "#00B6EB", "#A58AFF", "#FB61D7"),
          guide=guide_legend(keywidth=0.5, keyheight=0.5, order=6)
      ) +
      theme(#legend.position=c(0.96, 0.5), # the position of legend.
          legend.background=element_rect(fill=NA), # the background of legend.
          legend.title=element_text(size=7), # the title size of legend.
          legend.text=element_text(size=6), # the text size of legend.
          legend.spacing.y = unit(0.02, "cm")  # the distance of legends (y orientation).
      ) 

ggsave(p5,filename = "\\第7章 其他类型图的绘制\\图7-16-1 使用ggtree包绘制的系统发育树示例_a.png",
       width =8, height = 7, bg="white",dpi = 900,device=png)
ggsave(p5,filename = "\\第7章 其他类型图的绘制\\图7-16-1 使用ggtree包绘制的系统发育树示例_a.pdf",
       width =8, height = 7,device = cairo_pdf)

###############################图7-16-1 (b) 使用ggtree包绘制的系统发育树示例2

library(ggtreeExtra)
library(ggtree)
library(treeio)
library(tidytree)
library(ggstar)
library(ggplot2)
library(ggnewscale)

tree <- read.tree("\\第7章 其他类型图的绘制\\HMP_tree\\hmptree.nwk")
# the abundance and types of microbes
dat1 <- read.csv("\\第7章 其他类型图的绘制\\HMP_tree\\tippoint_attr.csv")
# the abundance of microbes at different body sites.
dat2 <- read.csv("\\第7章 其他类型图的绘制\\HMP_tree\\ringheatmap_attr.csv")
# the abundance of microbes at the body sites of greatest prevalence.
dat3 <- read.csv("\\第7章 其他类型图的绘制\\HMP_tree\\barplot_attr.csv")


# adjust the order
dat2$Sites <- factor(dat2$Sites, levels=c("Stool (prevalence)", "Cheek (prevalence)",
                                          "Plaque (prevalence)","Tongue (prevalence)",
                                          "Nose (prevalence)", "Vagina (prevalence)",
                                          "Skin (prevalence)"))
dat3$Sites <- factor(dat3$Sites, levels=c("Stool (prevalence)", "Cheek (prevalence)",
                                          "Plaque (prevalence)", "Tongue (prevalence)",
                                          "Nose (prevalence)", "Vagina (prevalence)",
                                          "Skin (prevalence)"))
# extract the clade label information. Because some nodes of tree are annotated to genera,
# which can be displayed with high light using ggtree.
nodeids <- nodeid(tree, tree$node.label[nchar(tree$node.label)>4])
nodedf <- data.frame(node=nodeids)
nodelab <- gsub("[\\.0-9]", "", tree$node.label[nchar(tree$node.label)>4])
# The layers of clade and hightlight
hightlight <- lapply(nodeids, function(x)geom_hilight(node=x, extendto=6.8, alpha=0.3,
                                                      fill="grey", color="grey50", size=0.05))
poslist <- c(1.6, 1.4, 1.6, 0.8, 0.1, 0.25, 1.6, 1.6, 1.2, 0.4,
             1.2, 1.8, 0.3, 0.8, 0.4, 0.3, 0.4, 0.4, 0.4, 0.6,
             0.3, 0.4, 0.3)
			 
# adjust the order
dat2$Sites <- factor(dat2$Sites, levels=c("Stool (prevalence)", "Cheek (prevalence)",
                                          "Plaque (prevalence)","Tongue (prevalence)",
                                          "Nose (prevalence)", "Vagina (prevalence)",
                                          "Skin (prevalence)"))
dat3$Sites <- factor(dat3$Sites, levels=c("Stool (prevalence)", "Cheek (prevalence)",
                                          "Plaque (prevalence)", "Tongue (prevalence)",
                                          "Nose (prevalence)", "Vagina (prevalence)",
                                          "Skin (prevalence)"))
# extract the clade label information. Because some nodes of tree are annotated to genera,
# which can be displayed with high light using ggtree.
nodeids <- nodeid(tree, tree$node.label[nchar(tree$node.label)>4])
nodedf <- data.frame(node=nodeids)
nodelab <- gsub("[\\.0-9]", "", tree$node.label[nchar(tree$node.label)>4])
# The layers of clade and hightlight
hightlight <- lapply(nodeids, function(x)geom_hilight(node=x, extendto=6.8, alpha=0.3,
                                                      fill="grey", color="grey50", size=0.05))
poslist <- c(1.6, 1.4, 1.6, 0.8, 0.1, 0.25, 1.6, 1.6, 1.2, 0.4,
             1.2, 1.8, 0.3, 0.8, 0.4, 0.3, 0.4, 0.4, 0.4, 0.6,
             0.3, 0.4, 0.3)
cladelabels <- mapply(function(x, y, z){geom_cladelabel(node=x, label=y, barsize=NA, extend=0,
                                                    offset.text=z, fontsize=1.4, angle="auto",
                                                    hjust=0.5, horizontal=FALSE, fontface="italic")},
                                     nodeids, nodelab, poslist, SIMPLIFY=FALSE)

# The circular layout tree.
p <- ggtree(tree, layout="fan", size=0.15, open.angle=5) +
     geom_hilight(data=nodedf, mapping=aes(node=node),
                  extendto=6.8, alpha=0.3, fill="grey", color="grey50",
                  size=0.05)


p <- p %<+% dat1 + geom_fruit(geom=geom_star,
                              mapping=aes(fill=Phylum, starshape=Type, size=Size),
                              position="identity",starstroke=0.1)+
         scale_fill_manual(values=c("#FFC125","#87CEFA","#7B68EE","#808080","#800080",
                                    "#9ACD32","#D15FEE","#FFC0CB","#EE6A50","#8DEEEE",
                                    "#006400","#800000","#B0171F","#191970"),
                           guide=guide_legend(keywidth = 0.5, keyheight = 0.5, order=1,
                                              override.aes=list(starshape=15)),
                           na.translate=FALSE)+
         scale_starshape_manual(values=c(15, 1),
                                guide=guide_legend(keywidth = 0.5, keyheight = 0.5, order=2),
                                na.translate=FALSE)+
         scale_size_continuous(range = c(1, 2.5),
                               guide = guide_legend(keywidth = 0.5, keyheight = 0.5, order=3,
                                                    override.aes=list(starshape=15)))+
         new_scale_fill()+
         geom_fruit(data=dat2, geom=geom_tile,
                    mapping=aes(y=ID, x=Sites, alpha=Abundance, fill=Sites),
                    color = "grey50", offset = 0.04,size = 0.02)+
         scale_alpha_continuous(range=c(0, 1),
                             guide=guide_legend(keywidth = 0.3, keyheight = 0.3, order=5)) +
         cladelabels +
         geom_fruit(data=dat3, geom=geom_bar,
                    mapping=aes(y=ID, x=HigherAbundance, fill=Sites),
                    pwidth=0.38, orientation="y", stat="identity")+
         scale_fill_manual(values=c("#0000FF","#FFA500","#FF0000","#800000",
                                    "#006400","#800080","#696969"),
                           guide=guide_legend(keywidth = 0.3, keyheight = 0.3, order=4))+
         geom_treescale(fontsize=1.2, linesize=0.3, x=4.9, y=0.1) +
         theme(legend.position=c(0.93, 0.5),
               legend.background=element_rect(fill=NA),
               legend.title=element_text(size=5),
               legend.text=element_text(size=4),
               legend.spacing.y = unit(0.02, "cm"))


ggsave(p,filename = "\\第7章 其他类型图的绘制\\图7-16-1 使用ggtree包绘制的系统发育树示例_b.png",
       width =8, height = 7, bg="white",dpi = 900,device=png)
ggsave(p,filename = "\\第7章 其他类型图的绘制\\图7-16-1 使用ggtree包绘制的系统发育树示例_b.pdf",
       width =8, height = 7,device = cairo_pdf)			   
			 