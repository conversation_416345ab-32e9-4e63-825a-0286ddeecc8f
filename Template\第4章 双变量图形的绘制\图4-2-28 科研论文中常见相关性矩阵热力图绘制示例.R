"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(psych)
library(rstatix)


#读取数据
df01 <- read.csv("\\第4章 双变量图形的绘制\\cor_data_01.csv")
df02 <- read.csv("\\第4章 双变量图形的绘制\\cor_data_02.csv")

cor_result<-corr.test(df01,df02)

#构建绘图数据集
cor_p <- cor_result$p %>% as.data.frame() %>% 
         rownames_to_column() %>% 
         pivot_longer(!rowname) %>% 
         mutate(p_value=case_when(
             value > 0.05 ~ " > 0.05",
             value > 0.01 & value <= 0.05 ~ "0.01 ~ 0.05",
             value > 0.001 & value <= 0.01 ~ "0.001 ~ 0.01",
             value <= 0.001 ~ " < 0.001",
          )) 
cor_r <- cor_result$r %>% as.data.frame() %>% 
         rownames_to_column() %>% 
         pivot_longer(!rowname) %>% 
         mutate(abs_cor=abs(value))

#改变绘图属性顺序
cor_p$p_value <- factor(cor_p$p_value, 
                          levels=c(' > 0.05','0.01 ~ 0.05',
                                   '0.001 ~ 0.01',' < 0.001'))		 
				  
ggplot()+
  geom_tile(data=cor_p,aes(x=rowname,y=name,fill=p_value,
                           alpha=p_value))+
  geom_point(data=cor_r,aes(x=rowname,y=name,size=abs_cor,
                            color=value)) +
  scale_fill_manual(values = c("white","#c0c0c0",
                               "#808080","#3f3f3f"))+
  scale_color_gradientn(colours = parula(100)) +
  scale_alpha_manual(values = c(0,1,1,1))+
  labs(x="",y="") +
  guides(alpha=FALSE,
         color=guide_colorbar(title = "Cor"),
         size = guide_legend(override.aes = list(shape = 21)),
         fill = guide_legend(keywidth = unit(0.8, "lines"),
                             keyheight = unit(1.5, "lines")))+
  theme_bw()+
  theme(legend.key = element_rect(colour="black"),
        text = element_text(family = "serif",face='bold',size = 12),
        axis.text = element_text(colour = "black",face='bold',size = 13),
        axis.text.x = element_text(angle = 90,hjust=1,vjust=0.5),
      #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-28 科研论文中常见相关性矩阵热力图绘制示例.png",
       width =9, height = 5.8, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-28 科研论文中常见相关性矩阵热力图绘制示例.pdf",
       width =9, height = 5.8,device = cairo_pdf)