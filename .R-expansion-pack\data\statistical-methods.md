# 统计方法指南

## 描述性统计

### 中心趋势测量
- **均值**: `mean(x, na.rm = TRUE)`
- **中位数**: `median(x, na.rm = TRUE)`
- **众数**: 自定义函数或使用`modeest`包
- **几何均值**: `exp(mean(log(x)))`

### 离散程度测量
- **标准差**: `sd(x, na.rm = TRUE)`
- **方差**: `var(x, na.rm = TRUE)`
- **四分位距**: `IQR(x, na.rm = TRUE)`
- **变异系数**: `sd(x)/mean(x)`

### 分布形状
- **偏度**: `moments::skewness(x)`
- **峰度**: `moments::kurtosis(x)`
- **分位数**: `quantile(x, probs = c(0.25, 0.5, 0.75))`

## 假设检验

### 正态性检验
```r
# Shapiro-Wilk检验（样本量 < 5000）
shapiro.test(data)

# Ko<PERSON><PERSON><PERSON>-Smirnov检验
ks.test(data, "pnorm", mean(data), sd(data))

# Anderson-Darling检验
nortest::ad.test(data)

# 图形化检验
qqnorm(data)
qqline(data)
```

### 方差齐性检验
```r
# Levene检验
car::leveneTest(value ~ group, data = df)

# Bartlett检验（假设正态分布）
bartlett.test(value ~ group, data = df)

# Fligner-Killeen检验（非参数）
fligner.test(value ~ group, data = df)
```

### 均值比较

#### 单样本t检验
```r
# 检验均值是否等于某个值
t.test(x, mu = 0, alternative = "two.sided")
```

#### 两样本t检验
```r
# 独立样本t检验
t.test(group1, group2, var.equal = TRUE)  # 等方差
t.test(group1, group2, var.equal = FALSE) # 不等方差（Welch检验）

# 配对样本t检验
t.test(before, after, paired = TRUE)
```

#### 方差分析（ANOVA）
```r
# 单因素方差分析
model <- aov(value ~ group, data = df)
summary(model)

# 多重比较
TukeyHSD(model)

# 双因素方差分析
model2 <- aov(value ~ factor1 * factor2, data = df)
summary(model2)
```

### 非参数检验

#### Mann-Whitney U检验（Wilcoxon秩和检验）
```r
wilcox.test(group1, group2, alternative = "two.sided")
```

#### Wilcoxon符号秩检验
```r
wilcox.test(before, after, paired = TRUE)
```

#### Kruskal-Wallis检验
```r
kruskal.test(value ~ group, data = df)
```

#### Friedman检验
```r
friedman.test(value ~ group | block, data = df)
```

## 相关性分析

### Pearson相关
```r
# 两变量相关
cor.test(x, y, method = "pearson")

# 相关矩阵
cor_matrix <- cor(data[, numeric_columns], use = "complete.obs")
corrplot::corrplot(cor_matrix, method = "color")
```

### Spearman秩相关
```r
cor.test(x, y, method = "spearman")
```

### Kendall's tau
```r
cor.test(x, y, method = "kendall")
```

### 偏相关
```r
# 使用ppcor包
ppcor::pcor.test(x, y, z)  # 控制变量z
```

## 回归分析

### 线性回归
```r
# 简单线性回归
model <- lm(y ~ x, data = df)
summary(model)

# 多元线性回归
model <- lm(y ~ x1 + x2 + x3, data = df)
summary(model)

# 模型诊断
plot(model)  # 诊断图
car::vif(model)  # 方差膨胀因子
```

### 逻辑回归
```r
# 二项逻辑回归
model <- glm(outcome ~ x1 + x2, family = binomial, data = df)
summary(model)

# 预测概率
predicted_prob <- predict(model, type = "response")

# 模型评估
library(pROC)
roc_curve <- roc(df$outcome, predicted_prob)
auc(roc_curve)
```

### 泊松回归
```r
# 计数数据回归
model <- glm(count ~ x1 + x2, family = poisson, data = df)
summary(model)
```

### 非线性回归
```r
# 多项式回归
model <- lm(y ~ poly(x, 2), data = df)

# 样条回归
library(splines)
model <- lm(y ~ bs(x, df = 3), data = df)

# 局部回归
model <- loess(y ~ x, data = df)
```

## 时间序列分析

### 基本组件
```r
# 时间序列对象
ts_data <- ts(data, start = c(2020, 1), frequency = 12)

# 分解
decomp <- decompose(ts_data)
plot(decomp)

# STL分解
stl_decomp <- stl(ts_data, s.window = "periodic")
```

### 平稳性检验
```r
# ADF检验
tseries::adf.test(ts_data)

# KPSS检验
tseries::kpss.test(ts_data)
```

### ARIMA模型
```r
# 自动ARIMA
library(forecast)
auto_model <- auto.arima(ts_data)

# 手动ARIMA
model <- arima(ts_data, order = c(1, 1, 1))

# 预测
forecast_result <- forecast(model, h = 12)
plot(forecast_result)
```

## 生存分析

### Kaplan-Meier估计
```r
library(survival)
surv_obj <- Surv(time, event)
km_fit <- survfit(surv_obj ~ group, data = df)
plot(km_fit)
```

### Cox比例风险模型
```r
cox_model <- coxph(Surv(time, event) ~ x1 + x2, data = df)
summary(cox_model)
```

## 多元统计分析

### 主成分分析（PCA）
```r
pca_result <- prcomp(data, scale. = TRUE)
summary(pca_result)
biplot(pca_result)
```

### 聚类分析
```r
# K-means聚类
kmeans_result <- kmeans(data, centers = 3)

# 层次聚类
hclust_result <- hclust(dist(data))
plot(hclust_result)
```

### 判别分析
```r
library(MASS)
lda_model <- lda(group ~ ., data = df)
predict(lda_model, newdata = test_data)
```

## 贝叶斯统计

### 贝叶斯t检验
```r
library(BayesFactor)
bf_result <- ttestBF(x = group1, y = group2)
```

### 贝叶斯回归
```r
library(rstanarm)
bayes_model <- stan_glm(y ~ x1 + x2, data = df)
summary(bayes_model)
```

## 效应量计算

### Cohen's d
```r
library(effsize)
cohen.d(group1, group2)
```

### 相关系数的置信区间
```r
cor.test(x, y)$conf.int
```

### R²和调整R²
```r
summary(lm_model)$r.squared
summary(lm_model)$adj.r.squared
```

## 功效分析

### 样本量计算
```r
library(pwr)

# t检验的样本量
pwr.t.test(d = 0.5, sig.level = 0.05, power = 0.8)

# ANOVA的样本量
pwr.anova.test(k = 3, f = 0.25, sig.level = 0.05, power = 0.8)

# 相关性的样本量
pwr.r.test(r = 0.3, sig.level = 0.05, power = 0.8)
```

## 模型选择和验证

### 信息准则
```r
AIC(model1, model2, model3)
BIC(model1, model2, model3)
```

### 交叉验证
```r
library(caret)
cv_results <- train(y ~ ., data = df, method = "lm", 
                   trControl = trainControl(method = "cv", number = 10))
```

### 自助法（Bootstrap）
```r
library(boot)
boot_function <- function(data, indices) {
  sample_data <- data[indices, ]
  # 计算统计量
  return(statistic)
}
boot_results <- boot(data, boot_function, R = 1000)
```
