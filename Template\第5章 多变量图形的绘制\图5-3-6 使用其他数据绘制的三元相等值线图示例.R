"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)
library(ggtern)


#构建数据
contour_data = read_excel("\\第5章 多变量图形的绘制\\ternary_countor.xlsx")


ggtern(data=contour_data, aes(x=Variable_1, y=Variable_2, z=Variable_3)) +    
    stat_density_tern(geom='polygon',
                      aes(fill=after_stat(level)),
                      base = "identity",
                      colour='grey50',linewidth=0.3) +
   geom_point(size=2)+
   labs(x="Var 1", y = "Var 2",z="Var 3") +
   scale_fill_gradientn(name="Level",colours = parula(100))+
   theme_dark() +
   theme_showarrows() +
   theme(legend.position = c(.95,.6),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",size = 16),
        axis.title = element_text(size = 18),
        axis.ticks = element_line(size = .5),
        axis.line = element_line(size = .4),
        axis.text = element_text(colour = "black",size = 14),
        #修改刻度长度
        tern.axis.ticks.length.major=unit(3.0,'mm'),
        tern.axis.ticks.length.minor=unit(1.5,'mm'))
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-6 使用其他数据绘制的三元相等值线图示例.png",
         width = 6.5, height = 5.2,dpi=600)
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-6 使用其他数据绘制的三元相等值线图示例.pdf",
        width = 6.5, height = 5.2,device = cairo_pdf) 
		