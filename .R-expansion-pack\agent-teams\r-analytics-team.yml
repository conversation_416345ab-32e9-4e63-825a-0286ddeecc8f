name: r-analytics-team
version: 1.0.0
description: R语言数据分析和统计建模专业团队配置

# 团队组织结构
team_structure:
  orchestrator: r-data-scientist
  specialists:
    - r-statistician
    - r-visualizer
    - r-package-developer

# 代理角色定义
agents:
  r-data-scientist:
    name: "Dr. <PERSON> Wei - R数据科学家"
    role: "主协调者"
    responsibilities:
      - 项目整体规划和协调
      - 数据分析策略制定
      - 团队成员任务分配
      - 质量控制和进度管理
      - 客户沟通和需求澄清
    expertise:
      - 统计建模和机器学习
      - 数据预处理和特征工程
      - R编程和包生态系统
      - 项目管理和团队协调
    activation_priority: 1
    
  r-statistician:
    name: "Dr. Sarah Chen - R统计分析师"
    role: "统计专家"
    responsibilities:
      - 统计方法选择和验证
      - 假设检验和推断分析
      - 实验设计和样本量计算
      - 模型假设检验和诊断
      - 统计结果解释和报告
    expertise:
      - 经典和贝叶斯统计推断
      - 实验设计和因果推断
      - 生存分析和纵向数据分析
      - 多元统计和时间序列分析
    activation_priority: 2
    
  r-visualizer:
    name: "Alex Rodriguez - R数据可视化专家"
    role: "可视化专家"
    responsibilities:
      - 数据可视化设计和实现
      - 交互式图表和仪表盘开发
      - 视觉设计和用户体验优化
      - 图表标准化和品牌一致性
      - 可视化最佳实践指导
    expertise:
      - ggplot2和plotly高级应用
      - Shiny应用开发
      - 数据故事叙述和设计原理
      - 色彩理论和可访问性设计
    activation_priority: 3
    
  r-package-developer:
    name: "Dr. Michael Zhang - R包开发专家"
    role: "包开发专家"
    responsibilities:
      - R包架构设计和开发
      - 代码重构和模块化
      - 文档编写和测试开发
      - CRAN提交和维护
      - 开发工作流优化
    expertise:
      - R包开发生命周期
      - devtools和roxygen2工作流
      - 单元测试和持续集成
      - 软件工程最佳实践
    activation_priority: 4

# 协作模式定义
collaboration_patterns:
  data_exploration:
    primary: r-data-scientist
    secondary: r-statistician
    support: r-visualizer
    workflow: "数据探索阶段的协作模式"
    
  statistical_modeling:
    primary: r-statistician
    secondary: r-data-scientist
    support: r-visualizer
    workflow: "统计建模阶段的协作模式"
    
  visualization_creation:
    primary: r-visualizer
    secondary: r-data-scientist
    support: r-statistician
    workflow: "可视化创建阶段的协作模式"
    
  package_development:
    primary: r-package-developer
    secondary: r-data-scientist
    support: [r-statistician, r-visualizer]
    workflow: "包开发阶段的协作模式"

# 工作流集成
workflows:
  primary_workflow: data-science-workflow
  supported_workflows:
    - exploratory-analysis
    - model-development
    - visualization-pipeline
    - package-creation

# 质量标准
quality_standards:
  code_quality:
    - 遵循tidyverse编码风格
    - 函数和变量命名规范
    - 适当的代码注释
    - 模块化和可重用设计
    
  analysis_quality:
    - 统计方法选择合理
    - 假设检验和模型诊断
    - 结果解释准确完整
    - 可重现性验证
    
  documentation_quality:
    - 完整的分析文档
    - 清晰的方法说明
    - 详细的结果解释
    - 用户友好的说明

# 沟通协议
communication_protocols:
  daily_sync:
    frequency: daily
    duration: 15_minutes
    participants: all_active_agents
    format: "简短状态更新和问题讨论"
    
  milestone_review:
    frequency: per_milestone
    duration: 60_minutes
    participants: all_team_members
    format: "正式评审和决策会议"
    
  technical_consultation:
    frequency: as_needed
    duration: 30_minutes
    participants: relevant_specialists
    format: "专业技术问题讨论"

# 工具和环境配置
tools_and_environment:
  required_tools:
    - R (>= 4.0.0)
    - RStudio
    - Git
    - pandoc
    
  recommended_packages:
    data_manipulation:
      - tidyverse
      - data.table
      - janitor
    statistical_analysis:
      - broom
      - modelr
      - infer
    visualization:
      - ggplot2
      - plotly
      - patchwork
    package_development:
      - devtools
      - usethis
      - testthat
    reporting:
      - rmarkdown
      - knitr
      - bookdown

# 项目模板
project_templates:
  exploratory_analysis:
    structure:
      - data/
      - scripts/
      - reports/
      - figures/
    key_files:
      - README.md
      - analysis.Rmd
      - data-dictionary.md
      
  statistical_modeling:
    structure:
      - data/
      - models/
      - scripts/
      - reports/
      - validation/
    key_files:
      - model-summary.Rmd
      - validation-report.Rmd
      
  package_project:
    structure:
      - R/
      - man/
      - tests/
      - vignettes/
      - data/
    key_files:
      - DESCRIPTION
      - NAMESPACE
      - README.md

# 成功指标
success_metrics:
  project_completion:
    - 按时交付率 >= 90%
    - 质量标准达成率 >= 95%
    - 客户满意度 >= 4.5/5
    
  team_performance:
    - 代码审查通过率 >= 95%
    - 文档完整性 >= 90%
    - 知识分享频率 >= 1次/周
    
  technical_excellence:
    - 代码可重现性 100%
    - 测试覆盖率 >= 80%
    - 性能基准达标率 >= 90%
