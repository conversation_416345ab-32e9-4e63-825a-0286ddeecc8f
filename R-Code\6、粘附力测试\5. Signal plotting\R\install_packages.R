# =============================================================================
# R包依赖安装脚本
#
# 功能：安装粘附力测试分析所需的R包
#
# 必需包：
# - ggplot2: 高质量图形绘制
# - dplyr: 数据处理
# - extrafont: Times New Roman字体支持（可选）
#
# 使用方法：
# source("install_packages.R")
# =============================================================================

# 定义所需的包
required_packages <- c(
  "ggplot2",      # 高质量图形绘制
  "dplyr",        # 数据处理
  "extrafont"     # 字体支持（可选）
)

# 检查和安装包的函数
install_if_missing <- function(package_name) {
  if (!require(package_name, character.only = TRUE, quietly = TRUE)) {
    tryCatch({
      install.packages(package_name, dependencies = TRUE)
      library(package_name, character.only = TRUE)
    }, error = function(e) {
      warning(paste("Failed to install", package_name, ":", e$message))
    })
  }
}

# 安装所有必要的包
for (pkg in required_packages) {
  install_if_missing(pkg)
}

# 验证安装
success_count <- 0
for (pkg in required_packages) {
  if (require(pkg, character.only = TRUE, quietly = TRUE)) {
    success_count <- success_count + 1
  }
}

if (success_count < length(required_packages)) {
  warning("部分包安装失败，请检查网络连接或手动安装")
}
