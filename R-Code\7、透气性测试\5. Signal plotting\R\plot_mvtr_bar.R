
# 加载必要的包
library(readr)

# 加载 plotBarChart 模块（假设在同一目录）
tryCatch({
  source("plotBarChart.R")
}, error = function(e) {
  cat("无法加载 plotBarChart.R，请确保文件在同一目录下\n")
  cat("错误信息:", e$message, "\n")
  stop("加载模块失败")
})

# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {

  # 读取数据
  data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
  materials <- data[[1]]
  values <- data[[ncol(data)]]

  # 调用 plotBarChart 函数
  p <- plotBarChart(materials = materials,
                    values = values)

  # 显示图表
  print(p)

  # 返回图表对象（可用于进一步修改或保存）
  return(p)
}

# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
csv_file <- "results.csv"
if (file.exists(csv_file)) {
  cat("找到CSV文件，开始绘制图表...\n")
  result <- plot_mvtr_bar_advanced(csv_file)
  cat("图表绘制完成！\n")
} else {
  cat("CSV文件不存在:", csv_file, "\n")
  cat("当前工作目录:", getwd(), "\n")
  cat("请确保 results.csv 文件在当前工作目录中\n")
}

# ========== 自定义参数说明 ==========
# 如需自定义样式，可以直接调用 plotBarChart 函数：
# plotBarChart(materials = c("Material A", "Material B"),
#              values = c(0.5, 1.2),
#              gradient_low = "#FFEBEE",     # 修改渐变起始色
#              gradient_high = "#D32F2F",    # 修改渐变结束色
#              y_limits = c(0, 2.0),         # 修改Y轴范围
#              value_format = "%.1f")        # 修改数值格式
