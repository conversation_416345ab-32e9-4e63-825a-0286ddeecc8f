# {{model_name}} 模型总结报告

[[LLM: 这是一个R语言统计/机器学习模型总结模板。执行时：
1. 根据模型类型（线性回归、逻辑回归、随机森林等）调整内容
2. 包含模型性能评估和解释
3. 提供可重现的R代码
4. 完成后执行tasks#execute-checklist model-checklist]]

## 模型概览

**模型类型**: {{model_type}}
**目标变量**: {{target_variable}}
**建模日期**: {{modeling_date}}
**数据集**: {{dataset_name}}
**样本量**: 训练集 {{train_size}}, 测试集 {{test_size}}

---

## 1. 建模目标

### 1.1 业务问题
{{business_problem}}

### 1.2 建模目标
{{modeling_objective}}

### 1.3 成功标准
{{success_criteria}}

---

## 2. 数据准备

### 2.1 特征工程
```r
# 特征工程代码
{{feature_engineering_code}}
```

**创建的特征**:
{{REPEAT_START}}
- {{feature_name}}: {{feature_description}}
{{REPEAT_END}}

### 2.2 数据分割
```r
# 数据分割
set.seed({{random_seed}})
train_index <- createDataPartition({{target_variable}}, p = {{train_ratio}}, list = FALSE)
train_data <- {{dataset_name}}[train_index, ]
test_data <- {{dataset_name}}[-train_index, ]
```

### 2.3 特征选择
**选择方法**: {{feature_selection_method}}

```r
# 特征选择代码
{{feature_selection_code}}
```

**最终特征** ({{final_feature_count}}个):
{{REPEAT_START}}
- {{selected_feature}}
{{REPEAT_END}}

---

## 3. 模型构建

### 3.1 模型配置
```r
# 模型参数设置
{{model_configuration}}
```

### 3.2 模型训练
```r
# 模型训练代码
{{model_training_code}}
```

### 3.3 超参数调优

^^IF_HYPERPARAMETER_TUNING^^
**调优方法**: {{tuning_method}}

```r
# 超参数调优
{{hyperparameter_tuning_code}}
```

**最优参数**:
{{REPEAT_START}}
- {{parameter_name}}: {{parameter_value}}
{{REPEAT_END}}
^^END_IF^^

---

## 4. 模型性能

### 4.1 训练集性能
{{REPEAT_START}}
- {{metric_name}}: {{train_metric_value}}
{{REPEAT_END}}

### 4.2 测试集性能
{{REPEAT_START}}
- {{metric_name}}: {{test_metric_value}}
{{REPEAT_END}}

### 4.3 交叉验证结果

^^IF_CROSS_VALIDATION^^
```r
# 交叉验证
{{cross_validation_code}}
```

**CV结果**: {{cv_metric_name}} = {{cv_mean}} ± {{cv_std}}
^^END_IF^^

### 4.4 性能可视化

#### 4.4.1 {{performance_chart_1_title}}
```r
{{performance_chart_1_code}}
```

#### 4.4.2 {{performance_chart_2_title}}
```r
{{performance_chart_2_code}}
```

---

## 5. 模型解释

### 5.1 特征重要性

^^IF_FEATURE_IMPORTANCE^^
```r
# 特征重要性
{{feature_importance_code}}
```

**Top 10 重要特征**:
{{REPEAT_START}}
{{rank}}. {{feature_name}}: {{importance_score}}
{{REPEAT_END}}
^^END_IF^^

### 5.2 模型系数/参数

^^IF_LINEAR_MODEL^^
**显著系数**:
{{REPEAT_START}}
- {{coefficient_name}}: {{coefficient_value}} (p = {{p_value}})
  - 解释: {{coefficient_interpretation}}
{{REPEAT_END}}
^^END_IF^^

### 5.3 部分依赖图

^^IF_PARTIAL_DEPENDENCE^^
```r
# 部分依赖图
{{partial_dependence_code}}
```
^^END_IF^^

### 5.4 SHAP值分析

^^IF_SHAP_ANALYSIS^^
```r
# SHAP值分析
{{shap_analysis_code}}
```
^^END_IF^^

---

## 6. 模型诊断

### 6.1 残差分析

^^IF_REGRESSION^^
```r
# 残差诊断图
{{residual_analysis_code}}
```

**诊断结果**:
- 线性性: {{linearity_check}}
- 独立性: {{independence_check}}
- 等方差性: {{homoscedasticity_check}}
- 正态性: {{normality_check}}
^^END_IF^^

### 6.2 异常值检测
```r
# 异常值检测
{{outlier_detection_code}}
```

**异常值数量**: {{outlier_count}}

### 6.3 多重共线性

^^IF_MULTICOLLINEARITY_CHECK^^
```r
# VIF检查
{{vif_check_code}}
```

**VIF > 5的变量**:
{{REPEAT_START}}
- {{variable_name}}: VIF = {{vif_value}}
{{REPEAT_END}}
^^END_IF^^

---

## 7. 模型比较

^^IF_MODEL_COMPARISON^^
### 7.1 候选模型
{{REPEAT_START}}
- {{model_name}}: {{model_description}}
{{REPEAT_END}}

### 7.2 性能比较
| 模型 | {{metric_1}} | {{metric_2}} | {{metric_3}} |
|------|-------------|-------------|-------------|
{{REPEAT_START}}
| {{model_name}} | {{metric_1_value}} | {{metric_2_value}} | {{metric_3_value}} |
{{REPEAT_END}}

### 7.3 模型选择理由
{{model_selection_rationale}}
^^END_IF^^

---

## 8. 预测示例

### 8.1 单个预测
```r
# 单个样本预测
new_data <- data.frame({{example_input}})
prediction <- predict({{model_object}}, new_data)
print(prediction)
```

### 8.2 批量预测
```r
# 批量预测
predictions <- predict({{model_object}}, test_data)
```

### 8.3 预测区间

^^IF_PREDICTION_INTERVAL^^
```r
# 预测区间
{{prediction_interval_code}}
```
^^END_IF^^

---

## 9. 模型部署

### 9.1 模型保存
```r
# 保存模型
saveRDS({{model_object}}, "{{model_filename}}.rds")
```

### 9.2 预测函数
```r
# 预测函数
predict_{{model_name}} <- function(new_data) {
  # 数据预处理
  {{preprocessing_steps}}
  
  # 预测
  predictions <- predict({{model_object}}, processed_data)
  
  return(predictions)
}
```

### 9.3 模型监控建议
- {{monitoring_suggestion_1}}
- {{monitoring_suggestion_2}}
- {{monitoring_suggestion_3}}

---

## 10. 结论与建议

### 10.1 模型总结
{{model_summary}}

### 10.2 关键发现
1. {{key_finding_1}}
2. {{key_finding_2}}
3. {{key_finding_3}}

### 10.3 业务影响
{{business_impact}}

### 10.4 局限性
- {{limitation_1}}
- {{limitation_2}}
- {{limitation_3}}

### 10.5 改进建议
- {{improvement_1}}
- {{improvement_2}}
- {{improvement_3}}

---

## 11. 技术附录

### 11.1 完整建模代码
```r
# 完整的可重现代码
{{complete_modeling_code}}
```

### 11.2 R环境
```r
sessionInfo()
```

### 11.3 使用的包
{{REPEAT_START}}
- {{package_name}} ({{package_version}})
{{REPEAT_END}}

---

**模型开发者**: {{developer_name}}
**审核者**: {{reviewer_name}}
**最后更新**: {{last_update_date}}

[[LLM: 完成模型总结后，提醒用户：
1. 验证所有性能指标的计算
2. 检查模型解释的合理性
3. 确保代码可重现
4. 考虑模型的实际应用场景
5. 评估是否需要进一步优化]]
