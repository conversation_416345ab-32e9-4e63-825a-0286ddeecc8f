
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(ggseg)

#######################################图7-12-4 （a）使用ggseg包绘制的大脑图谱结构示意图示例1

ggseg(mapping=aes(fill=region)) +
  scale_fill_brain("dk") +
  theme(text = element_text(family = "serif"),
        legend.position = "bottom")
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-12-4 使用ggseg包绘制的人体大脑图谱结构示意图_a.png",
       width =8.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-12-4 使用ggseg包绘制的人体大脑图谱结构示意图_a.pdf",
       width =8.5, height = 5,device = cairo_pdf)


#######################################图7-12-4 （b）使用ggseg包绘制的大脑图谱结构示意图示例2
plot(aseg)  +
  theme_void() +
  theme(text = element_text(family = "serif"),
        legend.position = "bottom")
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-12-4 使用ggseg包绘制的人体大脑图谱结构示意图_b.png",
       width =8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-12-4 使用ggseg包绘制的人体大脑图谱结构示意图_b.pdf",
       width =8, height = 4.5,device = cairo_pdf)
	   
	   