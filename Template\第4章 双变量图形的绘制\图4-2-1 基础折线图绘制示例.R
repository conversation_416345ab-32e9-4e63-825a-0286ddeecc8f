"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(reshape2)


#读取数据
file <- "\\第4章 双变量图形的绘制\\折线图数据.xlsx"
line_data <- read_excel(file)

#转换成长数据								
line_data_df <- line_data %>% tidyr::pivot_longer(cols = one:three,
                                                  cols_vary = "slowest",
                                                  names_to = "type", 
                                                  values_to = "values")
#改变绘图属性顺序
line_data_df$type <- factor(line_data_df$type, 
                            levels=c("one","two","three"), ordered=TRUE)												  

colors <- c("#2FBE8F","#459DFF","#FFCC37")												  
												  
												  
####################################图4-2-1（a）折线图绘制示例（ggplot2）
ggplot(data = line_data_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=2.5) +
  scale_color_manual(name="Type",values = colors) +
  theme_bw() +
  theme(legend.position = c(.15, .83),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 17),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-1 基础折线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-1 基础折线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-1（b）折线图绘制示例（ggprism）

ggplot(data = line_data_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=2.5) +
  ggprism::scale_color_prism("floral") +
  theme_prism(palette = "candy_bright",base_size = 16)+
  theme(legend.position = c(0.15, 0.85))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-1 基础折线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-1 基础折线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
   