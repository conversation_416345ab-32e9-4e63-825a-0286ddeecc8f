"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)


#读取数据
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\分组误差线图构建.xlsx"
error_line = read_excel(file)
											  
												  
												  
####################################图4-2-5（a）ggplot2 误差折线图绘制示例（ggprism）
#读取数据
ggplot(data = error_line,aes(x = time,y = mean,group=type)) +
  geom_line(colour="black",linewidth=1) +
  geom_errorbar(aes(ymin=mean-sd,ymax=mean+sd),linewidth=0.8)+
  geom_point(aes(fill=type),shape=21,size=6,stroke=1.5) +
  ggprism::scale_fill_prism(palette = "waves") +
  annotate("text",x=35,y=-6,label="(a)",size=8,fontface='bold') +
  scale_x_continuous(limits = c(-5,40),breaks = seq(-5,40,5),
                     expand = c(0,0),guide = "prism_minor") +
  scale_y_continuous(limits = c(-10,30),breaks = seq(-10,30,5),
                     expand = c(0,0),guide = "prism_minor") +
  labs(x="Day", y = "Values Change") +

  guides(fill=guide_legend(override.aes = list(size=4))) +
  theme_prism(border = TRUE,base_line_size = 1) +
  coord_cartesian(clip = "off") +
  ggprism::annotation_ticks(sides = "tr", type = "both", size = 1,
                   tick.length = unit(6, "pt"),
                   minor.length=unit(4, "pt"),
                   outside = TRUE)+
  theme(legend.position = c(0.15,0.85),
        legend.margin=margin(1,1,-5,1),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-5 带P值误差折线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-5 带P值误差折线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-5（b）ggplot2 带 P 值误差折线图绘制示例（ggprism）

ggplot(data = error_line,aes(x = time,y = mean,group=type)) +
  geom_line(colour="black",linewidth=1) +
  geom_errorbar(aes(ymin=mean-sd,ymax=mean+sd),linewidth=0.8)+
  geom_point(aes(fill=type),shape=21,size=6,stroke=1.5) +
  #绘制one three P值横线
  geom_segment(aes(x = 7,xend=7,y =21.5,yend=28)) +
  annotate("text",x=9,y=24.5,label="ns",size=6,angle=-90) +
   #绘制two four P值横线
  geom_segment(aes(x = 10.5,xend=10.5,y =18,yend=24.5)) +
  annotate("text",x=11,y=21.5,label="***",size=6,angle=-90) 
  ggprism::scale_fill_prism(palette = "waves") +
  annotate("text",x=35,y=-6,label="(a)",size=8,fontface='bold') +
  scale_x_continuous(limits = c(-5,40),breaks = seq(-5,40,5),
                     expand = c(0,0),guide = "prism_minor") +
  scale_y_continuous(limits = c(-10,30),breaks = seq(-10,30,5),
                     expand = c(0,0),guide = "prism_minor") +
  labs(x="Day", y = "Values Change") +
  guides(fill=guide_legend(override.aes = list(size=4))) +
  theme_prism(border = TRUE,base_line_size = 1) +
  coord_cartesian(clip = "off") +
  ggprism::annotation_ticks(sides = "tr", type = "both", size = 1,
                   tick.length = unit(6, "pt"),
                   minor.length=unit(4, "pt"),
                   outside = TRUE)+
  theme(legend.position = c(0.15,0.85),
        legend.margin=margin(1,1,-5,1),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-5 带P值误差折线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-5 带P值误差折线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
   