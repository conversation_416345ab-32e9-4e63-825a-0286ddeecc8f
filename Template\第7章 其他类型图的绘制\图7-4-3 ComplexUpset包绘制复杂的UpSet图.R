
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)
library(ComplexUpset)

set_size = function(w, h, factor=1.5) {
    s = 1 * factor
    options(
        repr.plot.width=w * s,
        repr.plot.height=h * s,
        repr.plot.res=100 / factor,
        jupyter.plot_mimetypes='image/png',
        jupyter.plot_scale=1
    )
}


movies = as.data.frame(ggplot2movies::movies)
genres = colnames(movies)[18:24]
movies[genres] = movies[genres] == 1
movies[movies$mpaa == '', 'mpaa'] = NA
movies = na.omit(movies)


####################################图7-4-3（a）ComplexUpset 包绘制复杂的UpSet图示例1
set_size(8, 3)
com_up01 <- upset(
    movies,
    genres,
    base_annotations=list(
        'Intersection size'=intersection_size(
            counts=FALSE,
            mapping=aes(fill=mpaa)
        ) + scale_fill_aaas()
    ),
    width_ratio=0.1
)
com_up01

ggsave(com_up01,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_a.png",
       width =12, height = 6, bg="white",dpi = 900,device=png)
ggsave(com_up01,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_a.pdf",
       width =12, height = 6,device = cairo_pdf)

	   
####################################图7-4-3（b）ComplexUpset 包绘制复杂的UpSet图示例2  
set_size(5, 3)

com_up02 <- upset(
    movies, genres,
    min_size=10,
    width_ratio=0.3,
    encode_sets=FALSE,  # for annotate() to select the set by name disable encoding
    set_sizes=(
        upset_set_size()
        + geom_text(aes(label=..count..), hjust=1.1, stat='count')
        # 给每个柱形图添加文本信息
        + annotate(geom='text', label='@', x='Drama', y=850, color='white', size=3)
        + expand_limits(y=1100)
        + theme(axis.text.x=element_text(angle=90))
    )
)


ggsave(com_up02,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_b.png",
       width =12, height = 6, bg="white",dpi = 900,device=png)
ggsave(com_up02,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_b.pdf",
       width =12, height = 6,device = cairo_pdf)

####################################图7-4-3（c）ComplexUpset 包绘制复杂的UpSet图示例3  
set_size(6, 4)
com_up03 <- upset(
    movies,
    genres,
    min_size=10,
    width_ratio=0.2,
    stripes=upset_stripes(
        geom=geom_segment(linewidth=5),
        colors=c('cornsilk1', 'deepskyblue1', 'grey90')
    )
)

ggsave(com_up03,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_c.png",
       width =12, height = 6, bg="white",dpi = 900,device=png)
ggsave(com_up03,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_c.pdf",
       width =12, height = 6,device = cairo_pdf)

####################################图7-4-3（d）ComplexUpset 包绘制复杂的UpSet图示例4  
set_size(8, 4)
com_up04 <- upset(
    movies, genres, name='genre', min_size=10,
    encode_sets=FALSE,  # for annotate() to select the set by name disable encoding
    matrix=(
        intersection_matrix(
            geom=geom_point(
                shape='square',
                size=3.5
            ),
            segment=geom_segment(
                linetype='dotted'
            ),
            outline_color=list(
                active='darkorange3',
                inactive='grey70'
            )
        )
        + scale_color_manual(
            values=c('TRUE'='orange', 'FALSE'='grey'),
            labels=c('TRUE'='yes', 'FALSE'='no'),
            breaks=c('TRUE', 'FALSE'),
            name='Is intersection member?'
        )
        + scale_y_discrete(
            position='right'
        )
        + annotate(
            geom='text',
            label='Look here →',
            x='Comedy-Drama',
            y='Drama',
            size=5,
            hjust=1
        )
    ),
    queries=list(
        upset_query(
            intersect=c('Drama', 'Comedy'),
            color='red',
            fill='red',
            only_components=c('intersections_matrix', 'Intersection size')
        )
    )
)

ggsave(com_up04,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_d.png",
       width =12, height = 6, bg="white",dpi = 900,device=png)
ggsave(com_up04,filename = "\\第7章 其他类型图的绘制\\图7-4-3 ComplexUpset包绘制复杂的UpSet图_d.pdf",
       width =12, height = 6,device = cairo_pdf)