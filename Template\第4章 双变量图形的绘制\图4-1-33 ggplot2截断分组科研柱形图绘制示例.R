"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据集

boken_bar <- read_excel("\\第4章 双变量图形的绘制\\broken axis bar long.xlsx")

									

####################################图4-1-33（a）ggplot2 未截断刻度误差柱形图（SE）
ggplot(data = boken_bar,aes(x=as.factor(group),y=value)) +
  #误差线
  stat_summary(aes(group=type),fun.data = 'mean_se', geom = "errorbar", 
               width = 0.3,linewidth=0.25,
               position=position_dodge(0.6)) +
  #柱状图
  geom_bar(aes(fill=type),color="black",stat="summary",
           fun=mean,linewidth=0.5,width=0.7,position="dodge") +
  ggbeeswarm::geom_beeswarm(aes(group=type), dodge.width = 0.9) +
  labs(x="Group",y="Mean value") +
  #颜色
  ggsci::scale_fill_npg() +
  scale_y_continuous(expand = c(0, 0),limits = c(0,500)) +
  theme_classic()+
  theme(legend.position = c(0.15,0.9),
        legend.title = element_blank(),
        #legend.margin = margin(20, 6, 0, 0),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-33 ggplot2截断分组科研柱形图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-33 ggplot2截断分组科研柱形图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-33（b）ggplot2 截断刻度误差柱形图（SE）

library(ggbreak)

ggplot(data = boken_bar,aes(x=as.factor(group),y=value,group=type)) +
  #误差线
  stat_summary(fun.data = 'mean_se', geom = "errorbar", 
               width = 0.3,linewidth=0.25,
               position=position_dodge(0.6)) +
  #柱状图
  geom_bar(aes(fill=type),color="black",stat="summary",
           fun=mean,linewidth=0.5,width=0.7,position="dodge") +
  ggbeeswarm::geom_beeswarm(dodge.width = 0.9) +
  labs(x="Group",y="Mean value") +
  ggsignif::geom_signif(y_position = 8,xmin =0.75,xmax =1.25,
                       annotation = c("P=0.003"), 
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 380,xmin =1.25,xmax =2.25,
                       annotation = c("P=0.013"), 
                       tip_length = c(0.5, 0.1),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  ggsignif::geom_signif(y_position = 520,xmin =1.25,xmax =3.25,
                       annotation = c("P<0.001"), 
                       tip_length = c(0.2, 0),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
  #颜色
  ggsci::scale_fill_npg() +
  scale_y_continuous(expand = c(0, 0),limits = c(0,600)) +
  #y轴截断
  ggbreak::scale_y_break(c(10,100),
                scales=1.2, expand = FALSE,
                space=0.25) +
  theme_classic()+
  theme(legend.position = "top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(0.2, "cm"),
        axis.ticks.y.right = element_blank(),
        axis.text.y.right=element_blank(),
        axis.line.y.right=element_blank(),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-33 ggplot2截断分组科研柱形图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-33 ggplot2截断分组科研柱形图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

   