"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
heatmap_data <- read_excel("\\第4章 双变量图形的绘制\\相关性热力图_P值.xlsx")

corr <- round(cor(heatmap_data), 2)

#使用 reshape2包的melt()函数将其转换成长数据	
library(reshape2)
melted_comatx <- reshape2::melt(corr)

##Get lower triangle of the correlation matrix
get_lower_tri<-function(cormat){
    cormat[upper.tri(cormat)] <- NA
    return(cormat)
  }
  # Get upper triangle of the correlation matrix
get_upper_tri <- function(cormat){
    cormat[lower.tri(cormat)]<- NA
    return(cormat)
  }
  
lower_tri <- get_lower_tri(corr)
upper_tri <- get_upper_tri(corr)  

upper_tri_melt <- reshape2::melt(upper_tri)
lower_tri_melt <- reshape2::melt(lower_tri) 
				  
####################################图4-2-21（a）使用ggplot2绘制的相关性矩阵热力图示例1

ggplot(data = melted_comatx,aes(x = Var1, y=Var2, fill=value)) +
  geom_tile(colour="black",linewidth=.2) +
  geom_text(aes(label=value),size=3,family = "serif",fontface="bold") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100))+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_a.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_a.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-21（b）使用ggplot2绘制的相关性矩阵热力图示例2（下三角）

ggplot(data = lower_tri_melt,aes(x = Var1, y=Var2, fill=value)) +
  geom_tile(colour="black",size=.2) +
  geom_text(aes(label=value),size=3,family = "serif",fontface="bold") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_b.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_b.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-21（c）使用ggplot2绘制的相关性矩阵热力图示例3（上三角）
ggplot(data = upper_tri_melt,aes(x = Var1, y=Var2, fill=value)) +
  geom_tile(colour="black",size=.2) +
  geom_text(aes(label=value),size=3,family = "serif",fontface="bold") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_c.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_c.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

####################################图4-2-21（d）使用ggplot2绘制的相关性矩阵热力图示例4（相关性数值和颜色块组合）
ggplot(data = lower_tri_melt,aes(x = Var1, y=Var2, fill=value)) +
  geom_tile(colour="black",size=.2) +
  geom_text(data = upper_tri_melt,aes(x = Var1, y=Var2,label=value),
            size=3,family = "serif",fontface="bold") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_d.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_d.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)	

####################################图4-2-21（d）-2 使用ggplot2绘制的相关性矩阵热力图示例4（相关性数值和颜色块组合）

ggplot(data = lower_tri_melt,aes(x = Var1, y=Var2, fill=value)) +
  geom_tile(colour="black",size=.2) +
  geom_text(data = melted_comatx,aes(x = Var1, y=Var2,label=value),
            size=3,family = "serif",fontface="bold") +
  labs(x="", y = "") +
  scale_fill_gradientn(name="Cor",limit = c(-1,1),colours = parula(100),
                      na.value="NA")+
  theme_minimal() +
  theme(plot.background = element_rect(fill = "white",colour="white"),
        legend.text=element_text(family = "serif",face='bold',size = 10,),
        legend.title=element_text(family = "serif",face='bold',size = 11),
        legend.key.width = unit(.6, "cm"),
        legend.key.height = unit(.85,"cm"),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.15, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_d-2.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-21 使用ggplot2绘制的相关性矩阵热力图示例_d-2.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)	   
