"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(circlize)


data <- c(0, 5, 6, 4, 7, 4,
          5, 0, 5, 4, 6, 5,
          6, 5, 0, 4, 5, 5,
          4, 4, 4, 0, 5, 5,
          7, 6, 5, 5, 0, 4,
          4, 5, 5, 5, 4, 0)
mat = matrix(data, 6, 6)
rownames(mat) = c("A","B","C","D","E","F")
colnames(mat) = c("A","B","C","D","E","F")		  

####################################图5-8-1（a）和弦图绘制基本样式（默认颜色）

png(file="\\第5章 多变量图形的绘制\\图5-8-1 circlize 包和弦图绘制示例_a.png",
    width = 5500, height = 5000,res=1000)
	
pdf(file="\\第5章 多变量图形的绘制\\图5-8-1 circlize 包和弦图绘制示例_a.pdf",
    width = 5.5, height = 5)	
	
par(cex = 1.5, mar = rep(1,4))
circlize::chordDiagramFromMatrix(mat)
dev.off()
	   
####################################图5-8-1（b）和弦图绘制基本样式（修改颜色）

png(file="\\第5章 多变量图形的绘制\\图5-8-1 circlize 包和弦图绘制示例_b.png",
    width = 5500, height = 5000,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-8-1 circlize 包和弦图绘制示例_b.pdf",
    width = 5.5, height = 5)	
	
par(cex = 1.5, mar = rep(1,4))
circlize::chordDiagramFromMatrix(mat,grid.col = parula(6))
dev.off()