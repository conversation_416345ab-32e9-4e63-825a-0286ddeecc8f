# {{project_name}} 数据分析报告

[[LLM: 这是一个R语言数据分析报告模板。在执行此模板时：
1. 替换所有{{placeholders}}为实际内容
2. 根据分析类型调整章节结构
3. 确保包含代码、结果和解释
4. 生成可重现的R Markdown格式
5. 在每个主要章节完成后，执行tasks#execute-checklist analysis-checklist]]

## 执行摘要

[[LLM: 简要总结分析目的、主要发现和建议，控制在200字以内]]

**分析目的**: {{analysis_objective}}

**主要发现**:
- {{key_finding_1}}
- {{key_finding_2}}
- {{key_finding_3}}

**建议**:
- {{recommendation_1}}
- {{recommendation_2}}

---

## 1. 项目背景

### 1.1 研究问题
{{research_question}}

### 1.2 数据来源
- **数据集**: {{dataset_name}}
- **数据期间**: {{data_period}}
- **样本量**: {{sample_size}}
- **数据来源**: {{data_source}}

### 1.3 分析目标
{{analysis_goals}}

---

## 2. 数据概览

### 2.1 数据结构
```r
# 数据维度
dim({{dataset_name}})

# 数据结构
str({{dataset_name}})

# 基本统计信息
summary({{dataset_name}})
```

### 2.2 变量说明
| 变量名 | 类型 | 描述 | 取值范围 |
|--------|------|------|----------|
{{REPEAT_START}}
| {{variable_name}} | {{variable_type}} | {{variable_description}} | {{variable_range}} |
{{REPEAT_END}}

### 2.3 数据质量评估
- **缺失值**: {{missing_values_summary}}
- **异常值**: {{outliers_summary}}
- **数据完整性**: {{data_completeness}}

---

## 3. 探索性数据分析

### 3.1 描述性统计

[[LLM: 为每个主要变量生成描述性统计表格和解释]]

#### 3.1.1 连续变量
```r
# 连续变量描述性统计
{{dataset_name}} %>%
  select_if(is.numeric) %>%
  summary()
```

#### 3.1.2 分类变量
```r
# 分类变量频数统计
{{dataset_name}} %>%
  select_if(is.factor) %>%
  map(table)
```

### 3.2 数据分布

[[LLM: 生成主要变量的分布图和解释]]

#### 3.2.1 {{target_variable}}分布
```r
# 目标变量分布图
ggplot({{dataset_name}}, aes(x = {{target_variable}})) +
  geom_histogram(bins = 30, fill = "skyblue", alpha = 0.7) +
  labs(title = "{{target_variable}}分布图",
       x = "{{target_variable}}",
       y = "频数") +
  theme_minimal()
```

**分布特征**: {{distribution_characteristics}}

### 3.3 变量关系

#### 3.3.1 相关性分析
```r
# 相关性矩阵
cor_matrix <- cor({{dataset_name}}[numeric_columns], use = "complete.obs")
corrplot::corrplot(cor_matrix, method = "color", type = "upper")
```

**主要发现**: {{correlation_findings}}

#### 3.3.2 分组比较
```r
# 按组比较目标变量
ggplot({{dataset_name}}, aes(x = {{group_variable}}, y = {{target_variable}})) +
  geom_boxplot() +
  geom_jitter(width = 0.2, alpha = 0.5) +
  labs(title = "{{target_variable}}按{{group_variable}}分组比较",
       x = "{{group_variable}}",
       y = "{{target_variable}}") +
  theme_minimal()
```

---

## 4. 统计分析

[[LLM: 根据研究问题选择合适的统计方法]]

### 4.1 假设检验

#### 4.1.1 {{hypothesis_test_name}}
**原假设**: {{null_hypothesis}}
**备择假设**: {{alternative_hypothesis}}

```r
# 执行假设检验
test_result <- {{test_function}}({{test_parameters}})
print(test_result)
```

**结果解释**: {{test_interpretation}}

### 4.2 回归分析

^^IF_REGRESSION^^
#### 4.2.1 模型构建
```r
# 构建回归模型
model <- {{model_type}}({{model_formula}}, data = {{dataset_name}})
summary(model)
```

#### 4.2.2 模型诊断
```r
# 模型诊断图
par(mfrow = c(2, 2))
plot(model)
```

#### 4.2.3 模型解释
**模型拟合度**: R² = {{r_squared}}, 调整R² = {{adjusted_r_squared}}

**显著变量**:
{{REPEAT_START}}
- {{variable_name}}: 系数 = {{coefficient}}, p值 = {{p_value}}, 解释: {{interpretation}}
{{REPEAT_END}}
^^END_IF^^

---

## 5. 高级分析

^^IF_ADVANCED_ANALYSIS^^
### 5.1 {{advanced_method_name}}

[[LLM: 根据需要包含机器学习、时间序列、生存分析等高级方法]]

```r
# 高级分析代码
{{advanced_analysis_code}}
```

**结果**: {{advanced_results}}
^^END_IF^^

---

## 6. 结果可视化

### 6.1 主要发现图表

[[LLM: 创建3-5个关键图表展示主要发现]]

#### 6.1.1 {{chart_title_1}}
```r
{{chart_code_1}}
```

#### 6.1.2 {{chart_title_2}}
```r
{{chart_code_2}}
```

### 6.2 交互式图表

^^IF_INTERACTIVE^^
```r
# 交互式图表
library(plotly)
interactive_plot <- ggplotly({{base_plot}})
interactive_plot
```
^^END_IF^^

---

## 7. 结论与建议

### 7.1 主要发现总结
1. {{finding_1_detailed}}
2. {{finding_2_detailed}}
3. {{finding_3_detailed}}

### 7.2 统计显著性
- {{significance_summary}}

### 7.3 实际意义
{{practical_implications}}

### 7.4 局限性
- {{limitation_1}}
- {{limitation_2}}
- {{limitation_3}}

### 7.5 后续研究建议
- {{future_research_1}}
- {{future_research_2}}

---

## 8. 技术附录

### 8.1 R环境信息
```r
sessionInfo()
```

### 8.2 使用的R包
```r
# 主要R包版本
packageVersion("tidyverse")
packageVersion("ggplot2")
{{REPEAT_START}}
packageVersion("{{package_name}}")
{{REPEAT_END}}
```

### 8.3 数据预处理代码
```r
# 完整的数据预处理流程
{{data_preprocessing_code}}
```

### 8.4 完整分析代码
```r
# 可重现的完整分析代码
{{complete_analysis_code}}
```

---

## 参考文献
{{REPEAT_START}}
{{reference_number}}. {{reference_citation}}
{{REPEAT_END}}

---

**报告生成时间**: {{generation_date}}
**分析师**: {{analyst_name}}
**审核**: {{reviewer_name}}

[[LLM: 报告完成后，提醒用户：
1. 检查所有代码是否可运行
2. 验证统计结果的正确性
3. 确保图表清晰易懂
4. 检查结论是否有数据支撑
5. 考虑生成R Markdown版本以确保可重现性]]
