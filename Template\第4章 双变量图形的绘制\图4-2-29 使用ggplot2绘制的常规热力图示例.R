"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)


#读取数据
heat_data_01 <- read_excel(r"(4.xlsx)")

# 将数据转换为长格式
heat_data_01_long <- heat_data_01 %>%
  pivot_longer(cols = -Index,  # 除了Index列之外的所有列都转换
               names_to = "name",  # 新的分类变量列名
               values_to = "value")  # 新的数值列名



color_set <- colorRampPalette(c("#f4faf0", "#09a4df"))(50)




# 定义控制变量
legend_bar_width <- 1.5      # 图例条形宽度（cm）
legend_bar_height <- 40      # 图例条形高度（cm）
legend_text_size <- 14       # 图例数值文字大小
legend_title_size <- 16      # 图例标题文字大小
legend_title_spacing_v <- 0.1  # 标题与颜色条的纵向距离（cm）
legend_title_spacing_h <- 1  # 标题与颜色条的横向距离（cm）

ggplot(data = heat_data_01_long,aes(x = Index, y=name, fill=value)) +
  geom_tile(colour="black",linewidth=0.2) +
  scale_fill_gradientn(colours = color_set,
                       name = expression(Delta*"C/C"[0]),  # 使用expression添加下标
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour=NA,
                                              barwidth = 1.5,
                                              barheight = legend_bar_height/2,
                                              title.position = "right",
                                              title.vjust = 1 + legend_title_spacing_v,  # 控制纵向距离
                                              title.hjust = 0.5 - legend_title_spacing_h)) +  # 控制横向距离
  labs(x="",y="")+
  theme_void() +
  theme(
    axis.text.x = element_blank(),
    axis.text.y = element_blank(),
    legend.key.width = unit(legend_bar_width, "cm"),
    legend.key.height = unit(legend_bar_height, "cm"),
    text = element_text(family = "Times New Roman"),
    legend.text = element_text(size = legend_text_size, family = "Times New Roman"),
    legend.title = element_text(size = legend_title_size, family = "Times New Roman"),
    plot.margin = margin(5, 5, 5, 5))








# ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-29 使用ggplot2绘制的常规热力图示例.png",
#        width =7.5, height = 1.5, bg="white",dpi = 900,device=png)
# ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-29 使用ggplot2绘制的常规热力图示例.pdf",
#       width =7.5, height = 1.5,device = cairo_pdf)

