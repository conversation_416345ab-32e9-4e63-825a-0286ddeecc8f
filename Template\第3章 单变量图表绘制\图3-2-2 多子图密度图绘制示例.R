"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#读取数据

#读取数据

dens_data <- readr::read_csv("\\第3章 单变量图表绘制\\density_data.csv")
den_long <- pivot_longer(dens_data,cols = starts_with("d"),names_to = "type",values_to = "values")



####################################图3-2-2（a）ggplot2 多子图密度图绘制示例1
ggplot(data = desi_data_long,aes(x = values)) +
  geom_density(fill="#DB3132",alpha=0.6,linewidth=0.3) +
  geom_rug(length = unit(0.07, "npc")) +
  xlim(c(0,25)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,0.15),
                     breaks = seq(0,0.15,0.05)) +
  facet_wrap(~type) +
  theme_bw() +
#   hrbrthemes::theme_ipsum_pub(base_family = "serif",
#                               base_size = 12,
#                               plot_margin = margin(10, 10, 10, 10))+
  theme(strip.background = element_blank(),
        strip.text = element_text(size = 14),
        text = element_text(family = "serif",face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-2 多子图密度图绘制示例_a.png",
       width = 8, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-2 多子图密度图绘制示例_a.pdf",
       width =8, height = 5,device = cairo_pdf)


####################################图3-2-2（b）ggplot2 多子图密度图绘制示例2（渐变色填充)
data_01_densi <- density(desi_data$data_01, n = 1000)
data_02_densi <- density(desi_data$data_02, n = 1000)
data_03_densi <- density(desi_data$data_03, n = 1000)
data_04_densi <- density(desi_data$data_04, n = 1000)

desi_data_01 <- data.frame(data=rep("data_01",1000),
                           densi_x=data_01_densi$x,
                           densi_y=data_01_densi$y)
desi_data_02 <- data.frame(data=rep("data_02",1000),
                           densi_x=data_02_densi$x,
                           densi_y=data_02_densi$y)
desi_data_03 <- data.frame(data=rep("data_03",1000),
                           densi_x=data_03_densi$x,
                           densi_y=data_03_densi$y)
desi_data_04 <- data.frame(data=rep("data_04",1000),
                           densi_x=data_04_densi$x,
                           densi_y=data_04_densi$y)
desi_data_pro <- rbind(desi_data_01,desi_data_02,
                       desi_data_03,desi_data_04)


ggplot(data = desi_data_pro,aes(x = densi_x,y = densi_y)) +
  geom_segment(aes(x = densi_x,xend=densi_x,y = densi_y,yend=0,
                   color=densi_x))+
  geom_line(linewidth=0.5) +
  scale_color_gradientn(name="values",colours = parula(100)) +
  xlim(c(0,25)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,0.15),
                     breaks = seq(0,0.15,0.05)) +
  labs(x="Values",y="Density") +
  facet_wrap(~data) +
  theme_bw() +
  theme(strip.background = element_blank(),
        strip.text = element_text(size = 14),
        text = element_text(family = "serif",face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-2 多子图密度图绘制示例_b.png",
       width = 8.3, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-2 多子图密度图绘制示例_b.pdf",
       width =8.3, height = 5,device = cairo_pdf)
