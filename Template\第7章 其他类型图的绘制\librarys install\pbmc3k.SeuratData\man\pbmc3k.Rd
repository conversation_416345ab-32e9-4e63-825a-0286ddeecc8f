% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/data.R
\docType{data}
\name{pbmc3k}
\alias{pbmc3k}
\title{PBMC 3k}
\format{
A \code{Seurat} object with the PBMC 3k dataset
}
\source{
\url{https://support.10xgenomics.com/single-cell-gene-expression/datasets/1.1.0/pbmc3k}
}
\usage{
pbmc3k
}
\description{
2,700 peripheral blood mononuclear cells (PBMC) from 10X genomics
}
\examples{
\dontrun{
if (requireNamespace("Seurat", quietly = TRUE)) {
  url <- 'http://cf.10xgenomics.com/samples/cell-exp/1.1.0/pbmc3k/pbmc3k_filtered_gene_bc_matrices.tar.gz'
  curl::curl_download(url = url, destfile = basename(path = url))
  untar(tarfile = basename(path = url))
  pbmc.data <- Seurat::Read10X(data.dir = 'filtered_gene_bc_matrices/hg19/')
  pbmc3k <- Seurat::CreateSeuratObject(counts = pbmc.data, project = 'pbmc3k', min.cells = 3, min.features = 200)
  # Annotations come from Seurat's PBMC3k Guided Clustering Tutorial
  # https://satijalab.org/seurat/v3.0/pbmc3k_tutorial.html
  annotations <- readRDS(file = system.file('extdata/annotations/annotations.Rds', package = 'pbmc3k.SeuratData'))
  pbmc3k <- Seurat::AddMetaData(object = pbmc3k, metadata = annotations)
  # Clean up downloaded files
  file.remove(basename(path = url))
  unlink(x = 'filtered_gene_bc_matrices/', recursive = TRUE)
}
}

}
\seealso{
\code{\link{pbmc3k.final}}
}
\keyword{datasets}
