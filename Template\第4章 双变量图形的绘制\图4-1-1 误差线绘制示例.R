"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
error_data = tibble(x = c(2, 4, 6),
                        y =c(4, 6, 3),
                        xerr =c(0.5,0.8,1.1),
                        yerr =c(0.8, 1.2, 0.6))

										

####################################图4-1-1（a）基本的误差线样式
ggplot(data = error_data,aes(x = x,y=y)) +
  geom_errorbar(aes(ymin = y-yerr, ymax = y+yerr),width = 0.5,linewidth=1) +
  geom_errorbarh(aes(xmin = x-xerr,xmax = x+xerr),height=0.5,linewidth=1) +
  geom_point(size=8,shape=21,fill="white",stroke=1) +
  labs(x="Class",y="Values") +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4),)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-1 误差线绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-1 误差线绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-1（b）在柱形图上添加纵向误差线样式
ggplot(data = error_data) +
  geom_errorbar(aes(x = x,y=y,ymin = y-yerr, ymax = y+yerr),width = 0.5,linewidth=1) +
  geom_bar(aes(x=x,y = y),fill="gray50",color="black",linewidth=0.8,
           width = 1.3,stat="identity") +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 8)) +
  labs(x="Class",y="Values") +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-1 误差线绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-1 误差线绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
