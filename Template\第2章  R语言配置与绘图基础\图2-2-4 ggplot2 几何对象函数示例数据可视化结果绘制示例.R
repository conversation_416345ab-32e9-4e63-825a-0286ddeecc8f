"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-4（a）ggplot2 几何对象函数示例1

ggplot(data = tips,aes(x = total_bill,y = tip))+
  geom_point(shape=21,size=5,fill="gray",alpha=.6) +
  labs(x="X Label", y = "Y Label")+
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.3,.9),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_blank(),#去除图例背景
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.32,"cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-4 ggplot2 几何对象函数示例数据可视化结果绘制示例_a.png",
       width = 4.3, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-4 ggplot2 几何对象函数示例数据可视化结果绘制示例_a.pdf",
       width = 4.3, height = 4,device = cairo_pdf)

###############################################图2-2-4（b）ggplot2 几何对象函数示例2

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip,size=tip))+
  geom_point(shape=21,alpha=.6) +
  labs(x="X Label", y = "Y Label")+
  scale_size_continuous(name="Values 01",guide = guide_legend(order = 1,
                                            label.position = "top"))+
  scale_fill_gradientn(name="Values 02",colours = parula(100),
                       guide=guide_colorbar(label.position = "top",
                                            order = 2))+
  #theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.35,.85),
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5),
        legend.margin = margin(.1, .1, .1, .1),
        legend.spacing.y = unit(.25, "cm"),
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.35,"cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-4 ggplot2 几何对象函数示例数据可视化结果绘制示例_b.png",
       width = 4.3, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-4 ggplot2 几何对象函数示例数据可视化结果绘制示例_b.pdf",
       width = 4.3, height = 4,device = cairo_pdf)


