# 数据集分析任务

这个任务指导你完成全面的数据集分析，包括数据探索、清洗、描述性统计和初步建模。

## 前提条件

- 数据文件已准备就绪（CSV、Excel、RDS等格式）
- R环境已配置必要的包（tidyverse、skimr、DataExplorer等）
- 分析目标和研究问题已明确

## 步骤

### 1. 数据导入和初步检查

收集以下信息：

- **数据源**: 数据来自哪里？
- **数据格式**: CSV、Excel、数据库等
- **数据大小**: 行数和列数
- **分析目标**: 要回答什么问题？
- **目标变量**: 如果是预测任务，目标变量是什么？

### 2. 数据质量评估

执行以下检查：

```r
# 基本信息
str(data)
summary(data)
skimr::skim(data)

# 缺失值分析
VIM::aggr(data)
naniar::vis_miss(data)

# 重复值检查
sum(duplicated(data))
```

### 3. 探索性数据分析 (EDA)

#### 3.1 单变量分析
- 数值变量：分布、异常值、统计摘要
- 分类变量：频次、比例、唯一值

#### 3.2 双变量分析
- 相关性分析
- 分组统计
- 交叉表分析

#### 3.3 多变量分析
- 主成分分析 (PCA)
- 聚类分析
- 变量重要性

### 4. 数据清洗和预处理

根据EDA结果执行：

- **缺失值处理**: 删除、填充或插值
- **异常值处理**: 识别和处理极端值
- **数据转换**: 标准化、归一化、对数变换
- **特征工程**: 创建新变量、编码分类变量
- **数据分割**: 训练集、验证集、测试集

### 5. 描述性统计报告

生成包含以下内容的报告：

- 数据概览和质量评估
- 关键变量的分布特征
- 变量间的关系和相关性
- 数据清洗和预处理步骤
- 主要发现和洞察

### 6. 初步建模（可选）

如果适用，进行初步建模：

- 选择合适的建模方法
- 基线模型构建
- 模型性能评估
- 特征重要性分析

### 7. 结果验证

使用检查清单验证分析质量：

```bash
*execute-checklist analysis-checklist
```

### 8. 文档生成

创建分析报告：

```bash
*create-doc analysis-report-tmpl
```

## 输出文件

分析完成后应生成：

- **数据分析脚本** (.R 或 .Rmd)
- **清洗后的数据集** (.rds 或 .csv)
- **分析报告** (.html 或 .pdf)
- **图表和可视化** (.png 或嵌入报告)

## 质量标准

- 所有代码都有适当的注释
- 分析步骤逻辑清晰
- 结果可重现
- 图表清晰且有意义
- 报告结构完整

## 常见数据类型处理

### 时间序列数据
- 时间格式转换
- 趋势和季节性分析
- 缺失时间点处理

### 文本数据
- 文本清洗和预处理
- 词频分析
- 情感分析

### 地理数据
- 坐标系统检查
- 空间分布分析
- 地图可视化

## 工具推荐

- **数据导入**: readr, readxl, haven
- **数据操作**: dplyr, tidyr
- **可视化**: ggplot2, plotly
- **统计分析**: stats, broom
- **报告生成**: rmarkdown, knitr

## 注意事项

- 始终保留原始数据的备份
- 记录所有数据处理步骤
- 注意数据隐私和安全
- 验证分析结果的合理性
- 考虑分析的局限性和假设
