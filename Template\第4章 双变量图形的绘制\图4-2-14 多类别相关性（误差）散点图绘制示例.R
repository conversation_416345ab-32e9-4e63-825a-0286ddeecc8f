"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
class_data <- read_excel("\\第4章 双变量图形的绘制\\分类相关性散点图.xlsx")


											 										  
####################################图4-2-14（a）多类别相关性（误差）散点图绘制示例

ggplot(data = class_data,aes(x = Variable_01,y = Variable_02,
                             color = type)) +
  geom_errorbar(aes(ymin = Variable_02-y_error, ymax = Variable_02+y_error),
               colour="gray40",linewidth=.2) +
  geom_errorbarh(aes(xmin = Variable_01-x_error, xmax = Variable_01+x_error),
               colour="gray40",linewidth=.2) +
  geom_point(aes(fill=type),color = "black",shape=21,size=3.5) +
  stat_smooth(aes(fill = type, color = type), method = "lm",
              formula = y ~ x) +
  stat_regline_equation(
    aes(label =  paste(after_stat(eq.label), ..adj.rr.label..,sep = "~~~~")),
        formula = y ~ x,size=4.5,family="serif",fontface="bold") + 
  theme_bw() +
  scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  ggsci::scale_fill_jco()+
  ggsci::scale_color_jco() +
  theme(legend.position = c(.8,.12),
        legend.title = element_blank(),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-14 多类别相关性（误差）散点图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-14 多类别相关性（误差）散点图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

	   
####################################图4-2-14（b）多类别相关性（误差）散点图绘制示例（ggprism）

ggplot(data = class_data,aes(x = Variable_01,y = Variable_02,
                             color = type)) +
  geom_errorbar(aes(ymin = Variable_02-y_error, ymax = Variable_02+y_error),
               colour="gray40",linewidth=.2) +
  geom_errorbarh(aes(xmin = Variable_01-x_error, xmax = Variable_01+x_error),
               colour="gray40",linewidth=.2) +
  geom_point(aes(fill=type,shape=type),size=2.5) +
  stat_smooth(aes(fill = type, color = type), method = "lm",
              formula = y ~ x) +
  stat_regline_equation(
    aes(label =  paste(after_stat(eq.label), ..adj.rr.label..,sep = "~~~~")),
        formula = y ~ x,size=4.5,fontface="bold",
      label.y = c(1.75,1.55)) + 
  
  scale_colour_prism(palette = "winter_bright")+
  scale_fill_prism(palette = "winter_bright") +
  scale_shape_prism()+
  theme_prism(palette = "winter_bright", base_size = 16)+
  theme(legend.position = c(.8,.15))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-14 多类别相关性（误差）散点图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-14 多类别相关性（误差）散点图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)