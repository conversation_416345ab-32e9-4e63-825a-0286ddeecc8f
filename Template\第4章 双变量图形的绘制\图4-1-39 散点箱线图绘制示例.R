"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(reshape2)


#构建数据集

head(tips)

									

####################################图4-1-39（a）散点箱线图绘制（geom_boxplot()结合geom_jittert()）
ggplot(data = tips,aes(x=day,y=total_bill)) +
  geom_boxplot(aes(fill=day),linewidth=0.5,outlier.alpha = 0) +
  geom_jitter(width = 0.2,alpha=0.8) +
  scale_fill_manual(values = colors)+
  labs(x="serif",y="Values") +
  theme(legend.position = "none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-39 散点箱线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-39 散点箱线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-39（b）散点箱线图绘制（geom_beeswarm()）

ggplot(data = tips,aes(x=day,y=total_bill)) +
  geom_boxplot(aes(fill=day),linewidth=0.5,outlier.alpha = 0) +
  ggbeeswarm::geom_beeswarm(dodge.width = 0.9,alpha=0.8) + 
  scale_fill_manual(values = colors)+
  labs(x="serif",y="Values") +
  theme(legend.position = "none",
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-39 散点箱线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-39 散点箱线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	      
	   
   