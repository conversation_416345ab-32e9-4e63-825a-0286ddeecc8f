
"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(gstat)
library(pals)
sf_use_s2(FALSE)

load("ch12.RData")

			 
			 
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")
point_data <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_huouse.csv")

#设定投影坐标
sf::st_crs(map_fig02) = 4326
#转换成sf对象
point_data_sf <- sf::st_as_sf(point_data,coords = c("long", "lat"),crs = 4326)
#生成400*400的网格
grid_df <- expand.grid(x=seq(from = st_bbox(map_fig02)[1],to = st_bbox(map_fig02)[3],length.out = 400),
                       y=seq(from = st_bbox(map_fig02)[2],to = st_bbox(map_fig02)[4],length.out = 400))
grid_sf <- sf::st_as_sf(grid_df,coords = c("x", "y"),crs = 4326)

#克里金插值
#或者使用自定义的模型参数
#model.variog <- vgm(psill=4.48, model="Exp", nugget=5.45, range=92000)				  
#ok = gstat::krige(formula = value ~ 1, locations = point_data_sf, 
#                  newdata=grid_sf, model=model.variog)		
	  

ok = gstat::krige(formula = value ~ 1, locations = point_data_sf, 
                  newdata=grid_sf, model=v.m)				  


####################################图6-7-2（a）利用gstat包进行插值计算的网格样式

				  
ggplot() +
  geom_sf(data = ok,aes(color=var1.pred),size=0.01) +
  geom_sf(data = map_fig02,fill="NA",color="black",linewidth=0.35) +
  scale_color_gradientn(colours = parula(100),breaks=seq(500,2500,500)) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(100,140,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",color="OK Result")+
  theme_classic() +
  theme(
    legend.text = element_text(size = 8),
    legend.title = element_text(size = 12,hjust = 0.5),
    legend.key.height=unit(0.7, "cm"),
    legend.key.width=unit(0.5, "cm"),
    text = element_text(family = "times",face='bold',size = 15),
    axis.text = element_text(colour = "black",face='bold',size = 12),
    axis.ticks.length=unit(.2, "cm"),
    plot.margin = margin(10, 20, 10, 10),
    axis.ticks = element_line(colour = "black",linewidth = 0.4),
    axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-2 利用gstat包进行插值计算的网格样式和根据地图文件裁剪的插值结果_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-2 利用gstat包进行插值计算的网格样式和根据地图文件裁剪的插值结果_a.pdf",
       width =5, height = 4,device = cairo_pdf)

####################################图6-7-2（b）根据地图文件裁剪的插值结果


#裁剪操作
ok_mask_result <- sf::st_intersection(ok,map_fig02)

ggplot() +
  geom_sf(data = ok_mask_result,aes(color=var1.pred),size=0.01) +
  geom_sf(data = map_fig02,fill="NA",color="black",linewidth=0.35) +
  scale_color_gradientn(colours = parula(100),breaks=seq(500,2500,500)) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(100,140,10)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",color="OK Result")+
  theme_classic() +
  theme(
    legend.text = element_text(size = 8),
    legend.title = element_text(size = 12,hjust = 0.5),
    legend.key.height=unit(0.7, "cm"),
    legend.key.width=unit(0.5, "cm"),
    text = element_text(family = "times",face='bold',size = 15),
    axis.text = element_text(colour = "black",face='bold',size = 12),
    axis.ticks.length=unit(.2, "cm"),
    plot.margin = margin(10, 20, 10, 10),
    axis.ticks = element_line(colour = "black",linewidth = 0.4),
    axis.line = element_line(colour = "black",linewidth = 0.4))

ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-2 利用gstat包进行插值计算的网格样式和根据地图文件裁剪的插值结果_b.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-2 利用gstat包进行插值计算的网格样式和根据地图文件裁剪的插值结果_b.pdf",
       width =5, height = 4,device = cairo_pdf)


