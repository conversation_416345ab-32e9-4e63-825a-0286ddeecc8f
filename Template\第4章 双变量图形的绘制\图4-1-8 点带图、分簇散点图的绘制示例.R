"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)

library(ggbeeswarm)


#构建数据
#构建数据
tips <- read_excel("\\第4章 双变量图形的绘制\\tips.xlsx")
#改变绘图属性顺序
tips$day <- factor(tips$day, 
                        levels=c("Thur","Fri","Sat","Sun"))
										

####################################图4-1-8（a）使用ggplot2绘制的默认点带图示例
ggplot(data = error_data,aes(x = x,y=y)) +
ggplot(data = tips) +
  geom_jitter(aes(x = day,y=total_bill,fill=day),shape=23,
              size=3,stroke = 0.5) +
  ggsci::scale_fill_jco() +
  labs(x="serif",y="Values") +
  theme_bw() +
  theme(legend.position="none",
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-8（b））使用ggplot2绘制的点带图示例（width=0.2）
ggplot(data = tips) +
  geom_jitter(aes(x = day,y=total_bill,fill=day),shape=23,
              size=3,stroke = 0.5,width = 0.2) +
  ggsci::scale_fill_jco() +
  labs(x="serif",y="Values") +
  theme_bw() +
  theme(legend.position="none",
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-1-8（c））使用ggbeeswarm绘制的分簇散点图示例（width=0.2）	
library(ggbeeswarm)

ggplot(data = tips) +
  ggbeeswarm::geom_beeswarm(aes(x = day,y=total_bill,fill=day),shape=21,
              size=2,cex = 2.5,stroke = 0.5) +
  ggsci::scale_fill_jco() +
  labs(x="serif",y="Values") +
  theme_bw() +
  theme(legend.position="none",
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-8 点带图、分簇散点图的绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
   