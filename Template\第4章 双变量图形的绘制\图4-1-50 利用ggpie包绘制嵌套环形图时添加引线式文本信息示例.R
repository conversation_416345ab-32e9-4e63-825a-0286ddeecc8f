"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)

library(ggpie)


#构建数据集

head(diamonds)
   
ggnestedpie(data = diamonds, group_key = c("cut", "color"), count_type = "full",
            inner_label_info = "all", inner_label_split = NULL,
            inner_label_threshold = 1, inner_label_size = 2,
            outer_label_type = "horizon", outer_label_pos = "out", 
            outer_label_info = "all",outer_label_size=5,
            border_size=0.35)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-50 利用ggpie包绘制嵌套环形图时添加引线式文本信息示例.png",
       width =8, height = 7.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-50 利用ggpie包绘制嵌套环形图时添加引线式文本信息示例.pdf",
       width =8, height = 7.5,device = cairo_pdf)