
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(forestplot)

# Cochrane data from the 'rmeta'-package
base_data <- tibble::tibble(mean  = c(0.578, 0.165, 0.246, 0.700, 0.348, 0.139, 1.017),
                            lower = c(0.372, 0.018, 0.072, 0.333, 0.083, 0.016, 0.365),
                            upper = c(0.898, 1.517, 0.833, 1.474, 1.455, 1.209, 2.831),
                            study = c("<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
                                      "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
                            deaths_steroid = c("36", "1", "4", "14", "3", "1", "8"),
                            deaths_placebo = c("60", "5", "11", "20", "7", "7", "10"),
                            OR = c("0.58", "0.16", "0.25", "0.70", "0.35", "0.14", "1.02"))


png(file="\\第7章 其他类型图的绘制\\图7-6-1 forestplot包绘制的森林图示例.png",
    family ="serif",width = 8000, height = 4000,res=1200)

pdf(file="\\第7章 其他类型图的绘制\\图7-6-1 forestplot包绘制的森林图示例.pdf",
    family ="serif",width = 8, height = 4)
	
base_data  %>% 
  forestplot(labeltext = c(study, deaths_steroid, deaths_placebo, OR),
             clip = c(0.1, 2.5),
             vertices = TRUE,
             xlog = TRUE)  %>% 
  fp_add_lines(h_3 = gpar(lty = 2), 
               h_11 = gpar(lwd = 1, columns = 1:4, col = "#000044"))  %>% 
  fp_set_style(box = "black",
               line = "black",
               summary = "black",
               txt_gp = fpTxtGp(label = gpar(fontfamily = 'serif'))
              )  %>% 
  fp_add_header(study = c("", "Study"),
                deaths_steroid = c("Deaths", "(steroid)"),
                deaths_placebo = c("Deaths", "(placebo)"),
                OR = c("", "OR"))  %>% 
  fp_append_row(mean  = 0.531,
                lower = 0.386,
                upper = 0.731,
                study = "Summary",
                OR = "0.53",
                is.summary = TRUE)  %>%  
  fp_set_zebra_style("#EFEFEF")
dev.off()