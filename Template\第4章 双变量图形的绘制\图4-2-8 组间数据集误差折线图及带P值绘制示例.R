"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(rstatix)


#读取数据
file <- "\\第4章 双变量图形的绘制\\line plot other width.xlsx"
group_line2 = read_excel(file)

#数据处理
group_line2_df <- group_line2 %>% pivot_longer(cols = !"Frequency",names_to = "type", 
                             values_to = "value",cols_vary="slowest") %>% 
                  mutate(all_type = case_when(
                         str_detect(type,"IgA") ~ "IgA",
                         str_detect(type,"anti-CD") ~ "anti-CD"))
						 
group_line2_stat <-  group_line2_df %>% group_by(Frequency, all_type) %>% 
           rstatix::get_summary_stats(type = "mean_se")		
		   
colors <- c("#87D0BF","gray60")

												  
####################################图4-2-8（a）使用ggplot2绘制的分组误差折线图示例

ggplot(data = group_line2_stat,aes(x = Frequency,y = mean)) +
  geom_line(aes(color=all_type,group=all_type),linewidth=1) +
  geom_errorbar(aes(color=all_type,ymin=mean-se,ymax=mean+se),
                linewidth=0.8,width = 16)+
  geom_point(aes(color=all_type),shape=15,size=4,stroke=1) +
  annotate("text",x=8,y=170,label="(a)",size=8,fontface='bold') +
  scale_color_manual(values = colors) +
  guides(color=guide_legend(override.aes = list(size=3.5))) +
  scale_y_continuous(limits = c(0,180),breaks = seq(0,150,50),
                     expand = c(0,0)) +
  scale_x_continuous(limits = c(0,310),breaks = seq(0,300,50),
                     ) +
  labs(x="Frequency(Hz)",y="Specific force(Mean)") +
  ggpubr::theme_pubr() +
  theme(legend.position = c(0.8,0.2),
        legend.title=element_blank(),
        legend.text = element_text(face='bold',size = 12),
        legend.margin=margin(1,5,1,5),
        text = element_text(face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 14),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_a.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_a.pdf",
       width =5, height = 4,device = cairo_pdf)
	   
####################################图4-2-8（b）ggplot2分组误差折线图绘制示例（ggline()）

ggpubr::ggline(data = group_line2_df,x = "Frequency",y="value",
               shape = 15,color="all_type",group = "all_type",
               add = "mean_se",palette=c("#87D0BF","gray60"),
               point.size=3.5) +
  annotate("text",x=1.5,y=170,label="(a)",size=8,fontface='bold') +
  scale_color_manual(values = colors) +
 
  guides(color=guide_legend(override.aes = list(size=3.5))) +
  labs(x="Frequency(Hz)",y="Specific force(Mean)") +
  theme(
        legend.title=element_blank(),
        legend.text = element_text(face='bold',size = 12),
        legend.margin=margin(1,5,1,5),
        text = element_text(face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-2-8（c）ggplot2带P值分组误差折线图绘制（星号）
#计算P值
group_line2_df %>% 
           rstatix::t_test(value ~ all_type) %>%  
           add_significance()

ggplot(data = group_line2_stat,aes(x = Frequency,y = mean)) +
  geom_line(aes(color=all_type,group=all_type),linewidth=1) +
  geom_errorbar(aes(color=all_type,ymin=mean-se,ymax=mean+se),
                linewidth=0.8,width = 16)+
  geom_point(aes(color=all_type),shape=15,size=4,stroke=1) +
  geom_segment(aes(x = 105,xend=105,y =178,yend=198)) +
#   #添加显著性水平图层
  geom_text(aes(x = 110,y = 188,label="****"),size=5,angle=-90) +
  annotate("text",x=280,y=30,label="(a)",size=8,fontface='bold') +
  scale_color_manual(values = colors) +
 
  guides(color=guide_legend(override.aes = list(size=3.5))) +
  scale_y_continuous(limits = c(0,200),breaks = seq(0,200,50),
                     expand = c(0,0)) +
  scale_x_continuous(limits = c(0,310),breaks = seq(0,300,50),
                     ) +
  labs(x="Frequency(Hz)",y="Specific force(Mean)") +
  ggpubr::theme_pubr() +
  theme(legend.position = c(0.2,0.96),
        legend.title=element_blank(),
        legend.text = element_text(face='bold',size = 12),
        legend.margin=margin(1,5,1,5),
        text = element_text(face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 14),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_c.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_c.pdf",
       width =5, height = 4,device = cairo_pdf)

####################################图4-2-8（d）ggplot2带P值分组误差折线图绘制（数值）

ggplot(data = group_line2_stat,aes(x = Frequency,y = mean)) +
  geom_line(aes(color=all_type,group=all_type),linewidth=1) +
  geom_errorbar(aes(color=all_type,ymin=mean-se,ymax=mean+se),
                linewidth=0.8,width = 16)+
  geom_point(aes(color=all_type),shape=15,size=4,stroke=1) +
  geom_segment(aes(x = 105,xend=105,y =178,yend=198)) +
#   #添加显著性水平图层
  geom_text(aes(x = 70,y = 188,label="P=0.019"),size=5,fontface="italic") +
  annotate("text",x=280,y=30,label="(a)",size=8,fontface='bold') +
  scale_color_manual(values = colors) +
 
  guides(color=guide_legend(override.aes = list(size=3.5))) +
  scale_y_continuous(limits = c(0,200),breaks = seq(0,200,50),
                     expand = c(0,0)) +
  scale_x_continuous(limits = c(0,310),breaks = seq(0,300,50),
                     ) +
  labs(x="Frequency(Hz)",y="Specific force(Mean)") +
  ggpubr::theme_pubr() +
  theme(legend.position = c(0.5,0.96),
        legend.title=element_blank(),
        legend.text = element_text(face='bold',size = 12),
        legend.margin=margin(1,5,1,5),
        text = element_text(face='bold',size = 16),
        axis.text = element_text(colour = "black",face='bold',size = 14),
        plot.margin = margin(10, 10.5, 10, 10)) 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_d.png",
       width =5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-8 组间数据集误差折线图及带P值绘制示例_d.pdf",
       width =5, height = 4,device = cairo_pdf)	   
   