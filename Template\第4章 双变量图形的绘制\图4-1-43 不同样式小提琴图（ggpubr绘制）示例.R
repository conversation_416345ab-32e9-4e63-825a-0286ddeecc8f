"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(scales)


#构建数据集

violin_data <- read_excel("G:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\小提琴图数据.xlsx")


								
####################################图4-1-43（a）使用ggpubr绘制的小提琴图示例1
ggpubr::ggviolin(data = violin_data,x="class",y="values",fill ="class",
                add="mean_sd",error.plot = "crossbar",palette="npg")
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-43 不同样式小提琴图（ggpubr绘制）示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-43 不同样式小提琴图（ggpubr绘制）示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-43（b）使用ggpubr绘制的小提琴图示例2

ggpubr::ggviolin(data = violin_data,x="class",y="values",fill ="class",
                orientation = "horiz",add = "boxplot",palette="npg")
ggsave(filename = "\\图4-1-43 不同样式小提琴图（ggpubr绘制）示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-43 不同样式小提琴图（ggpubr绘制）示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
