"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-21（a）theme_gray()绘图主题样式

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis() +
  theme_gray(base_family = "serif")
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_a.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_a.pdf",
       width = 4, height = 4,device = cairo_pdf)

###############################################图2-2-21（b）theme_bw ()绘图主题样式

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis() +
  theme_bw(base_family = "serif")
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_b.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_b.pdf",
       width = 4, height = 4,device = cairo_pdf)
	   
###############################################图2-2-21（c）theme_minimal () 绘图主题样式
ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis() +
  theme_minimal(base_family = "serif") 
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_c.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_c.pdf",
       width = 4, height = 4,device = cairo_pdf)

###############################################图2-2-21（d）theme_bw ()绘图主题样式

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis() +
  theme_classic(base_family = "serif")
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_d.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-21 ggplot2自带绘图主题样式_d.pdf",
       width = 4, height = 4,device = cairo_pdf)
