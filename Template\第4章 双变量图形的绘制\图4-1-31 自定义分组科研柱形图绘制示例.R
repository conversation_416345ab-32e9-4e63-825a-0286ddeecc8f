"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据集

paper_group_bar02 <- read_excel("\\第4章 双变量图形的绘制\\paper_group_bar_data 02.xlsx")

#修改变量顺序	
paper_group_bar02$all_type <- factor(paper_group_bar02$all_type, 
                                     levels=c("Vehicle","GCV"))	

#进行分组计算
paper_stat_2 <- paper_group_bar02 %>% 
    group_by(index) %>% 
    rstatix::wilcox_test(value ~ all_type) %>% 
    rstatix::adjust_pvalue() %>%
    rstatix::add_significance() %>% 
    rstatix::add_xy_position(x = "index")									 

#构建部分P值标签
paper_stat_2.grouped <- tibble::tribble(
  ~index, ~group1,  ~group2, ~p,   ~y.position, ~groups,
  "Tnf",  "Vehicle", "GCV", 0.00794, 1.5,       "Vehicle, GCV",
  "Il12", "Vehicle", "GCV", 0.00866, 1.65,      "Vehicle, GCV",
  "Ccl2", "Vehicle", "GCV", 0.00433, 1.75,      "Vehicle, GCV",
)

paper_stat_2.grouped <- tibble::tribble(
  ~index, ~group1,  ~group2, ~p,   ~y.position, 
  "Tnf",  "Vehicle", "GCV", 0.00794, 1.5,      
  "Il12", "Vehicle", "GCV", 0.00866, 1.65,      
  "Ccl2", "Vehicle", "GCV", 0.00433, 1.75,      
)

ggpubr::ggbarplot(data = paper_group_bar02,x="index",y="value",
                  fill="all_type",add="mean_se",
                  palette = c("lightgray","#DDF8E8"),
                  width=0.8,size=0.3,
                  ylab="Mean value",xlab="",legend = "top",
                  position = position_dodge(0.8),
                  add.params= list(width = 0.3,size=0.3),
                  ggtheme=theme_classic(base_family = "serif")) +
 ggbeeswarm::geom_beeswarm(aes(x = index, y=value,group=all_type), 
                           dodge.width = 0.9) + 
 ggsignif::geom_signif(y_position = c(1.45,1.7,1.85),
                       xmin = c(2.8,3.8,4.8), xmax = c(3.2, 4.2,5.2),
                       annotation = c("P=0.008","P=0.009","P=0.004"), 
                       tip_length = c(0.06, 0.45),
                       size=0.3,textsize = 5,vjust =-0.35,
                       family = "serif") +
 scale_y_continuous(expand = c(0, 0),limits = c(0,2),
                     breaks = seq(0,2,0.5)) +
 theme(
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-31 自定义分组科研柱形图绘制示例.png",
       width =6, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-31 自定义分组科研柱形图绘制示例.pdf",
       width =6, height = 4.5,device = cairo_pdf)

