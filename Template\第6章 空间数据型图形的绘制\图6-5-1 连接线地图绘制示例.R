"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

link_data = readxl::read_excel("\\第6章 空间数据型图形的绘制\\Link_Map_data.xlsx")

single_link <- link_data %>% dplyr::filter(line_class==1)

####################################图6-5-1（a）直角连接线样式地图绘制示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  geom_segment(data = single_link,aes(x=long_start,xend=long,y = lat_start,yend=lat,
                                      linewidth=line_width)) +
  scale_linewidth(range = c(1,2.5),breaks = c(1,3,5))+
  labs(x="Longitude",y="Latitude",linewidth="Line Width\n(class)")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.95,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.width = unit(0.8, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-5-1 连接线地图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-5-1 连接线地图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-5-1（b）圆角连接线样式地图绘制示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  geom_segment(data = single_link,aes(x=long_start,xend=long,y = lat_start,yend=lat,
                                      linewidth=line_width),
               lineend="round") +
  scale_linewidth(range = c(1,2.5),breaks = c(1,3,5))+
  labs(x="Longitude",y="Latitude",linewidth="Line Width\n(class)")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.95,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.width = unit(0.8, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-5-1 连接线地图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-5-1 连接线地图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)