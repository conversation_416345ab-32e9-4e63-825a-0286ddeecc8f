"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(wesanderson)
library(Radviz)

#构建绘制圆的数据
circleFun <- function(center = c(0,0),diameter = 1, npoints = 100){
    r = diameter / 2
    tt <- seq(0,2*pi,length.out = npoints)
    xx <- center[1] + r * cos(tt)
    yy <- center[2] + r * sin(tt)
    return(data.frame(x = xx, y = yy))
}
circle_dat <- circleFun(c(0,0),2.3,npoints = 100)


data(iris)
das <- c('Sepal.Length','Sepal.Width','Petal.Length','Petal.Width')

S <- make.S(das)
scaled <- apply(iris[,das],2,do.L)
rv <- do.radviz(scaled,S)


####################################图5-6-1（a）Radviz 图绘制示例1

p <- plot(rv)+
  geom_point(shape=21,fill="red",size=5) 

p + geom_path(data = circle_dat,aes(x,y)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_a.png",
       width =5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_a.pdf",
       width =5, height = 5,device = cairo_pdf)  

	   
####################################图5-6-1（b）Radviz 图绘制示例2 

data(iris)
das <- c('Sepal.Length','Sepal.Width','Petal.Length','Petal.Width')
S <- make.S(das)
scaled <- apply(iris[,das],2,do.L)
rv <- do.radviz(scaled,S)

sim.mat <- cosine(scaled)
in.da(S,sim.mat) 
new <- do.optimRadviz(S,sim.mat,iter=10,n=100)
new.S <- make.S(get.optim(new))
new.rv <- do.radviz(scaled,new.S)

plot(new.rv) +
  geom_point(shape=21,fill="yellow",size=5) +
  geom_path(data = circle_dat,aes(x,y)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_b.png",
       width =5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_b.pdf",
       width =5, height = 5,device = cairo_pdf) 

####################################图5-6-1（c）Radviz 图绘制示例3
data(iris)
das <- c('Sepal.Length','Sepal.Width','Petal.Length','Petal.Width')
S <- make.S(das)
scaled <- apply(iris[,das],2,do.L)
rv <- do.radviz(scaled,S)

sim.mat <- cosine(scaled)
in.da(S,sim.mat) 
new <- do.optimRadviz(S,sim.mat,iter=10,n=100)
new.S <- make.S(get.optim(new))
new.rv <- do.radviz(scaled,new.S)

plot(new.rv) +
  geom_point(data=. %>% arrange(Petal.Width),
             aes(fill=Petal.Width),shape=21,size=5) +
  geom_path(data = circle_dat,aes(x,y)) +
  scale_fill_gradientn(colours = parula(100)) 
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_c.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-6-2 采用R语言绘制的Radviz图示例_c.pdf",
       width =5.5, height = 5,device = cairo_pdf)	   
