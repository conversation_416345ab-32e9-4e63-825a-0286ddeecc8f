# R数据科学工作流

这个工作流定义了R数据科学项目的标准流程，协调多个专业代理协作完成复杂的数据分析任务。

## 工作流概述

**目标**: 提供结构化的数据科学项目执行流程
**协调者**: r-data-scientist (Dr. <PERSON>)
**参与代理**: r-statistician, r-visualizer, r-package-developer

## 阶段定义

### 阶段1: 项目启动和规划
**负责代理**: r-data-scientist
**持续时间**: 1-2天
**输出**: 项目计划、数据需求分析

#### 关键活动
1. 理解业务问题和分析目标
2. 评估数据可用性和质量
3. 制定分析策略和时间表
4. 确定成功标准和交付物

#### 决策点
- 数据是否足够支持分析目标？
- 需要哪些专业技能和代理参与？
- 项目范围和时间表是否现实？

### 阶段2: 数据探索和理解
**负责代理**: r-data-scientist + r-statistician
**持续时间**: 2-3天
**输出**: EDA报告、数据质量评估

#### 关键活动
1. 执行 `analyze-dataset` 任务
2. 数据质量评估和清洗策略
3. 统计描述和分布分析
4. 识别数据模式和异常

#### 协作模式
- r-data-scientist: 总体数据探索和协调
- r-statistician: 深度统计分析和假设检验

#### 决策点
- 数据质量是否满足建模要求？
- 是否需要额外的数据收集？
- 统计假设是否得到验证？

### 阶段3: 数据可视化和洞察发现
**负责代理**: r-visualizer + r-data-scientist
**持续时间**: 1-2天
**输出**: 可视化报告、关键洞察

#### 关键活动
1. 执行 `create-visualization` 任务
2. 创建探索性可视化
3. 设计解释性图表
4. 识别数据中的模式和趋势

#### 协作模式
- r-visualizer: 专业图表设计和实现
- r-data-scientist: 分析指导和洞察解释

#### 决策点
- 可视化是否有效传达了关键信息？
- 是否发现了意外的数据模式？
- 需要进一步探索哪些方向？

### 阶段4: 统计建模和分析
**负责代理**: r-statistician + r-data-scientist
**持续时间**: 3-5天
**输出**: 统计模型、分析结果

#### 关键活动
1. 执行 `build-model` 任务
2. 选择合适的统计方法
3. 模型构建和验证
4. 结果解释和统计推断

#### 协作模式
- r-statistician: 统计方法选择和模型构建
- r-data-scientist: 整体分析协调和业务解释

#### 决策点
- 模型性能是否达到预期？
- 统计假设是否得到满足？
- 结果是否具有实际意义？

### 阶段5: 结果可视化和报告
**负责代理**: r-visualizer + r-data-scientist
**持续时间**: 2-3天
**输出**: 最终报告、演示材料

#### 关键活动
1. 创建结果可视化
2. 设计报告布局和结构
3. 编写分析总结和建议
4. 准备演示材料

#### 协作模式
- r-visualizer: 专业图表和报告设计
- r-data-scientist: 内容组织和业务建议

#### 决策点
- 报告是否清晰传达了分析结果？
- 可视化是否支持关键结论？
- 建议是否可行和有价值？

### 阶段6: 代码打包和部署（可选）
**负责代理**: r-package-developer + r-data-scientist
**持续时间**: 2-4天
**输出**: R包、部署方案

#### 关键活动
1. 代码重构和模块化
2. 创建R包结构
3. 编写文档和测试
4. 部署和维护计划

#### 协作模式
- r-package-developer: 包开发和技术实现
- r-data-scientist: 需求定义和验收测试

#### 决策点
- 是否需要创建可重用的R包？
- 部署方案是否满足用户需求？
- 维护计划是否可持续？

## 质量控制检查点

### 每个阶段结束时
1. 执行相关的质量检查清单
2. 团队评审和反馈
3. 利益相关者确认
4. 文档更新和版本控制

### 关键质量标准
- **可重现性**: 所有分析都可以重现
- **准确性**: 结果经过验证和交叉检查
- **清晰性**: 文档和代码易于理解
- **完整性**: 所有交付物都已完成

## 风险管理

### 常见风险和应对策略
1. **数据质量问题**: 早期识别，制定清洗策略
2. **分析复杂度超预期**: 分阶段交付，调整范围
3. **技术难题**: 及时咨询专家，寻求外部支持
4. **时间压力**: 优先级管理，核心功能优先

## 沟通协议

### 日常沟通
- 每日简短同步会议
- 关键决策点的正式评审
- 问题和风险的及时上报

### 文档标准
- 所有代码都有适当注释
- 分析步骤有清晰记录
- 决策依据有文档支持

## 工具和环境

### 必需工具
- R (>= 4.0.0)
- RStudio
- Git版本控制
- R Markdown

### 推荐包
- tidyverse: 数据操作和可视化
- caret: 机器学习
- rmarkdown: 报告生成
- testthat: 测试框架

## 成功标准

### 项目成功的标志
1. 业务问题得到明确回答
2. 分析结果具有统计显著性
3. 建议具有实际可操作性
4. 代码和分析可重现
5. 利益相关者满意度高

### 质量指标
- 代码覆盖率 > 80%
- 文档完整性 > 90%
- 同行评审通过率 100%
- 客户满意度 > 4.5/5
