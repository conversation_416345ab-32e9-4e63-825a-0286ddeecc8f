"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(reshape2)


#读取数据
file <- "\\第4章 双变量图形的绘制\\点线图构建02.xlsx"
dot_line_data = read_excel(file)

#转换成长数据								
dot_line_df <- dot_line_data %>% tidyr::pivot_longer(cols = one:four,
                                                  cols_vary = "slowest",
                                                  names_to = "type", 
                                                  values_to = "values")
#改变绘图属性顺序
dot_line_df$type <- factor(dot_line_df$type, 
                            levels=c("one","two","three","four"), ordered=TRUE)
											  

colors <- c("#2FBE8F","#459DFF","#FF5B9B","#FFCC37")											  
												  
												  
####################################图4-2-2（a）点线图示例（ggplot2）
ggplot(data = dot_line_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=1.5) +
  geom_point(aes(fill=type,shape=type),size=6,stroke=1) +
  scale_color_manual(values = colors,) +
  scale_fill_manual(values = colors) +
  scale_shape_manual(values=c(21,22,23,24)) +
  guides(color=guide_legend(override.aes = list(size=4))) +
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(.15, .83),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-2 点线图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-2 点线图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-2（b）点线图示例（ggprism）
library(ggprism)
ggplot(data = dot_line_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=1.5) +
  geom_point(aes(fill=type,shape=type),size=6,stroke=1) +
  ggprism::scale_color_prism(palette = "waves") +
  ggprism::scale_fill_prism(palette = "waves") +
  scale_shape_manual(values=c(21,22,23,24)) +
  guides(color=guide_legend(override.aes = list(size=5))) +
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values") +
  theme_prism(border = TRUE) +
  coord_cartesian(clip = "off") +
  theme(legend.position = c(.15, .83))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-2 点线图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-2 点线图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
   