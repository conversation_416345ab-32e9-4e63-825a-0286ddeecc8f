"""
测试时间：2024年05月07日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第1章 科研论文配图的绘制与配色基础\\tips.csv"
tips <- readr::read_csv(data_file)

#######################################图1-2-14 a　单色系可视化配图示例

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=3) +
  labs(x="X Label", y = "Y Label")+
  scale_fill_distiller(palette = "YlGnBu",direction = 1,
                       guide = guide_colorbar(label.position = "top")) +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.3,.9),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_blank(),#去除图例
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.3,"cm"),
        text = element_text(family = "times",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据数据集绘制的单色系、双色渐变色系和多色系可视化配图示例_a.png",
       width = 3, height = 3.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据集绘制的单色系、双色渐变色系和多色系可视化配图示例_a.pdf",
       width = 3, height = 3.2,device = cairo_pdf)

######################################图1-2-14 b　双色渐变色系可视化配图示例
ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=3) +
  labs(x="X Label", y = "Y Label")+
  scale_fill_distiller(palette = "Spectral",direction = -1,
                       guide = guide_colorbar(label.position = "top")) +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.3,.9),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_blank(),#去除图例
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.3,"cm"),
        text = element_text(family = "times",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据数据集绘制的单色系、双色渐变色系和多色系可视化配图示例_b.png",
       width = 3, height = 3.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据数据集绘制的单色系、双色渐变色系和多色系可视化配图示例_b.pdf",
       width = 3, height = 3.2,device = cairo_pdf)

###################################图1-2-14 c　多色系可视化配图示例
ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=3) +
  labs(x="X Label", y = "Y Label")+
  scale_fill_distiller(palette = "Set1",direction = 1,
                       guide = guide_colorbar(label.position = "top")) +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.3,.9),
        legend.text=element_text(family = "serif",size = 12,vjust =-1),#vjust设置标签和轴之间的距离
        legend.title=element_blank(),#去除图例
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.3,"cm"),
        text = element_text(family = "times",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据数据集绘制的单色系、双色渐变色系和多色系可视化配图示例_c.png",
       width = 3, height = 3.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-14 根据数据集绘制的单色系、双色渐变色系和多色系可视化配图示例_c.pdf",
       width = 3, height = 3.2,device = cairo_pdf)
