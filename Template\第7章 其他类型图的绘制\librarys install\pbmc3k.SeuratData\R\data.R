#' PBMC 3k
#'
#' 2,700 peripheral blood mononuclear cells (PBMC) from 10X genomics
#'
#' @format A \code{Seurat} object with the PBMC 3k dataset
#'
#' @source \url{https://support.10xgenomics.com/single-cell-gene-expression/datasets/1.1.0/pbmc3k}
#'
#' @seealso \code{\link{pbmc3k.final}}
#'
#' @examples
#' \dontrun{
#' if (requireNamespace("Seurat", quietly = TRUE)) {
#'   url <- 'http://cf.10xgenomics.com/samples/cell-exp/1.1.0/pbmc3k/pbmc3k_filtered_gene_bc_matrices.tar.gz'
#'   curl::curl_download(url = url, destfile = basename(path = url))
#'   untar(tarfile = basename(path = url))
#'   pbmc.data <- Seurat::Read10X(data.dir = 'filtered_gene_bc_matrices/hg19/')
#'   pbmc3k <- Seurat::CreateSeuratObject(counts = pbmc.data, project = 'pbmc3k', min.cells = 3, min.features = 200)
#'   # Annotations come from Seurat's PBMC3k Guided Clustering Tutorial
#'   # https://satijalab.org/seurat/v3.0/pbmc3k_tutorial.html
#'   annotations <- readRDS(file = system.file('extdata/annotations/annotations.Rds', package = 'pbmc3k.SeuratData'))
#'   pbmc3k <- Seurat::AddMetaData(object = pbmc3k, metadata = annotations)
#'   # Clean up downloaded files
#'   file.remove(basename(path = url))
#'   unlink(x = 'filtered_gene_bc_matrices/', recursive = TRUE)
#' }
#' }
#'
"pbmc3k"

#' Processed PBMC 3k Dataset
#'
#' 2,638 PBMCs processed using the standard Seurat workflow as demonstrated in
#' \href{https://satijalab.org/seurat/v3.0/pbmc3k_tutorial.html}{Seurat's guided clustering tutorial}
#'
#' @format A \code{Seurat} object with the processed PBMC 3k dataset
#'
#' @source \url{https://support.10xgenomics.com/single-cell-gene-expression/datasets/1.1.0/pbmc3k} \url{https://satijalab.org/seurat/v3.0/pbmc3k_tutorial.html}
#'
#' @seealso \code{\link{pbmc3k}}
#'
#' @examples
#' \dontrun{
#' if (requireNamespace("Seurat", quietly = TRUE) && requireNamsepace("pbmc3k", quietly = TRUE)) {
#'   attachNamespace('Seurat')
#'   attachNamespace('pbmc3k.SeuratData')
#'   data("pbmc3k")
#'   pbmc3k.final <- pbmc3k
#'   pbmc3k.final[['percent.mt']] <- PercentageFeatureSet(pbmc3k.final, pattern = '^MT-')
#'   pbmc3k.final <- subset(x = pbmc3k.final, subset = nFeature_RNA > 200 & nFeature_RNA < 2500 & percent.mt < 5)
#'   pbmc3k.final <- NormalizeData(pbmc3k.final)
#'   pbmc3k.final <- FindVariableFeatures(pbmc3k.final)
#'   pbmc3k.final <- ScaleData(pbmc3k.final, features = rownames(pbmc3k.final))
#'   pbmc3k.final <- RunPCA(pbmc3k.final, features = VariableFeatures(pbmc3k.final))
#'   pbmc3k.final <- JackStraw(pbmc3k.final)
#'   pbmc3k.final <- ScoreJackStraw(pbmc3k.final, dims = 1:20)
#'   pbmc3k.final <- FindNeighbors(pbmc3k.final, dims = 1:10)
#'   pbmc3k.final <- FindClusters(pbmc3k.final, resolution = 0.5)
#'   pbmc3k.final <- RunUMAP(pbmc3k.final, dims = 1:10)
#'   new.cluster.ids <- c("Naive CD4 T", "Memory CD4 T", "CD14+ Mono", "B", "CD8 T", "FCGR3A+ Mono", "NK", "DC", "Platelet")
#'   names(new.cluster.ids) <- levels(pbmc3k.final)
#'   pbmc3k.final <- RenameIdents(pbmc3k.final, new.cluster.ids)
#' }
#' }
#'
"pbmc3k.final"