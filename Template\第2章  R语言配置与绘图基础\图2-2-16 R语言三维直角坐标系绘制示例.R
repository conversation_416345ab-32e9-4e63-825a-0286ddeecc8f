"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

library(plot3D)



###############################################图2-2-16（a）三维直角坐标系刻度样式

gpng(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_a.png"
    ,family ="serif",width = 800, height = 650,res=150)
scatter3D(x, rep(1,len), z[,1], bty = "b2",colkey = FALSE,
          phi = 14,theta = 45,
          pch = 18,alpha = 0,ticktype = "detailed", expand =0.5,
          ylim = c(1,5),zlim = c(0,0.5),
          xlab = "X Label", ylab = "Y Label", zlab = "Z Label")
dev.off()

svg(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_a.svg",
    family ="serif")
scatter3D(x, rep(1,len), z[,1], bty = "b2",colkey = FALSE,
          phi = 14,theta = 45,
          pch = 18,alpha = 0,ticktype = "detailed", expand =0.5,
          ylim = c(1,5),zlim = c(0,0.5),
          xlab = "X Label", ylab = "Y Label", zlab = "Z Label")
dev.off()

pdf(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_a.pdf",
    family ="serif" )
#par(family = "serif")
scatter3D(x, rep(1,len), z[,1], bty = "b2",colkey = FALSE,
          phi = 14,theta = 45,
          pch = 18,alpha = 0,ticktype = "detailed", expand =0.5,
          ylim = c(1,5),zlim = c(0,0.5),
          xlab = "X Label", ylab = "Y Label", zlab = "Z Label"
)
dev.off()

###############################################图2-2-16（b）三维直角坐标系柱形图

data(VADeaths)

pdf(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_b.pdf",
    family ="serif" )
svg(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_b.svg",
    family ="serif")
hist3D(z = VADeaths, scale = FALSE, expand = 0.01, 
       phi = 20,bty = "b2",
       col = "#0073C2", border = "black", 
       shade = 0.2, ltheta = 90,
       space = 0.3, ticktype = "detailed", d = 2,
       xlab = "X Label", ylab = "Y Label", zlab = "Z Label")
dev.off()

								 
png(file="\\第2章  R语言配置与绘图基础\\图2-2-16 R语言三维直角坐标系绘制示例_b.png",
    width = 800, height = 650,res=120,family ="serif" )
hist3D(z = VADeaths, scale = FALSE, expand = 0.01, 
       phi = 20,bty = "b2",
       col = "#0073C2", border = "black", 
       shade = 0.2, ltheta = 90,
       space = 0.3, ticktype = "detailed", d = 2,
       xlab = "X Label", ylab = "Y Label", zlab = "Z Label")
dev.off()

