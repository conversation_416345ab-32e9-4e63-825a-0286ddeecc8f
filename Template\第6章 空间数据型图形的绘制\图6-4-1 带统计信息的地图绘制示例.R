"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(pals)
library(scatterpie)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

values01 <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_City.csv")

colors <- c("#2FBE8F","#459DFF","#FF5B9B","#FFCC37")

####################################图6-4-1（a）带柱形图的地图示例

values_df <- values01 %>% tidyr::pivot_longer(cols = c("orange","apple","banana","watermelon"))

#对数据进行缩放处理和新变量构建
MaxH <- max(values_df$value)
width<-1.3
Scale<-3 
values_df <- values_df %>% dplyr::mutate(hjust1=ifelse(name=='orange',-width, 
                                         ifelse(name=='apple',-width/2,
                                         ifelse(name=='banana',0,width/2))),
                                  hjust2=ifelse(name=='orange',-width/2, 
                                         ifelse(name=='apple',0,
                                         ifelse(name=='banana',width/2,width))),
                                  value_scale=value/MaxH*Scale)
#构建图例数据
Lengend_data<-data.frame(X=rep(132,5),Y=rep(32,5),index=seq(0,MaxH,MaxH/4))


ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  geom_rect(data = values_df, aes(xmin = long +hjust1, xmax = long+hjust2,
                                  ymin = lat, ymax = lat + value_scale ,
                                  fill= name),
            linewidth =0.25, colour ="black", alpha = 1) +
  #添加图例图层
  geom_rect(data = Lengend_data,aes(xmin = X , xmax = X+0.7 ,
                                    ymin = Y, ymax = Y+index / MaxH * Scale),
            linewidth =0.25, colour ="black",fill = "NA",alpha = 1)+
  geom_text(data = Lengend_data,aes(x=X+1.5,y=  Y+index / MaxH * Scale,
                                    label=index),size=2,family="serif",fontface='bold') +
  scale_fill_manual(values = colors)+
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.97,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_blank(),
        legend.key.size = unit(0.3, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-4-1（b）带饼图的地图示例1
library(scatterpie)

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  scatterpie::geom_scatterpie(data = values01,aes(x=long, y=lat, group=SP_ID,r=1.7),
                              cols=c("orange","apple","banana","watermelon"), 
                              color="black", alpha=1,size=0.35) +
  scale_fill_manual(values = colors)+
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.97,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_blank(),
        legend.key.size = unit(0.3, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-4-1（c）带饼图的地图示例2
library(scatterpie)

#计算半径大小
values01$Sumindex<-rowSums(values01[,c("orange","apple","banana","watermelon")])
Bubble_Scale<-1.5
radius<-sqrt(values01$Sumindex/pi)
Max_radius<-max(radius)
values01$radius<-radius/Max_radius*Bubble_Scale


ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  scatterpie::geom_scatterpie(data = values01,aes(x=long, y=lat, group=SP_ID,r=radius),
                              cols=c("orange","apple","banana","watermelon"), 
                              color="black", alpha=1,size=0.35) +
  scatterpie::geom_scatterpie_legend(values01$radius, x=135, y=32, n=2,
                                     labeller=function(x) 10*x,size=3,family="serif",
                                     fontface='bold')+
  scale_fill_manual(values = colors)+
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.97,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_blank(),
        legend.key.size = unit(0.4, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggplot2::ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-4-1 带统计信息的地图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   
	   