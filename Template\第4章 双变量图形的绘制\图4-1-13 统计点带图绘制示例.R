"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)

library(ggprism)
library(ggbeeswarm)
library(rstatix)


data("wings")


##数据处理
wings$measure <- wings$measure %>% 
  gsub("\\.", " ", .) %>% 
  tools::toTitleCase() %>% 
  factor(., levels = c("Wing Size", "Cell Size", "Cell Number"))

#计算P值
wings_pvals <- wings %>%
  group_by(sex, measure) %>%
  rstatix::t_test(
    percent.change ~ genotype, 
    p.adjust.method = "BH", 
    var.equal = TRUE, 
    ref.group = "Tps1MIC/+"
  ) %>%
  rstatix::add_x_position(x = "measure", dodge = 0.9) %>% # dodge must match points
  mutate(label = c("***", "*", "P = 0.26", "***", "***", "P = 0.65"))

#################################### 图4-1-13 (a)使用ggplot2默认绘图主题绘制统计点带图示例
ggplot(wings, aes(x = measure, y = percent.change)) + 
  ggbeeswarm::geom_beeswarm(aes(fill = genotype), dodge.width = 0.9, 
                            shape = 21) + 
  facet_wrap(~ sex, scales = "free",
    labeller = labeller(sex = c(male = "\u2642", female = "\u2640"))) + 
  geom_hline(yintercept = 0, linetype = 2, linewidth = 0.3) + 
  stat_summary(geom = "crossbar", aes(fill = genotype),fun = mean,
               position = position_dodge(0.9), colour = "red", 
               linewidth = 0.4, width = 0.7,show.legend = FALSE) + 
  add_pvalue(wings_pvals, y = 10, xmin = "xmin", xmax = "xmax", 
              tip.length = 0, fontface = "italic", label.size = 3.5,
              lineend = "round", bracket.size = 0.5) + 
  scale_y_continuous(limits = c(-20, 12), expand = c(0, 0),
    breaks = seq(-20, 10, 5), guide = "prism_offset") + 
  labs(y = "% change") + 
  scale_fill_manual(values = c("#026FEE", "#87FFFF"), 
     labels = c(expression("Tps"*1^italic("MIC")~"/ +"), 
               expression("Tps"*1^italic("MIC")))) + 
  theme(legend.position = "bottom",
        legend.title = element_blank(),
        text = element_text(size = 15),
        axis.title.x = element_blank(),
        strip.text = element_text(size = 16),
        legend.spacing.x = unit(0, "pt"),
        legend.text = element_text(margin = margin(r = 20))) + 
  geom_text(data = data.frame(sex = factor("female", levels = c("male", "female")), 
                              measure = "Cell Number", 
                              percent.change = -18.5, 
                              lab = "(n = 10)"), 
            aes(label = lab)) + 
  guides(fill = guide_legend(override.aes = list(size = 3)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-13 统计点带图绘制示例_a.png",
       width =7, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-13 统计点带图绘制示例_a.pdf",
       width =7, height = 4.5,device = cairo_pdf)
	   
	   
#################################### 图4-1-13 (b)使用ggprism绘制主题函数统计点带图示例

ggplot(wings, aes(x = measure, y = percent.change)) + 
  ggbeeswarm::geom_beeswarm(aes(fill = genotype), dodge.width = 0.9, 
                            shape = 21) + 
  facet_wrap(~ sex, scales = "free",
    labeller = labeller(sex = c(male = "\u2642", female = "\u2640"))) + 
  geom_hline(yintercept = 0, linetype = 2, linewidth = 0.3) + 
  stat_summary(geom = "crossbar", aes(fill = genotype),fun = mean,
               position = position_dodge(0.9), colour = "red", 
               linewidth = 0.4, width = 0.7,show.legend = FALSE) + 
  add_pvalue(wings_pvals, y = 10, xmin = "xmin", xmax = "xmax", 
              tip.length = 0, fontface = "italic", 
              lineend = "round", bracket.size = 0.5) + 
  theme_prism(base_fontface = "plain", base_line_size = 0.7) + 
  scale_x_discrete(guide = guide_prism_bracket(width = 0.15), 
                   labels = scales::wrap_format(5)) + 
  scale_y_continuous(limits = c(-20, 12), expand = c(0, 0),
    breaks = seq(-20, 10, 5), guide = "prism_offset") + 
  labs(y = "% change") + 
  scale_fill_manual(values = c("#026FEE", "#87FFFF"), 
    labels = c(expression("Tps"*1^italic("MIC")~"/ +"), 
               expression("Tps"*1^italic("MIC")))) + 
  theme(legend.position = "bottom",
        axis.title.x = element_blank(),
        strip.text = element_text(size = 14),
        legend.spacing.x = unit(0, "pt"),
        legend.text = element_text(margin = margin(r = 20))) + 
  geom_text(data = data.frame(sex = factor("female", levels = c("male", "female")), 
                              measure = "Cell Number", 
                              percent.change = -18.5, 
                              lab = "(n = 10)"), 
            aes(label = lab)) + 
  guides(fill = guide_legend(override.aes = list(size = 3)))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-13 统计点带图绘制示例_b.png",
       width =6.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-13 统计点带图绘制示例_b.pdf",
       width =6.5, height = 4.5,device = cairo_pdf)