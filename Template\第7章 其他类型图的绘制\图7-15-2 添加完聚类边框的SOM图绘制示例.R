
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(kohonen)

iris_unique <- unique(iris[complete.cases(iris),]) # Remove duplicates
#scale data
iris.sc <- scale(iris_unique[, 1:4])

#build grid
iris.grid <- kohonen::somgrid(xdim = 10, ydim=10, topo="hexagonal", toroidal=TRUE)

# build model
set.seed(33)
iris.som <- kohonen::som(iris.sc, grid=iris.grid, rlen=700, alpha=c(0.05,0.01), 
                         keep.data=TRUE)

png(file="\\第7章 其他类型图的绘制\\图7-15-2 添加完聚类边框的SOM图绘制示例.png",
    width = 6000, height = 6000,res=1200)
som.hc <- cutree(hclust(object.distances(iris.som, "codes")), 8)
plot(iris.som, type = "property", property = getCodes(iris.som, 1)[,1], 
     shape = "straight", palette.name = hcl.colors, main = "", cex = 1.2)
add.cluster.boundaries(iris.som, som.hc,lwd = 4,col="red")
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-15-2 添加完聚类边框的SOM图绘制示例.pdf",
    width = 6, height = 6)
som.hc <- cutree(hclust(object.distances(iris.som, "codes")), 8)
plot(iris.som, type = "property", property = getCodes(iris.som, 1)[,1], 
     shape = "straight", palette.name = hcl.colors, main = "", cex = 1.2)
add.cluster.boundaries(iris.som, som.hc,lwd = 4,col="red")
dev.off()