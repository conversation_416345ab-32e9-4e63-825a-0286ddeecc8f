"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第2章  R语言配置与绘图基础\\tips.csv"
tips <- readr::read_csv(data_file)

###############################################图2-2-20（a）ggplot2 默认图例位置

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis(option="magma") +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  scale_x_continuous(expand = c(0, 0),limits = c(0, 55),
                     breaks = seq(0, 55, by = 5)) +
  theme(
        legend.text=element_text(family = "serif",size = 12),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_a.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_a.pdf",
       width = 4, height = 4,device = cairo_pdf)
	   
###############################################图2-2-20（b）ggplot2 图例位置设置（top）

ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis(option="magma") +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  scale_x_continuous(expand = c(0, 0),limits = c(0, 55),
                     breaks = seq(0, 55, by = 5)) +
  theme(legend.position = "top",
        legend.text=element_text(family = "serif",size = 12),#vjust设置标签和轴之间的距离
        legend.title=element_text(family = "serif",size = 12.5),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_b.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_b.pdf",
       width = 4, height = 4,device = cairo_pdf)

###############################################图2-2-20（c）ggplot2 图例位置设置（c(0.3,0.8)）
ggplot(data = tips,aes(x = total_bill,y = tip,fill=tip))+
  geom_point(shape=21,size=5) +
  labs(x="X Label", y = "Y Label")+
  viridis::scale_fill_viridis(option="magma") +
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(0, 12),
                     breaks = seq(0, 12, by = 2)) +
  scale_x_continuous(expand = c(0, 0),limits = c(0, 55),
                     breaks = seq(0, 55, by = 5)) +
  theme(legend.position = "inside",
        legend.position.inside = c(.3,.85),
        legend.background =element_blank(),
        legend.text=element_text(family = "serif",size = 12,vjust =1),#vjust设置标签和轴之间的距离
        legend.title=element_blank(),#去除图例
        legend.direction = "horizontal",
        legend.key.width = unit(.65, "cm"),
        legend.key.height = unit(.4,"cm"),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_c.png",
       width = 4, height = 4, dpi = 900,device=png)
ggsave(filename = "\\第2章  R语言配置与绘图基础\\图2-2-20 ggplot2 不同图例位置设置可视化结果_c.pdf",
       width = 4, height = 4,device = cairo_pdf)
