# R数据分析质量检查清单

[[LLM: 这是一个R数据分析质量检查清单。执行时：
1. 逐项检查分析的各个方面
2. 为每项提供通过/失败/部分通过的评估
3. 记录发现的问题和改进建议
4. 生成最终的质量评估报告]]

## 检查清单概述

**分析项目**: {{project_name}}
**检查日期**: {{check_date}}
**检查者**: {{checker_name}}
**分析师**: {{analyst_name}}

---

## 1. 数据质量检查 (权重: 25%)

[[LLM: 仔细检查数据相关的所有方面，确保数据质量符合分析要求]]

### 1.1 数据完整性
- [ ] **数据来源明确**: 数据来源、收集方法、时间范围清楚记录
- [ ] **数据字典完整**: 所有变量都有清晰的定义和说明
- [ ] **样本量充足**: 样本量满足统计分析的要求
- [ ] **数据版本控制**: 使用的数据版本明确，可追溯

### 1.2 缺失值处理
- [ ] **缺失值识别**: 正确识别和标记所有缺失值
- [ ] **缺失模式分析**: 分析缺失值的模式和原因
- [ ] **处理方法合理**: 缺失值处理方法适当且有理论依据
- [ ] **影响评估**: 评估缺失值处理对结果的影响

### 1.3 异常值检测
- [ ] **异常值识别**: 使用适当方法识别异常值
- [ ] **异常值验证**: 验证异常值的真实性
- [ ] **处理决策记录**: 记录异常值的处理决策和理由
- [ ] **敏感性分析**: 评估异常值对结果的影响

### 1.4 数据变换
- [ ] **变换合理性**: 数据变换有明确的统计或业务理由
- [ ] **变换记录**: 所有数据变换步骤都有详细记录
- [ ] **可重现性**: 数据预处理过程完全可重现
- [ ] **原始数据保留**: 保留原始数据，不直接修改

**第1部分得分**: ___/16 (通过标准: ≥13)

---

## 2. 统计方法检查 (权重: 30%)

[[LLM: 检查统计方法的选择和应用是否正确]]

### 2.1 方法选择
- [ ] **方法适当性**: 统计方法适合研究问题和数据类型
- [ ] **假设检验**: 检验统计方法的基本假设
- [ ] **样本量计算**: 进行了适当的功效分析或样本量计算
- [ ] **多重比较**: 适当处理多重比较问题

### 2.2 模型构建
- [ ] **模型规范**: 模型规范合理，包含相关变量
- [ ] **变量选择**: 变量选择方法合理，避免过拟合
- [ ] **交互作用**: 考虑了重要的交互作用
- [ ] **模型验证**: 进行了适当的模型验证

### 2.3 假设检验
- [ ] **假设明确**: 原假设和备择假设明确表述
- [ ] **显著性水平**: 显著性水平预先设定且合理
- [ ] **检验功效**: 考虑了统计检验的功效
- [ ] **效应量**: 报告了效应量，不仅仅是p值

### 2.4 不确定性量化
- [ ] **置信区间**: 提供了适当的置信区间
- [ ] **标准误**: 正确计算和报告标准误
- [ ] **敏感性分析**: 进行了敏感性分析
- [ ] **稳健性检验**: 使用稳健的统计方法

**第2部分得分**: ___/16 (通过标准: ≥13)

---

## 3. 代码质量检查 (权重: 20%)

[[LLM: 检查R代码的质量、可读性和可重现性]]

### 3.1 代码结构
- [ ] **代码组织**: 代码结构清晰，逻辑合理
- [ ] **函数化**: 重复代码封装成函数
- [ ] **模块化**: 代码分解为逻辑模块
- [ ] **命名规范**: 变量和函数命名清晰一致

### 3.2 代码质量
- [ ] **注释充分**: 关键步骤有清晰注释
- [ ] **错误处理**: 包含适当的错误处理机制
- [ ] **代码效率**: 代码运行效率合理
- [ ] **最佳实践**: 遵循R语言最佳实践

### 3.3 可重现性
- [ ] **随机种子**: 设置了随机种子确保可重现
- [ ] **环境记录**: 记录了R版本和包版本信息
- [ ] **路径管理**: 使用相对路径，避免硬编码
- [ ] **依赖管理**: 明确列出所有依赖包

### 3.4 版本控制
- [ ] **Git使用**: 使用版本控制系统
- [ ] **提交信息**: 提交信息清晰有意义
- [ ] **分支策略**: 使用合理的分支策略
- [ ] **代码审查**: 进行了代码审查

**第3部分得分**: ___/16 (通过标准: ≥13)

---

## 4. 结果解释检查 (权重: 25%)

[[LLM: 检查结果的解释是否准确、完整和合理]]

### 4.1 结果准确性
- [ ] **计算正确**: 所有统计量计算正确
- [ ] **表格准确**: 表格数据准确无误
- [ ] **图表正确**: 图表正确反映数据
- [ ] **结论一致**: 结论与结果一致

### 4.2 解释完整性
- [ ] **统计意义**: 正确解释统计显著性
- [ ] **实际意义**: 讨论结果的实际意义
- [ ] **局限性**: 明确说明分析的局限性
- [ ] **不确定性**: 适当讨论结果的不确定性

### 4.3 可视化质量
- [ ] **图表选择**: 选择合适的图表类型
- [ ] **图表清晰**: 图表清晰易读，标签完整
- [ ] **颜色使用**: 颜色使用合理，考虑色盲友好
- [ ] **图表说明**: 图表有完整的标题和说明

### 4.4 报告质量
- [ ] **结构清晰**: 报告结构逻辑清晰
- [ ] **语言准确**: 使用准确的统计术语
- [ ] **受众适宜**: 语言和内容适合目标受众
- [ ] **建议实用**: 提供了实用的建议和下一步行动

**第4部分得分**: ___/16 (通过标准: ≥13)

---

## 5. 总体评估

### 5.1 得分汇总
- 数据质量: ___/16 (25%)
- 统计方法: ___/16 (30%)  
- 代码质量: ___/16 (20%)
- 结果解释: ___/16 (25%)

**总分**: ___/64
**加权得分**: ___/100

### 5.2 质量等级
- **优秀** (90-100分): 分析质量极高，可直接使用
- **良好** (80-89分): 分析质量良好，需要少量改进
- **合格** (70-79分): 分析基本合格，需要一些改进
- **需要改进** (60-69分): 分析有明显问题，需要重大改进
- **不合格** (<60分): 分析质量不达标，需要重新进行

**本次评估等级**: {{quality_grade}}

### 5.3 主要发现
**优点**:
{{REPEAT_START}}
- {{strength_point}}
{{REPEAT_END}}

**需要改进的方面**:
{{REPEAT_START}}
- {{improvement_point}}
{{REPEAT_END}}

### 5.4 改进建议
{{REPEAT_START}}
{{priority_level}}. {{improvement_recommendation}}
{{REPEAT_END}}

### 5.5 下一步行动
- [ ] {{action_item_1}}
- [ ] {{action_item_2}}
- [ ] {{action_item_3}}

---

**检查完成时间**: {{completion_time}}
**建议复查时间**: {{review_time}}

[[LLM: 检查完成后，生成一个简洁的总结报告，包括：
1. 总体质量评级
2. 主要问题和风险
3. 优先改进建议
4. 是否建议发布/使用此分析]]
