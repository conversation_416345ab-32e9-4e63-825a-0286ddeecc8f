"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
clev_data01 = tibble(labels = c('a','b','c','d','e',"f"),
                    values =c(2,3.5,4.2,5,6.8,7.5))


										

####################################图4-1-4（a）利用ggplot2绘制的横向 “棒棒糖”图 
ggplot(data = clev_data01) +
   geom_segment(aes(x = 00, xend=values,y=labels,yend=labels)) +
   geom_point(aes(x = values, y= labels),shape=21,fill="gray60",
              size = 7) +
  labs(x="Values",y="Class") +
  scale_x_continuous(expand = c(0, 0),limits = c(0, 8)) +
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-4（b）利用ggplot2绘制的纵向 “棒棒糖”图
ggplot(data = clev_data01) +
   geom_segment(aes(x = 00, xend=values,y=labels,yend=labels)) +
   geom_point(aes(x = values, y= labels),shape=21,fill="gray60",
              size = 7) +
  labs(x="Values",y="Class") +
  scale_x_continuous(expand = c(0, 0),limits = c(0, 8)) +
 #转换横纵轴
  coord_flip()+
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-1-4（c）利用ggplot2绘制的多组 “棒棒糖”图   
	   
clev_data02 = tibble(labels = c('a','b','c','d','e',"f"),
                     value01 =c(2,3.5,4.2,5,6.8,7.5),
                     value02 =c(1.1,3,4.9,5.7,6,7))
clev_data02_long = clev_data02 %>% 
  pivot_longer(cols = starts_with("value"),names_to = "type",
               values_to = "values")  

ggplot() +
   geom_segment(data = clev_data02,aes(x = value01,xend=value02,
                                       y=labels,yend=labels)) +
   geom_point(data = clev_data02_long,aes(x = values, y= labels,fill=type),
              shape=21,size = 6) +
   
   labs(x="Values",y="Class") +
   scale_x_continuous(expand = c(0, 0),limits = c(0, 8)) +
   ggprism::scale_fill_prism(palette = "waves") +
   theme(legend.position = c(0.2,0.88),
         legend.background = element_blank(),
         legend.title=element_blank(),
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-4 “棒棒糖”图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)
			   
