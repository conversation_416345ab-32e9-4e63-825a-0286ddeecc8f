
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)

library(plotrix)

# make some reference data 参考值，即相当于观测值
ref<-rnorm(30,sd=2)
# add a little noise，相当于第一种模型的模拟值
model1 <- ref+rnorm(30)/3
# add more noise， 相当于第二种模型的模拟值
model2 <- ref+rnorm(30)
# add more noise， 相当于第三种模型的模拟值，误差更大
model3 <- ref+rnorm(30)*1.5
model4 <- ref+rnorm(30)/2
model5 <- ref+rnorm(30)*1.3


####################################图7-5-1（a）泰勒图默认样式绘制示例
png(file="\\第7章 其他类型图的绘制\\图7-5-1 泰勒图绘制示例_a.png",
    family ="serif",width = 5100, height = 5300,res=1200)
pdf(file="\\第7章 其他类型图的绘制\\图7-5-1 泰勒图绘制示例_a.pdf",
    family ="serif",width = 5.1, height = 5.3)	
	
par(family = "serif",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 3,ref.sd=T,col = "#352A87",main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 3,add=TRUE,col="#1283D4")
# now add the worst model
taylor.diagram(ref,model3,pch = 17,pcex = 3,add=TRUE,col="#33B7A0")
taylor.diagram(ref,model4,pch = 18,pcex = 3,add=TRUE,col="#D1BA58")
taylor.diagram(ref,model5,pch = 19,pcex = 3,add=TRUE,col="#F9FB0E")
lpos<-1.6*sd(ref)
legend(lpos-0.2,lpos+0.1, # 可以改变图例的位置
       legend=c("model01","model02", "model03","model04","model05"),
       pch=c(15,16,17,18,19),col=c('#352A87','#1283D4','#33B7A0','#D1BA58',"#F9FB0E"))
dev.off()

	   
####################################图7-5-1（b）泰勒图全扇叶样式绘制示例（pos.cor=F） 
png(file="\\第7章 其他类型图的绘制\\图7-5-1 泰勒图绘制示例_b.png",
    family ="serif",width = 7000, height = 5500,res=1200)
	
pdf(file="\\第7章 其他类型图的绘制\\图7-5-1 泰勒图绘制示例_b.pdf",
    family ="serif",width = 7, height = 5.5)	
	
par(family = "serif",mar = rep(1,4))
taylor.diagram(ref,model1,pch = 15,pcex = 2.5,ref.sd=T,col = "#352A87",pos.cor=F,main = "")
taylor.diagram(ref,model2,pch = 16,pcex = 2.5,add=TRUE,col="#1283D4")
# now add the worst model
taylor.diagram(ref,model3,pch = 17,pcex = 2.5,add=TRUE,col="#33B7A0")
taylor.diagram(ref,model4,pch = 18,pcex = 2.5,add=TRUE,col="#D1BA58")
taylor.diagram(ref,model5,pch = 19,pcex = 2.5,add=TRUE,col="#F9FB0E")
lpos<-1.6*sd(ref)
legend(lpos-0.7,lpos+0.65, # 可以改变图例的位置
       legend=c("model01","model02", "model03","model04","model05"),
       cex = 0.8,
       pch=c(15,16,17,18,19),col=c('#352A87','#1283D4','#33B7A0','#D1BA58',"#F9FB0E"))
dev.off()


