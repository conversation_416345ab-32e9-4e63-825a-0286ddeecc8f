"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggprism)



#读取数据
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\分组误差线图构建.xlsx"
error_line = read_excel(file)


colors <- c("#2FBE8F","#459DFF","#FF5B9B","#FFCC37")											  
												  
												  
####################################图4-2-4（a）误差折线图绘制示例1
ggplot(data = error_line,aes(x = time,y = mean,group=type)) +
  geom_line(colour="black",linewidth=1) +
  geom_errorbar(aes(ymin=mean-sd,ymax=mean+sd),linewidth=0.8)+
  geom_point(aes(fill=type),shape=21,size=6,stroke=1.5) +
  scale_fill_manual(values = colors) +
  annotate("text",x=1,y=27,label="a.",size=8,family='serif',fontface='bold') +
  guides(fill=guide_legend(override.aes = list(size=4))) +
  
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values Change") +
  theme_bw() +
  theme(legend.position = "top",
        legend.margin=margin(1,1,-5,1),
        legend.title = element_blank(),
        #legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-4 误差折线图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-4 误差折线图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-4（b）误差折线图绘制示例2

ggplot(data = error_line,aes(x = time,y = mean,group=type)) +
  geom_line(colour="black",linewidth=1) +
  geom_errorbar(aes(ymin=mean-sd,ymax=mean+sd),linewidth=0.8)+
  geom_point(aes(fill=type,shape=type),size=6,stroke=1) +
  annotate("text",x=1,y=27,label="a.",size=8,family='serif',
         fontface='bold') +
  scale_fill_manual(values = colors) +
  scale_shape_manual(values=c(21,22,23,24)) +
  guides(fill=guide_legend(override.aes = list(size=4))) +
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values Change") +
  theme_bw() +
  theme(legend.position = "top",
        legend.margin=margin(1,1,-5,1),
        legend.title = element_blank(),
        #legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-4 误差折线图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-4 误差折线图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
   