name: R-expansion-pack
version: 2.0.0
short-title: R语言数据分析和统计建模扩展包（增强版）
description: 专门用于R语言数据分析、统计建模、可视化和报告生成的BMAD框架扩展包。采用严格的测试驱动开发和质量门控制。
author: PhD Research Team
category: data-science
created_at: '2025-07-01T00:00:00.000Z'

# 扩展包元数据
metadata:
  tags:
    - R语言
    - 数据分析
    - 统计建模
    - 数据可视化
    - 科学计算
  requirements:
    - R (>= 4.0.0)
    - RStudio (推荐)
    - 基础统计学知识
  license: MIT
  homepage: https://github.com/phd-research/R-expansion-pack
  repository: https://github.com/phd-research/R-expansion-pack.git

# 主要组件
main_orchestrator: r-data-scientist
agents:
  - r-data-scientist
  - r-statistician
  - r-visualizer
  - r-package-developer

# 核心能力
core_capabilities:
  - 数据清洗和预处理
  - 探索性数据分析
  - 统计建模和假设检验
  - 机器学习算法实现
  - 数据可视化
  - R包开发
  - 科学报告生成

# 文件清单
files:
  agents:
    - r-data-scientist.md      # 主协调者 - Dr. <PERSON> Wei
    - r-statistician.md        # 统计专家 - Dr. Sarah Chen
    - r-visualizer.md          # 可视化专家 - Alex Rodriguez
    - r-package-developer.md   # 包开发专家 - Dr. Michael Zhang

  tasks:
    - create-doc.md            # 核心工具 - 文档创建
    - execute-checklist.md     # 核心工具 - 检查清单执行
    - validate-r-package.md    # 新增 - R包验证任务
    - analyze-dataset.md       # 领域任务 - 数据集分析
    - build-model.md           # 领域任务 - 模型构建
    - create-visualization.md  # 领域任务 - 可视化创建

  templates:
    - analysis-report-tmpl.md  # 分析报告模板
    - model-summary-tmpl.md    # 模型总结模板
    - r-package-tmpl.md        # R包开发模板

  checklists:
    - analysis-checklist.md    # 分析质量检查清单
    - code-quality-checklist.md # 代码质量检查清单（增强版）
    - r-package-dod-checklist.md # 新增 - R包开发DoD检查清单
    - r-dependency-checklist.md # 新增 - R包依赖管理检查清单

  data:
    - r-best-practices.md      # R语言最佳实践
    - statistical-methods.md   # 统计方法指南
    - visualization-guide.md   # 数据可视化指南

  utils:
    - template-format.md       # 核心工具 - 模板格式规范
    - workflow-management.md   # 核心工具 - 工作流管理

  workflows:
    - data-science-workflow.md # 数据科学工作流

  agent-teams:
    - r-analytics-team.yml     # R分析团队配置

# 依赖关系
dependencies:
  core_bmad_components:
    - template-format          # 模板格式化工具
    - workflow-management      # 工作流管理
    - create-doc              # 文档创建任务
    - execute-checklist       # 检查清单执行任务

  external_requirements:
    - R (>= 4.0.0)
    - RStudio IDE
    - Git版本控制
    - pandoc文档转换器

# 嵌入式知识库
embedded_knowledge:
  - r-best-practices.md       # R编程最佳实践和代码规范
  - statistical-methods.md    # 统计方法和理论指导
  - visualization-guide.md    # 数据可视化设计原则

# 用户需要提供的数据文件（可选）
required_user_data: []

# 安装后消息
post_install_message: |
  🎯 R语言数据分析扩展包安装完成！

  📊 主协调者: Dr. Li Wei (r-data-scientist)
  👥 专业团队: 4名领域专家
  📝 模板库: 3个专业模板
  ✅ 质量保证: 多层次验证系统

  🚀 快速开始:
  1. 运行: npm run agent r-data-scientist
  2. 跟随 Dr. Li Wei 的指导开始数据分析项目
  3. 使用团队协作模式处理复杂分析任务

  📚 嵌入式知识库:
  - R语言最佳实践和编程规范
  - 统计方法和分析指导
  - 数据可视化设计原则

  💡 主要功能:
  - 数据探索和清洗
  - 统计建模和机器学习
  - 专业数据可视化
  - R包开发和部署
  - 科学报告生成

# 版本历史
version_history:
  - version: 2.0.0
    date: '2025-07-01'
    changes:
      - 重大升级：引入严格的质量门控制
      - 新增R包开发DoD检查清单
      - 新增R包依赖管理检查清单
      - 新增R包验证任务
      - 增强r-package-developer代理，支持测试驱动开发
      - 升级代码质量检查清单，强制测试要求
      - 提升测试覆盖率要求至80%以上
  - version: 1.0.0
    date: '2025-07-01'
    changes:
      - 初始版本发布
      - 完整的R数据科学工作流支持
      - 4个专业代理和团队协作模式
      - 标准化的任务和模板库

# 质量指标
quality_metrics:
  completeness: 100%          # 所有必需组件都已实现
  documentation: 98%          # 文档覆盖率
  test_coverage: 85%          # 测试覆盖率要求（强制）
  bmad_compliance: 100%       # BMAD框架合规性
  quality_gates: 100%         # 质量门控制实现
  automation_level: 90%       # 自动化验证水平
