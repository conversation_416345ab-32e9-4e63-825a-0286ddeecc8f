
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(LDheatmap)

data(CEUSNP)
data(CEUDist)

###############################图7-8-1 (a) SNP连锁不平衡图绘制示例1
png(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_a.png",
    width = 4000, height = 4000,res=1000)
par(mar = rep(2,4))
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = grey.colors(20))
old.prompt <- devAskNewPage(ask = TRUE)
# Highlight a certain LD block of interest:
LDheatmap.highlight(MyHeatmap, i = 3, j = 8, col = "black",
fill = "grey",flipOutline=FALSE, crissCross=FALSE)
# Plot a symbol in the center of the pixel which represents LD between
# the fourth and seventh SNPs:
LDheatmap.marks(MyHeatmap,  4,  7,  gp=grid::gpar(cex=2),  pch = "*")
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_a.pdf",
    width = 4, height = 4)
par(mar = rep(2,4))
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = grey.colors(20))
old.prompt <- devAskNewPage(ask = TRUE)
# Highlight a certain LD block of interest:
LDheatmap.highlight(MyHeatmap, i = 3, j = 8, col = "black",
fill = "grey",flipOutline=FALSE, crissCross=FALSE)
# Plot a symbol in the center of the pixel which represents LD between
# the fourth and seventh SNPs:
LDheatmap.marks(MyHeatmap,  4,  7,  gp=grid::gpar(cex=2),  pch = "*")
dev.off()


###############################图7-8-1 (b) SNP连锁不平衡图绘制示例2
png(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_b.png",
    width = 4000, height = 4000,res=1000)
par(mar = rep(2,4))
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = parula(20))
old.prompt <- devAskNewPage(ask = TRUE)
# Plot a symbol in the center of the pixel which represents LD between
# the fourth and seventh SNPs:
LDheatmap.marks(MyHeatmap,  4,  7,  gp=grid::gpar(cex=2,col="red"),  pch = "*")
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_b.pdf",
    width = 4, height = 4)
par(mar = rep(2,4))
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = parula(20))
old.prompt <- devAskNewPage(ask = TRUE)
# Plot a symbol in the center of the pixel which represents LD between
# the fourth and seventh SNPs:
LDheatmap.marks(MyHeatmap,  4,  7,  gp=grid::gpar(cex=2,col="red"),  pch = "*")
dev.off()


###############################图7-8-1 (c) SNP连锁不平衡图绘制示例2
library(grid)


png(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_c.png",
    width = 4000, height = 4000,res=1000)
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = parula(20))
LDheatmap(MyHeatmap, SNP.name = c("rs2283092", "rs6979287"))
grid.edit("symbols", pch = 20, gp = gpar(cex = 1))
grid.edit(gPath("ldheatmap", "heatMap", "title"), gp = gpar(col = "red"))
grid.edit(gPath("ldheatmap", "geneMap","SNPnames"), gp = gpar(cex=1.2))
grid.edit(gPath("ldheatmap", "heatMap", "heatmap"), gp = gpar(col = "white",
                                                             lwd = 2))
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-8-1 使用LDheatmap包绘制的SNP连锁不平衡图示例_c.pdf",
    width = 4, height = 4)
MyHeatmap <- LDheatmap(CEUSNP, genetic.distances = CEUDist,
                       color = parula(20))
LDheatmap(MyHeatmap, SNP.name = c("rs2283092", "rs6979287"))
grid.edit("symbols", pch = 20, gp = gpar(cex = 1))
grid.edit(gPath("ldheatmap", "heatMap", "title"), gp = gpar(col = "red"))
grid.edit(gPath("ldheatmap", "geneMap","SNPnames"), gp = gpar(cex=1.2))
grid.edit(gPath("ldheatmap", "heatMap", "heatmap"), gp = gpar(col = "white",
                                                             lwd = 2))
dev.off()