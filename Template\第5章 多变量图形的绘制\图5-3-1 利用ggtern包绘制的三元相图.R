"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(ggtern)


df = data.frame(x=30,y=40,z=30)
ggtern(df,aes(x,y,z)) + 
   #绘制连接线
   geom_crosshair_tern(size=1,colour="red") +
   geom_point(shape=21,size=8,fill="#459DFF",stroke=1) +
   geom_label(aes(y = y-15,label=sprintf("A=%.1f, B=%.1f, C=%.1f",
                                         x,y,z)),
             family = "serif",colour="red")+
   labs(x="A", y = "B",z="C") +
   annotate("text",x=25,y=50,z = 30,label="S",size=8,
            family = "serif",fontface="italic") +
   annotate("text",x=65,y=8,z = 22,label="a",size=8,
            family = "serif",fontface="italic") +
   annotate("text",x=5,y=50,z = 55,label="b",size=8,
            family = "serif",fontface="italic") +
   annotate("text",x=25,y=80,z = 6,label="c",size=8,
            family = "serif",fontface="italic") +
   #ggplot2::theme_bw() +
   theme_showarrows()+ 
   theme(
        text = element_text(family = "serif",size = 16),
        axis.title = element_text(size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks = element_line(linewidth = .5),
        tern.axis.ticks.length.major=unit(3.0,'mm'),
        tern.axis.ticks.length.minor=unit(1.5,'mm'))
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-1 利用ggtern包绘制的三元相图.png",
         width = 6.2, height = 5.2,dpi=900)
ggtern::ggsave(filename = "\\第5章 多变量图形的绘制\\图5-3-1 利用ggtern包绘制的三元相图.pdf",
        width = 6.2, height = 5.2,device = cairo_pdf)