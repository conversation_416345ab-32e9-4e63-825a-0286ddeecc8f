
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)


#读取数据
paired_data <- read_xlsx("\\第7章 其他类型图的绘制\\Paired_data.xlsx")

df <- paired_data %>% filter(Time!='January'& Group== 'Meditation'& Subject > 40)
	 	 
  

####################################图7-2-1（a）配对图样式

ggpubr::ggpaired(df, x = "Time", y = "Scores",fill = "Time",palette = "aaas",
                 xlab="serif",ylab="Scores",
                 point.size = 3,line.size = 0.2) +
   guides(x = "prism_offset", y = "prism_offset") +
   ggprism::theme_prism(base_size = 16) +
   theme(legend.position = "top")
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-1 配对图样式绘制示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-1 配对图样式绘制示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)


####################################图7-2-1（b）配对图样式（P值）

ggpubr::ggpaired(df, x = "Time", y = "Scores",fill = "Time",palette = "aaas",
                 xlab="serif",ylab="Scores",
                 point.size = 3,line.size = 0.2) +
   stat_compare_means(paired = TRUE) +
   guides(x = "prism_offset", y = "prism_offset") +
   ggprism::theme_prism(base_size = 16) +
   theme(legend.position = "top")
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-1 配对图样式绘制示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第7章 其他类型图的绘制\\图7-2-1 配对图样式绘制示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)


