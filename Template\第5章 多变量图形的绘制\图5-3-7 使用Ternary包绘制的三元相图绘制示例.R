"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(reshape2)

library("Ternary")



####################################图5-3-7（a）使用Ternary包绘制的三元相图示例1

png(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_a.png",
    width = 5000, height = 4500,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_a.pdf",
    width = 4.5, height = 5.5)
	
# Configure plotting area
par(mfrow = c(1, 1), mar = rep(2, 4))

# Initial plot
TernaryPlot(alab = "Redder \u2192", blab = "\u2190 Greener", clab = "Bluer \u2192",
            lab.col = c("red", "darkgreen", "blue"),
            main = "Colours", # Title
            point = "right", lab.cex = 0.8, grid.minor.lines = 0,
            grid.lty = "solid", col = rgb(0.9, 0.9, 0.9), grid.col = "white", 
            axis.col = rgb(0.6, 0.6, 0.6), ticks.col = rgb(0.6, 0.6, 0.6),
            axis.rotate = FALSE,
            padding = 0.08)
# Colour the background:
cols <- TernaryPointValues(rgb)
ColourTernary(cols, spectrum = NULL)

# Add data points
data_points <- list(
  R = c(255, 0, 0), 
  O = c(240, 180, 52),
  Y = c(210, 222, 102),
  G = c(111, 222, 16),
  B = c(25, 160, 243),
  I = c(92, 12, 243),
  V = c(225, 24, 208)
)
AddToTernary(graphics::points, data_points, pch = 21, cex = 2.8, 
             bg = vapply(data_points, 
                         function (x) rgb(x[1], x[2], x[3], 128,
                                          maxColorValue = 255),
                         character(1))
             )
AddToTernary(text, data_points, names(data_points), cex = 0.8, font = 2)
legend("bottomright", 
       legend = c("Red", "Orange", "Yellow", "Green"),
       cex = 0.8, bty = "n", pch = 21, pt.cex = 1.8,
       pt.bg = c(rgb(255,   0,   0, 128, NULL, 255), 
                 rgb(240, 180,  52, 128, NULL, 255),
                 rgb(210, 222, 102, 128, NULL, 255),
                 rgb(111, 222,  16, 128, NULL, 255)),
       )
dev.off()


####################################图5-3-7（b）使用Ternary包绘制的三元相图示例2

png(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_b.png",
    width = 5000, height = 4500,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_b.pdf",
    width = 6, height = 5)	
data(holdridge, holdridgeLifeZonesUp, package = "Ternary")
# Suppress plot margins
par(mar = c(0, 0, 0, 0))

# Create blank Holdridge plot
HoldridgePlot(hex.labels = holdridgeLifeZonesUp)
HoldridgeBelts()

# Plot data, shaded by latitude
HoldridgePoints(holdridge$PET, holdridge$Precipitation,
                col = hcl.colors(91)[abs(holdridge$Latitude) + 1],
                lwd = 2)

# Add legend to interpret shading
PlotTools::SpectrumLegend(
  "topright", bty = "n", # No box
  horiz = TRUE, # Horizontal
  x.intersp = -0.5, # Squeeze in X direction
  legend = paste0(seq(0, 90, 15), "°"),
  palette = hcl.colors(91),
  title = "Latitude"
)
dev.off()


####################################图5-3-7（c）使用Ternary包绘制的三元相图示例3

png(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_c.png",
    width = 5000, height = 4500,res=1000)
pdf(file="\\第5章 多变量图形的绘制\\图5-3-7 使用Ternary包绘制的三元相图绘制示例_c.pdf",
    width = 6, height = 5)	
	
par(mar = rep(0.2, 4))

FunctionToContour <- function(a, b, c) {
  a - c + (4 * a * b) + (27 * a * b * c)
}

values <- TernaryPointValues(FunctionToContour, resolution = 24L)
TernaryPlot(alab = "a", blab = "b", clab = "c",
            # Place an opaque fill behind grid lines:
            panel.first = ColourTernary(values, spectrum = hcl.colors(256)))
TernaryContour(FunctionToContour, resolution = 36L, legend = TRUE, bty = "n")
dev.off()
