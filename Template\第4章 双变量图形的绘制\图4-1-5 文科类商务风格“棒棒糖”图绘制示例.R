"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(bbplot)


#构建数据
library(stringr) # string manipulation
dat_wide = tibble::tribble(
  ~Country,   ~Y2010,   ~Y2020,
  'countryA',  71.5, 101.4,
  'countryB',  74.4, 102.9,
  'countryC',  60.9, 135.2,
  'countryD',   127, 136.2,
  'countryE',  58.5, 137.1,
  'countryF', 170.9, 158.8,
  'countryG', 106.8,   169,
  'countryH', 123.6, 170.9,
  'countryI', 208.5, 199.8,
  'countryJ',   181, 216.7,
  'countryK', 185.4,   222,
  'countryL', 202.7,   236,
  'countryM', 173.8, 239.9,
  'countryN', 193.1, 242.3,
  'countryO', 173.8, 260.6,
  'countryP', 221.1, 269.8
)

dat_long = dat_wide %>% 
  gather(key = 'Year', value = 'Energy_Value', Y2010:Y2020) %>% 
  mutate(Year = str_replace(Year, 'Y', ''))										

ggplot() +
  geom_segment(data = dat_wide, aes(x= Y2010, xend = Y2020, 
                   y= Country,yend = Country),
                   linewidth = 1.5, colour = '#D0D0D0') +
  geom_point(data = dat_long,aes(x = Energy_Value, 
             y= Country, fill = Year),shape=21,size = 5.5) +
  labs(title = 'Energy Values in selected countries \nand regions',
       x = NULL, y = NULL) +
  scale_fill_manual(values = c("#1380A1", "#FAAB18")) +
  bbc_style() +
  theme(
        axis.text = element_text(size = 15),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-5 文科类商务风格“棒棒糖”图绘制示例.png",
       width =8.5, height = 5.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-5 文科类商务风格“棒棒糖”图绘制示例.pdf",
       width =8.5, height = 5.5,device = cairo_pdf)