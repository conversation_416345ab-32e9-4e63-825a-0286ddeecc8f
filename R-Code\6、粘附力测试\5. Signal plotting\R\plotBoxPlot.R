# ==========================================
# 文件名：plotBoxPlot.R
# 主要功能：绘制通用箱线图（可移植到其他项目）
# 依赖文件：无
# 主要函数：
#   - plotBoxPlot(): 绘制单组或双组数据对比箱线图
# 输入参数：一组或两组数值数据，组标签
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================

# 加载必要的包
library(ggplot2)

# 跨平台字体设置 - 确保Times New Roman在PDF中正确显示
if (.Platform$OS.type == "windows") {
  # Windows系统字体设置
  windowsFonts(times = windowsFont("Times New Roman"))
} else {
  # 非Windows系统的字体设置
  # 在Linux/Mac系统中，通常使用Liberation Serif或其他serif字体作为替代
  if (capabilities("cairo")) {
    # 如果支持cairo，可以直接使用Times New Roman
    windowsFonts(times = "Times New Roman")
  }
}

#' 绘制通用箱线图
#' 可移植到其他项目使用的通用箱线图绘制函数
#'
#' @param group1_data 第一组数值数据
#' @param group2_data 第二组数值数据（可选，为NULL时只显示单组数据）
#' @param group1_label 第一组标签
#' @param group2_label 第二组标签（可选）
#' @param show_points 是否显示原始数据点（默认TRUE）
#' @param show_mean 是否显示均值点（默认TRUE）
#' @param show_notch 是否显示缺口箱线图（默认FALSE）
#' @param color_palette 颜色方案选择：1-默认蓝红，2-科研期刊风格，3-彩虹色系（默认1）
#' @param line_width 箱线图线条粗细，包括中位数线（默认0.5）
#' @return ggplot对象
plotBoxPlot <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                       group2_data = NULL,
                       group1_label = "Group 1",
                       group2_label = "Group 2",
                       show_points = TRUE,
                       show_mean = TRUE,
                       show_notch = FALSE,
                       color_palette = 1,
                       line_width = 0.5) {

  # 定义颜色方案
  if (color_palette == 1) {
    # 默认蓝红配色
    colors <- c(rgb(0, 0.45, 0.74), rgb(0.85, 0.33, 0.1))
  } else if (color_palette == 2) {
    # 科研期刊风格配色
    colors <- c("#2FBE8F", "#459DFF", "#FF5B9B", "#FFCC37")
  } else if (color_palette == 3) {
    # 彩虹色系配色
    colors <- c("#E31A1C", "#1F78B4", "#33A02C", "#FF7F00")
  } else {
    # 默认配色
    colors <- c(rgb(0, 0.45, 0.74), rgb(0.85, 0.33, 0.1))
  }

  # 判断是单组数据还是双组数据
  if (is.null(group2_data)) {
    # 单组数据：显示单个组的箱线图
    plot_data <- data.frame(
      group = factor(rep(group1_label, length(group1_data))),
      value = group1_data
    )

    # 计算统计量用于显示
    mean_val <- mean(group1_data)
    median_val <- median(group1_data)
    sd_val <- sd(group1_data)

  } else {
    # 双组数据：显示两组的箱线图比较
    plot_data <- data.frame(
      group = factor(c(rep(group1_label, length(group1_data)),
                      rep(group2_label, length(group2_data))),
                    levels = c(group1_label, group2_label)),
      value = c(group1_data, group2_data)
    )
  }

  # 创建基础箱线图
  p <- ggplot(plot_data, aes(x = group, y = value))

  # 添加误差带（箱线图的须线，使用stat_boxplot方法）
  p <- p + stat_boxplot(geom = "errorbar", width = 0.4, linewidth = 0.5)

  # 添加箱线图
  if (is.null(group2_data)) {
    # 单组数据：使用单一颜色
    p <- p + geom_boxplot(fill = colors[1],
                         color = "black",
                         linewidth = line_width,
                         width = 0.6,
                         alpha = 0.7,
                         notch = show_notch,
                         outlier.shape = 21,
                         outlier.fill = colors[1],
                         outlier.color = "black",
                         outlier.size = 2)
  } else {
    # 双组数据：使用不同颜色
    p <- p + geom_boxplot(aes(fill = group),
                         color = "black",
                         linewidth = line_width,
                         width = 0.6,
                         alpha = 0.7,
                         notch = show_notch,
                         outlier.shape = 21,
                         outlier.color = "black",
                         outlier.size = 2) +
      # 设置颜色方案
      scale_fill_manual(values = colors[1:2])
  }
  
  # 添加原始数据点（如果需要）
  if (show_points) {
    if (is.null(group2_data)) {
      # 单组数据：添加抖动点
      p <- p + geom_jitter(width = 0.2, 
                          size = 2.5, 
                          color = "black",
                          fill = "white",
                          shape = 21,
                          stroke = 1)
    } else {
      # 双组数据：添加抖动点
      p <- p + geom_jitter(aes(fill = group),
                          width = 0.2, 
                          size = 2.5, 
                          color = "black",
                          shape = 21,
                          stroke = 1,
                          alpha = 0.8)
    }
  }
  
  # 添加均值点（如果需要）
  if (show_mean) {
    if (is.null(group2_data)) {
      # 单组数据：添加均值点
      mean_data <- data.frame(group = factor(group1_label), 
                             mean_value = mean(group1_data))
      p <- p + geom_point(data = mean_data, 
                         aes(x = group, y = mean_value),
                         size = 4, 
                         color = "red", 
                         shape = 18)  # 菱形
    } else {
      # 双组数据：添加均值点
      mean_data <- data.frame(
        group = factor(c(group1_label, group2_label),
                      levels = c(group1_label, group2_label)),
        mean_value = c(mean(group1_data), mean(group2_data))
      )
      p <- p + geom_point(data = mean_data, 
                         aes(x = group, y = mean_value),
                         size = 4, 
                         color = "red", 
                         shape = 18)  # 菱形
    }
  }

  # 设置坐标轴
  if (is.null(group2_data)) {
    # 单组数据：设置单个组的坐标轴
    max_value <- max(group1_data, na.rm = TRUE)
    p <- p +
      scale_x_discrete(expand = expansion(mult = c(0.4, 0.4))) +  # 增加左右间距
      scale_y_continuous(limits = c(0, max_value * 1.2),
                        expand = expansion(mult = c(0, 0.05))) +
      # 设置标签
      labs(x = "Test Group",
           y = "Value")
  } else {
    # 双组数据：设置两组比较的坐标轴
    max_value <- max(c(group1_data, group2_data), na.rm = TRUE)
    p <- p +
      scale_x_discrete(expand = expansion(mult = c(0.4, 0.4))) +  # 增加左右间距
      scale_y_continuous(limits = c(0, max_value * 1.2),
                        expand = expansion(mult = c(0, 0.05))) +
      # 设置标签
      labs(x = "Group",
           y = "Value")
  }

  # 应用通用主题样式，与其他图表保持一致
  p <- p +
    theme_bw() +
    theme(
      # 背景设置 - 统一白色背景
      plot.background = element_rect(fill = "white", color = NA),
      panel.background = element_rect(fill = "white", color = NA),
      
      # 全局字体设置 - 统一Times New Roman，确保PDF兼容性
      text = element_text(family = "times", face = "bold", size = 18, color = "black"),

      # 坐标轴文字设置 - 参考模板样式
      axis.text.x = element_text(angle = 0, hjust = 0.5, size = 15, color = "black", face = "bold"),
      axis.text.y = element_text(size = 15, color = "black", face = "bold"),

      # 坐标轴标题设置 - 参考模板样式，添加间距控制
      axis.title.x = element_text(size = 18, face = "bold", color = "black",
                                  margin = margin(t = 12)),  # 增加与X轴的距离
      axis.title.y = element_text(size = 18, face = "bold", color = "black",
                                  margin = margin(r = 12)),  # 增加与Y轴的距离

      # 网格线和边框设置 - 完整边框，无网格线
      panel.grid = element_blank(),  # 必须移除所有网格线
      axis.line = element_blank(),   # 移除轴线，避免与边框重复
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),

      # 刻度线设置 - 朝内显示
      axis.ticks = element_line(color = "black", linewidth = 0.5),
      axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内

      # 边距设置 - 标准边距
      # plot.margin = margin(10, 10, 0, 10),  # 上右下左边距

      # 图例设置
      legend.position = "none"
    )

  return(p)
}

#' 为粘附力测试专门设计的箱线图函数
#' 在粘附力测试项目中使用时，可以调用此函数
#'
#' @param group1_data 第一组剥离能量数据
#' @param group2_data 第二组剥离能量数据（可选）
#' @param group1_label 第一组标签（默认为"2733+Silbione"）
#' @param group2_label 第二组标签（默认为"2733"）
#' @param show_points 是否显示原始数据点（默认FALSE）
#' @param show_mean 是否显示均值点（默认TRUE）
#' @param show_notch 是否显示缺口箱线图（默认FALSE）
#' @param color_palette 颜色方案选择（默认1）
#' @param line_width 箱线图线条粗细（默认0.5）
#' @return ggplot对象
plotPeelingEnergyBoxPlot <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                                    group2_data = NULL,
                                    group1_label = "2733+Silbione",
                                    group2_label = "2733",
                                    show_points = FALSE,
                                    show_mean = FALSE,
                                    show_notch = FALSE,
                                    color_palette = 2,
                                    line_width = 0.3) {

  # 调用通用的箱线图函数
  p <- plotBoxPlot(group1_data, group2_data, group1_label, group2_label,
                   show_points, show_mean, show_notch, color_palette, line_width)

  # 为粘附力测试添加特定的Y轴标签
  p <- p + labs(y = expression(bold(paste("Peeling Energy (J/m"^"2", ")"))))

  # 如果是双组数据，使用特定的X轴标签
  if (!is.null(group2_data)) {
    p <- p + labs(x = "Adhesive substrate")
  } else {
    p <- p + labs(x = "Test Group")
  }

  # 添加轴标签间距控制
  p <- p + theme(
    # X轴标题距离控制 - 增加与X轴的距离
    axis.title.x = element_text(size = 18, face = "bold", color = "black",
                                margin = margin(t = 15)),  # t=top，增加上边距15pt
    # Y轴标题距离控制 - 增加与Y轴的距离
    axis.title.y = element_text(size = 18, face = "bold", color = "black",
                                margin = margin(r = 15))   # r=right，增加右边距15pt
  )

  return(p)
}

# ==========================================
# PDF保存说明
# ==========================================
# 为了确保Times New Roman字体在PDF中正确显示，请使用以下方式保存：
#
# 方法1：使用cairo_pdf设备（推荐）
ggsave("filename.pdf", plot = your_plot, width = 4.5, height = 4, device = cairo_pdf)
#
# 方法2：使用pdf设备并指定字体族
# pdf("filename.pdf", width = 4.5, height = 4, family = "Times")
# print(your_plot)
# dev.off()
#
# 注意：cairo_pdf设备对字体的支持更好，建议优先使用
# ==========================================
