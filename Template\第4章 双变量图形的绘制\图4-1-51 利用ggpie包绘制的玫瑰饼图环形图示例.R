"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(cowplot)
library(ggpubr)
library(readxl)
library(scales)

library(ggpie)


#构建数据集

head(diamonds)
   
# pie plot
p1=ggrosepie(diamonds, group_key = "color", count_type = "full", label_info = "all",
             tick_break = c(3000,5000,7000,11000), donut_frac=NULL,
             border_size=0.5)
# donut plot
p2=ggrosepie(diamonds, group_key = "color", count_type = "full", label_info = "all",
             tick_break = c(3000,5000,7000,11000), donut_frac=0.3,donut_label_size=3,
             border_size=0.5)
cowplot::plot_grid(p1,p2)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-51 利用ggpie包绘制的玫瑰饼图/环形图示例.png",
       width =10, height = 7.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-51 利用ggpie包绘制的玫瑰饼图/环形图示例.pdf",
       width =10, height = 7.5,device = cairo_pdf)