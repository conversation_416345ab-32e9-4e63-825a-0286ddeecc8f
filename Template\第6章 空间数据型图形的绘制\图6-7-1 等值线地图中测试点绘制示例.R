"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""


library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(gstat)
library(pals)
library(readxl)
sf_use_s2(FALSE)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")

point_data <- readr::read_csv("\\第6章 空间数据型图形的绘制\\Virtual_huouse.csv")


####################################图6-7-1（a）样例数据点分布（透明度设置

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  geom_point(data=point_data,aes(x=long,y=lat),size=0.5,alpha=0.5) +
  #scale_fill_manual(values = c("#458B74","#CDCD00","#F5DEB3")) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude",fill="Typology Type")+
  theme_classic() +
  theme(legend.position = c(0.95,0.8),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 8,hjust = 0.5),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.key.size = unit(0.5, 'cm'),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-1 等值线地图中测试点绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-1 等值线地图中测试点绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-7-1（b）样例数据点分布（数值映射颜色）

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8) +
  geom_point(data=point_data,aes(x=long,y=lat,color=value),size=0.5) +
  scale_color_gradientn(colours = parula(100),breaks=seq(500,2500,500)) +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  theme(legend.position = c(1,0.5),
        legend.text = element_text(size = 8),
        legend.title = element_text(size = 12,hjust = 0.5),
        legend.key.height=unit(0.7, "cm"),
        legend.key.width=unit(0.5, "cm"),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 20, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-1 等值线地图中测试点绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-7-1 等值线地图中测试点绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

	   
	   