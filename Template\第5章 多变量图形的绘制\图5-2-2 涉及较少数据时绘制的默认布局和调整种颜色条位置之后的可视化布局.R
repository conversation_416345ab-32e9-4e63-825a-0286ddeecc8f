"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


#构建数据
cor_line_01 <- readxl::read_excel("\\第5章 多变量图形的绘制\\散点图样例数据2.xlsx",
                               sheet = "data03")

											  												 												  
####################################图5-2-2（a）较少数据默认多变量相关性散点图绘制示例

ggplot(data = cor_line_01,aes(x=x,y=y)) +
  geom_errorbar(aes(ymin = y-y_err, ymax = y+y_err),
               colour="gray40",linewidth=0.2,width = 0) +
  geom_errorbarh(aes(xmin = x-x_err, xmax = x+x_err),
               colour="gray40",linewidth=0.2,height = 0) +
  geom_point(aes(fill=values),shape=21,size=5) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(intercept=0, slope=1),linetype=5,alpha=1, size=.5) + 
  scale_fill_gradientn(colours = parula(100),breaks=seq(0.1,0.9,0.1),
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour = "black",
                           title = "Z Values",
                           title.hjust = 0.5,
                           title.position = "right",
                           barwidth = 1, barheight = 14,
                       )) +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 60),
                     breaks = seq(0,60,10)) +
  scale_x_continuous(expand = c(0,0),limits = c(0,40),
                     breaks = seq(0,40,10)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(legend.title = element_text(size = 14, angle = 90),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-2 涉及较少数据时绘制的默认布局和调整种颜色条位置之后的可视化布局_a.png",
       width =5.8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-2 涉及较少数据时绘制的默认布局和调整种颜色条位置之后的可视化布局_a.pdf",
       width =5.8, height = 4.5,device = cairo_pdf)
	   
####################################图5-2-2（b）较少数据多变量相关性散点图图层调整绘制示例

ggplot(data = cor_line_01,aes(x=x,y=y)) +
  geom_errorbar(aes(ymin = y-y_err, ymax = y+y_err),
               colour="gray40",linewidth=0.2,width = 0) +
  geom_errorbarh(aes(xmin = x-x_err, xmax = x+x_err),
               colour="gray40",linewidth=0.2,height = 0) +
  geom_point(aes(fill=values),shape=21,size=5) +
  #绘制对角线:最佳拟合线
  geom_abline(aes(intercept=0, slope=1),linetype=5,alpha=1, size=.5) + 
  scale_fill_gradientn(colours = parula(100),breaks=seq(0.2,0.8,0.2),
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour = "black",
                                              label.position="left",
                           title = "Colorbar Label",
                           title.hjust = 0.5,
                           title.position = "right",
                           barwidth = 1.2, barheight = 8,
                       )) +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 60),
                     breaks = seq(0,60,10)) +
  scale_x_continuous(expand = c(0,0),limits = c(0,40),
                     breaks = seq(0,40,10)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(legend.position = c(0.15,0.65),
        legend.title = element_text(size = 14, angle = 90),
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-2 涉及较少数据时绘制的默认布局和调整种颜色条位置之后的可视化布局_b.png",
       width =5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-2 涉及较少数据时绘制的默认布局和调整种颜色条位置之后的可视化布局_b.pdf",
       width =5, height = 4.5,device = cairo_pdf)
   