
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)
library(ggprism)

library(plotrix)

set.seed(10)
#构建模型参考值
ref<-rnorm(30,sd=2)
#构建模型结果值
model1<-ref+rnorm(30)/2 
model2<-ref+rnorm(30) 
model3<-ref+rnorm(30)*1.1 
model4<-ref+rnorm(30)*1.5 

#为每一个模型构建偏差
bias1 <- 0.5
bias2 <- -1
bias3 <- 0.9
bias4 <- -0.25


####################################图7-5-2（a）泰勒图颜色映射绘制示例1
png(file="\\第7章 其他类型图的绘制\\图7-5-2 颜色映射泰勒图绘制示例_a.png",
    family ="serif",width = 5000, height = 5000,res=1200)
	
	
par(family = "serif",mar = rep(1,4))

# 构建颜色值
num_cols <- 8 # 偏差的颜色数量
cols <- brewer.pal(num_cols,'Spectral') # 制作调色板
# 制作颜色断点的矢量
# 断点定义每种颜色的区域
min_bias <- -1 # minimum bias
max_bias <- 1 # maximum bias
col_breaks <- seq(min_bias,max_bias,(max_bias - min_bias)/(num_cols))

#根据偏向分配颜色
# 根据偏差值分配颜色指数
col1 <- cols[max(which( col_breaks <= bias1))]
col2 <- cols[max(which( col_breaks <= bias2))]
col3 <- cols[max(which( col_breaks <= bias3))]
col4 <- cols[max(which( col_breaks <= bias4))]

# 显示图表并为每个模型添加点数
# 使用为每个模型分配的颜色来表示该模型的点
par(family = "serif",mar = rep(1,4))
taylor.diagram(ref,model1,col=col1,pch = 15,pcex = 2,main = NULL,sd.arcs=TRUE)
taylor.diagram(ref,model2,col=col2,pch = 15,pcex = 2,add=T)
taylor.diagram(ref,model3,col=col3,pch = 15,pcex = 2,add=T)
taylor.diagram(ref,model4,col=col4,pch = 15,pcex = 2,add=T)

# adding color bar
color.legend(xl = 3.1,yb = 1,xr = 3.3,yt = 2.6, # coordinates
             (col_breaks[1:(length(col_breaks)-1)]+col_breaks[2:length(col_breaks)])/2, # legend values (mean of color value)
             rect.col=cols, # colors
             cex=1,
             gradient='y' # vertical gradient
             )
dev.off()
	   
####################################图7-5-2（b）泰勒图颜色映射绘制示例2 
set.seed(10)
#构建模型参考值
ref<-rnorm(30,sd=2)
#构建模型结果值
model1<-ref+rnorm(30)/2 
model2<-ref+rnorm(30) 
model3<-ref+rnorm(30)*1.1 
model4<-ref+rnorm(30)*1.5 

#为每一个模型构建偏差
bias1 <- 0.5
bias2 <- -1
bias3 <- 0.9
bias4 <- -0.25

# 构建颜色值
num_cols <- 8 # 偏差的颜色数量
cols <- parula(num_cols) # 制作调色板
# 制作颜色断点的矢量
# 断点定义每种颜色的区域
min_bias <- -1 # minimum bias
max_bias <- 1 # maximum bias
col_breaks <- seq(min_bias,max_bias,(max_bias - min_bias)/(num_cols))

#根据偏向分配颜色
# 根据偏差值分配颜色指数
col1 <- cols[max(which( col_breaks <= bias1))]
col2 <- cols[max(which( col_breaks <= bias2))]
col3 <- cols[max(which( col_breaks <= bias3))]
col4 <- cols[max(which( col_breaks <= bias4))]

# 显示图表并为每个模型添加点数
# 使用为每个模型分配的颜色来表示该模型的点

png(file="\\第7章 其他类型图的绘制\\图7-5-2 颜色映射泰勒图绘制示例_b.png",
    family ="serif",width = 5000, height = 5000,res=1200)

pdf(file="\\第7章 其他类型图的绘制\\图7-5-2 颜色映射泰勒图绘制示例_b.pdf",
    family ="serif",width = 5, height = 5)

par(family = "serif",mar = rep(1,4))
taylor.diagram(ref,model1,col=col1,pcex = 2,main = NULL,sd.arcs=TRUE)
taylor.diagram(ref,model2,col=col2,pcex = 2,add=T)
taylor.diagram(ref,model3,col=col3,pcex = 2,add=T)
taylor.diagram(ref,model4,col=col4,pcex = 2,add=T)

# adding color bar
color.legend(xl = 3.1,yb = 1,xr = 3.3,yt = 2.6, # coordinates
             (col_breaks[1:(length(col_breaks)-1)]+col_breaks[2:length(col_breaks)])/2, # legend values (mean of color value)
             rect.col=cols, # colors
             cex=1,
             gradient='y' # vertical gradient
             )

dev.off()
