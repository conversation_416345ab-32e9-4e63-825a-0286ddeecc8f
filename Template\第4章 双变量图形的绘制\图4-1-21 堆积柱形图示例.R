"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggprism)

#构建数据集
bar_data02 = data.frame(labels = c('one', 'two', 'three', 'four', 'five'),
                        type01 = c(10, 8, 5, 10, 2),
                        type02 = c(13, 10,7, 4, 10),
                        type03 = c(5, 7, 10,6, 8))
#数据转换
bar_data02_long <- bar_data02 %>% tidyr::pivot_longer(cols = starts_with("type"),
                            names_to = "Type", values_to = "Values")
							
#改变绘图属性顺序
bar_data02_long$labels <- factor(bar_data02_long$labels, 
                        levels=c("one","two","three","four","five"))										

####################################图4-1-21（a）灰色系堆积柱形图示例

grey_color_palette  <- c("#d0d0d0","#a8a8a8","#808080")

ggplot(data = bar_data02_long, aes(x = labels,y = Values,fill=Type)) +
  geom_bar(stat = "identity",width= 0.6,position="stack",
           color="black",linewidth=0.3) +
  #添加对应数值文本
  geom_text(aes(label=Values),position = position_stack(vjust = 0.5),
            family="serif",fontface="bold",size=5) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,30),
                     breaks = seq(0,30,5)) +
  scale_fill_manual(values = grey_color_palette) +
  labs(x="Name",y="Values") +
  guides(fill = guide_legend(override.aes = list(size = 2)))+
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                 hjust = 0.3),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-21 堆积柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-21 堆积柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-21（b）NEJM色系堆积柱形图示例

# 降序则使fct_reorder(name,desc(value))
ggplot(data = bar_data02_long, aes(x = labels,y = Values,fill=Type)) +
  geom_bar(stat = "identity",width= 0.6,position="stack",
           color="black",linewidth=0.3) +
  #添加对应数值文本
  geom_text(aes(label=Values),position = position_stack(vjust = 0.5),
            family="serif",fontface="bold",size=5) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,30),
                     breaks = seq(0,30,5)) +
  ggsci::scale_fill_nejm() +
  labs(x="Name",y="Values") +
  guides(fill = guide_legend(override.aes = list(size = 2)))+
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                 hjust = 0.3),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-21 堆积柱形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-21 堆积柱形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)