% QUICK_PLOT - 快速绘制MVTR柱状图脚本
% 直接运行此脚本来绘制example_data.csv的柱状图

clear; clc; close all;

% 设置文件路径
csv_file = '../4、Code/example_data.csv';

% 检查文件是否存在
if ~exist(csv_file, 'file')
    error('找不到文件: %s', csv_file);
end

% 读取数据
data = readtable(csv_file, 'Encoding', 'UTF-8');
materials = data{:, 1};  % 第一列：材料名称
values = data{:, end};   % 最后一列：数值

% 处理材料名称
if isnumeric(materials)
    materials = cellstr(string(materials));
elseif ~iscell(materials)
    materials = cellstr(materials);
end

% 创建柱状图
figure('Position', [100, 100, 800, 600]);
bar_colors = [0.2, 0.6, 0.8];  % 蓝色
bar(values, 'FaceColor', bar_colors, 'EdgeColor', [0.1, 0.3, 0.5], 'LineWidth', 1.5);

% 设置标签和标题
set(gca, 'XTickLabel', materials);
set(gca, 'XTick', 1:length(materials));
xtickangle(45);

title('材料MVTR测试结果', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('材料类型', 'FontSize', 14);
ylabel('重量 (g)', 'FontSize', 14);

% 美化图形
grid on;
set(gca, 'FontSize', 12);
set(gca, 'Box', 'on');

% 在柱子上显示数值
for i = 1:length(values)
    text(i, values(i) + max(values)*0.01, sprintf('%.2f', values(i)), ...
        'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
        'FontSize', 10, 'FontWeight', 'bold');
end

% 调整Y轴范围
ylim([min(values)*0.95, max(values)*1.1]);

fprintf('柱状图绘制完成！\n');
fprintf('材料数量: %d\n', length(materials));
fprintf('数值范围: %.2f - %.2f\n', min(values), max(values));
