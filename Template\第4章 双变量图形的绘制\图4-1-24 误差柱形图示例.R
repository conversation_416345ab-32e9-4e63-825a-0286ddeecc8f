"""
测试时间：2024年05月10日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggprism)

library(rstatix)

#构建数据集

df <- data.frame(
  name=c("A","B","C","D","E") ,  
  value=c(5,8,10,12,20))

# 计算standard error：SE
se  <-  sd(df$value) / sqrt(length(df$value))
								

####################################图4-1-24（a）单数据误差柱形图示例
ggplot(data = df,aes(x = name,y = value)) +
  geom_bar(stat="identity",fill="gray",width = 0.65,colour="black",
          linewidth=0.4) +
  geom_errorbar(aes(ymin=value-se,ymax=value+se),width=0.2,linewidth=.3)+
  scale_y_continuous(expand = c(0, 0),limits = c(0,25),
                     breaks = seq(0,25,5)) +
  labs(x="Name",y="Values") +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

err_bar <- iris %>% select(Species, Sepal.Length) 

err_bar_stat <- err_bar %>% group_by(Species) %>% 
           rstatix::get_summary_stats(type = "common")
	   
####################################图4-1-24（）SD误差柱形图示例

# SD
ggplot(data = err_bar_stat,aes(x = Species,y = mean)) +
  geom_bar(aes(fill=Species),stat="identity",width=0.65,colour="black",
          linewidth=0.4) +
  geom_errorbar(aes(ymin=mean-sd, ymax=mean+sd),width=0.2,linewidth=0.3) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
  ggsci::scale_fill_jco() +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-24（）CI误差柱形图示例	

# CI
ggplot(data = err_bar_stat,aes(x = Species,y = mean)) +
  geom_bar(aes(fill=Species),stat="identity",width=0.65,colour="black",
          linewidth=0.4) +
  geom_errorbar(aes(ymin=mean-ci, ymax=mean+ci),width=0.2,linewidth=0.3) +
  scale_y_continuous(expand = c(0, 0),limits = c(0,8),
                     breaks = seq(0,8,2)) +
  ggsci::scale_fill_jco() +
  theme_classic() +
  theme(legend.position="top",
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-24 误差柱形图示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)   