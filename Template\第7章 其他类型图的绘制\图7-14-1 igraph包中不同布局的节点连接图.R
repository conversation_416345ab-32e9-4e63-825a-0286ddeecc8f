
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(igraph)
library(ggraph)

# Create data
data <- matrix(sample(0:1, 400, replace=TRUE, prob=c(0.8,0.2)), nrow=20)

network <- graph_from_adjacency_matrix(data , mode='undirected', diag=F )


# When ploting, we can use different layouts:
png(file="\\第7章 其他类型图的绘制\\图7-14-1 igraph包中不同布局的节点连接图.png",
    width = 4000, height = 6000,res=1000)
par(mfrow=c(3,2), mar=rep(1,4))
plot(network, layout=layout.drl, main="drl")
plot(network, layout=layout.circle, main="circle")
plot(network, layout=layout.grid, main="grid")
plot(network, layout=layout.sphere, main="sphere")
plot(network, layout=layout.graphopt, main="graphopt")
plot(network, layout=layout.gem, main="gem")
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-14-1 igraph包中不同布局的节点连接图.pdf",
    width = 4, height = 6)
par(mfrow=c(3,2), mar=rep(1,4))
plot(network, layout=layout.drl, main="drl")
plot(network, layout=layout.circle, main="circle")
plot(network, layout=layout.grid, main="grid")
plot(network, layout=layout.sphere, main="sphere")
plot(network, layout=layout.graphopt, main="graphopt")
plot(network, layout=layout.gem, main="gem")
dev.off()