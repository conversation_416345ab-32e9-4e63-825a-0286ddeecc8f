"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
file <- "\\第4章 双变量图形的绘制\\散点图样例数据.xlsx"
scatter_data <- read_excel(file)

num <- nrow(scatter_data)
											  
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\mtcars.xlsx"
mtcars <- read_excel(file)												  
num2 <- nrow(mtcars)												  
####################################图4-2-12（a）使用ggpubr绘制的正相关散点图示例

ggplot(data = scatter_data,aes(x = values,y = pred_values)) +
  geom_point(shape=22,size=2.5,fill="black") +
  stat_smooth(method = "lm", se=FALSE, color="red", formula = y ~ x) +
#   #绘制对角线:最佳拟合线
#   geom_abline(slope = 1,intercept = 0,linetype="longdash",
#               linewidth=0.7) +
  ggpubr::stat_regline_equation(label.x = .1,label.y = 1.6,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(..rr.label.., ..p.label.., sep = "~`,`~")),
                   label.x = .1, label.y = 1.4,p.accuracy = 0.001,
                   size=6,family='serif',fontface='bold') +
  annotate("text",x=0.1,y=1.2,label=paste("N = ",num),size=6,
            family='serif',hjust = 0) +
  #修改坐标轴刻度
  scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0, 0)) +
  scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0, 0)) +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-12（b）使用ggpubr绘制的负相关散点图示例

ggplot(data = mtcars,aes(x = wt,y = mpg)) +
  geom_point(shape=22,size=2.5,fill="black") +
  stat_smooth(method = "lm", se=FALSE, color="red", formula = y ~ x) +
  ggpubr::stat_regline_equation(label.x = 3,label.y = 33,size=6,
                                family="serif",fontface="bold") +
  ggpubr::stat_cor(aes(label = paste(..rr.label.., ..p.label.., sep = "~`,`~")),
                   label.x = 3, label.y = 30,p.accuracy = 0.001,
                   size=6,family='serif',fontface='bold') +
  annotate("text",x=3,y=27,label=paste("N = ",num2),size=6,
            family='serif',hjust = 0) +
  #修改坐标轴刻度
#   scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0, 0)) +
#   scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0, 0)) +
  labs(x="Variable 01",y="Variable 02")+
theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-12（c）使用ggpubr绘制的负相关散点图示例
ggplot(data = scatter_data,aes(x = values,y = pred_values)) +
  geom_point(shape=21,size=2.5,fill="black") +
  ggpmisc::stat_poly_line(formula = y ~ x, color = "red",se = FALSE) +
  ggpmisc::stat_correlation(mapping = use_label(c("R","P", "n")),
                           family = "serif",fontface='bold',size=6) +
  ggpmisc::stat_poly_eq(mapping = use_label(c("eq"),sep = "*\", \"*"),
                        formula = y ~ x,family = "serif",fontface='bold',
                        size=6,label.x = .05, label.y = .85) +
  ggpmisc::stat_poly_eq(mapping = use_label("adj.R2"),
                        formula = y ~ x,family = "serif",fontface='bold',
                        size=6,label.x = .05, label.y = .75) +

  ggpmisc::stat_poly_eq(mapping = use_label(c("AIC","BIC")), label.x = "right", label.y = "bottom", 
                       family = "serif",fontface='bold',size=6,formula = y ~ x,
                       ) +
  #修改坐标轴刻度
  scale_x_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  scale_y_continuous(limits = c(0,1.8),breaks = seq(0,1.8,0.2),expand = c(0,0)) +
  labs(x="X Label", y = "Y Label") +
  #添加不同线图例
  scale_colour_manual(name="", values=c("black","red")) +
  guides(colour = guide_legend(override.aes = list(alpha = 0)))+
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-2-12（d）使用ggpubr绘制的负相关散点图示例	   
ggplot(data = mtcars,aes(x = wt,y = mpg)) +
  geom_point(shape=21,size=2.5,fill="black") +
  ggpmisc::stat_poly_line(formula = y ~ x, color = "red",se = FALSE) +
  ggpmisc::stat_correlation(mapping = use_label(c("R","P", "n")),
                           family = "serif",fontface='bold',size=6,
                           label.x = .25, label.y = .95) +
  ggpmisc::stat_poly_eq(mapping = use_label(c("eq"),sep = "*\", \"*"),
                        formula = y ~ x,family = "serif",fontface='bold',
                        size=6,label.x = .25, label.y = .85) +
  ggpmisc::stat_poly_eq(mapping = use_label("adj.R2"),
                        formula = y ~ x,family = "serif",fontface='bold',
                        size=6,label.x = .25, label.y = .75) +

  ggpmisc::stat_poly_eq(mapping = use_label(c("AIC","BIC")), label.x = "left", label.y = "bottom", 
                       family = "serif",fontface='bold',size=6,formula = y ~ x,
                       ) +
  labs(x="X Label", y = "Y Label") +
  #添加不同线图例
  scale_colour_manual(name="", values=c("black","red")) +
  guides(colour = guide_legend(override.aes = list(alpha = 0)))+
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(size = .4),
        axis.line = element_line(size = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-12 正、负相关散点图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   
   