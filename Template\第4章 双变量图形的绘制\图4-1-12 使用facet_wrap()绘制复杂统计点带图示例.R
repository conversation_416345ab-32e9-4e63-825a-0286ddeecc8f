"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
strip_data_02 <- read_excel("\\第4章 双变量图形的绘制\\strip charts_pro.xlsx")

strip_data_02 <- strip_data_02 %>% dplyr::select(2:4)

#重新修改变量顺序
strip_data_02$type <- factor(strip_data_02$type, 
                        levels=c("Non-SMK","SMK","Non-SMK+abx","SMK+abx"))
#计算P值
strip_pvals <- strip_data_02 %>%
  group_by(group) %>%
  rstatix::t_test(
    value ~ type, 
    p.adjust.method = "BH", 
    var.equal = TRUE, 
  )   %>% rstatix::add_x_position(x = "type", dodge = 0.9)

y_position = c(2.6, 2.8,3.1,3.5)
										
pairwise.grouped <- tibble::tribble(
  ~group1, ~group2, ~p.adj.signif,  ~y.position, ~group,
  "SMK", "SMK+abx", "NS",      3.5,              "Contact",
  "Non-SMK",   "SMK",     "****", 3.1,           "Contact",
  "Non-SMK",   "SMK",     "****", 2.6,           "Non_Contact",
  "Non-SMK+abx", "SMK+abx",  "****", 2.8,        "Non_Contact",
)


ggplot(data = strip_data_02,aes(x = type,y=value)) +
    # 添加均值横线
    stat_summary(geom = "crossbar",fun = mean,
        colour = "black",linewidth = 0.4, width = 0.7,
        show.legend = FALSE) +
    geom_jitter(aes(fill=type),shape=21,size=3.5,stroke = 0.5,
                width = 0.2) +
    scale_y_continuous(expand = c(0, 0),breaks = seq(-1,4,1),
                         limits = c(-1, 4)) +
    geom_hline(yintercept = 0,linetype="longdash",linewidth=0.4,alpha=0.8) +
  #分面操作
    facet_wrap(~ group,scales = "free") +
   #分面添加P值 p-value brackets with pairwise comparisons of grouped data
    ggprism::add_pvalue(
        pairwise.grouped,tip.length = 0, label.size = 5,
        fontface = "italic", lineend = "round", bracket.size = 0.5) +
    #设置图例成1行
    guides(fill = guide_legend(nrow = 1)) +
    ggprism::scale_fill_prism(palette = "winter_bright") +
    labs(x="",y="Values") +
    ggprism::theme_prism(palette = "winter_bright")+
    theme(legend.position = "bottom",
          legend.margin = margin(-20, 6, 0, 0),
          axis.text.x=element_text(angle=10),
          strip.text = element_text(size = 18))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-12 使用facet_wrap()绘制复杂统计点带图示例.png",
       width =8, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-12 使用facet_wrap()绘制复杂统计点带图示例.pdf",
       width =8, height = 4.5,device = cairo_pdf)