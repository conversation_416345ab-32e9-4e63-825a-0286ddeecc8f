% 清除工作区和窗口
clear all
clc
close all

% 从文件中读取数据，例如 no_gel.csv
data = readmatrix('gel.csv'); 

% 将数据分成三组，并处理各组数据的有效长度
force1 = data(~isnan(data(:, 1)), 1);  % 第一组力，只取有效数据
displacement1 = data(~isnan(data(:, 2)), 2);  % 第一组位移，只取有效数据

force2 = data(~isnan(data(:, 3)), 3);  % 第二组力，只取有效数据
displacement2 = data(~isnan(data(:, 4)), 4);  % 第二组位移，只取有效数据

force3 = data(~isnan(data(:, 5)), 5);  % 第三组力，只取有效数据
displacement3 = data(~isnan(data(:, 6)), 6);  % 第三组位移，只取有效数据

%% 调用画图函数
plotDisplacementForce(displacement1, force1, displacement2, force2, displacement3, force3);



%% 调用计算剥离能量的函数
peelingEnergy1 = calculatePeelingEnergy(force1, displacement1);
peelingEnergy2 = calculatePeelingEnergy(force2, displacement2);
peelingEnergy3 = calculatePeelingEnergy(force3, displacement3);

% 将每组的剥离能量存储在一个矩阵中（假设有多次测量）
peelingEnergies = [
    peelingEnergy1;  % 第一组剥离能量
    peelingEnergy2;  % 第二组剥离能量
    peelingEnergy3   % 第三组剥离能量
];

