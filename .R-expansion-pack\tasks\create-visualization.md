# 数据可视化创建任务

这个任务指导你创建有效、美观且有洞察力的数据可视化图表。

## 前提条件

- 数据已清洗和预处理
- 可视化目标和受众已明确
- 必要的R包已安装（ggplot2、plotly、shiny等）
- 了解数据的结构和含义

## 步骤

### 1. 可视化规划

收集以下信息：

- **可视化目的**: 探索、解释、展示、监控
- **目标受众**: 技术专家、管理层、公众
- **数据类型**: 数值、分类、时间序列、地理
- **变量数量**: 单变量、双变量、多变量
- **交互需求**: 静态图表 vs 交互式图表
- **输出格式**: 打印、网页、演示文稿

### 2. 图表类型选择

根据数据类型和目的选择合适的图表：

#### 单变量可视化
- **直方图**: 数值分布
- **密度图**: 平滑分布
- **箱线图**: 分布摘要
- **条形图**: 分类频次
- **饼图**: 比例关系

#### 双变量可视化
- **散点图**: 数值关系
- **线图**: 时间趋势
- **热力图**: 相关性矩阵
- **分组条形图**: 分类比较
- **小提琴图**: 分布比较

#### 多变量可视化
- **散点图矩阵**: 多维关系
- **平行坐标图**: 高维数据
- **雷达图**: 多指标比较
- **树状图**: 层次结构
- **网络图**: 关系网络

### 3. 设计原则

遵循可视化设计最佳实践：

#### 清晰性原则
- 简洁明了的标题和标签
- 清晰的图例和注释
- 合适的字体大小和样式
- 避免图表垃圾

#### 准确性原则
- 正确的比例和尺度
- 诚实的数据表示
- 避免误导性的视觉效果
- 标注数据来源和时间

#### 美观性原则
- 协调的色彩搭配
- 平衡的布局设计
- 一致的视觉风格
- 专业的外观

### 4. 颜色和主题设计

选择合适的颜色方案：

```r
# 使用RColorBrewer调色板
library(RColorBrewer)
display.brewer.all()

# 自定义主题
custom_theme <- theme_minimal() +
  theme(
    plot.title = element_text(size = 16, face = "bold"),
    axis.text = element_text(size = 12),
    legend.position = "bottom"
  )
```

### 5. 基础图表创建

使用ggplot2创建基础图表：

```r
# 散点图
ggplot(data, aes(x = var1, y = var2)) +
  geom_point(aes(color = group)) +
  labs(title = "标题", x = "X轴标签", y = "Y轴标签") +
  custom_theme

# 条形图
ggplot(data, aes(x = category, y = value)) +
  geom_col(fill = "steelblue") +
  coord_flip() +
  labs(title = "标题") +
  custom_theme
```

### 6. 高级可视化技术

#### 分面图表
```r
# 分面显示
ggplot(data, aes(x = var1, y = var2)) +
  geom_point() +
  facet_wrap(~group) +
  custom_theme
```

#### 动画图表
```r
# 使用gganimate
library(gganimate)
p <- ggplot(data, aes(x = var1, y = var2)) +
  geom_point() +
  transition_time(time_var)
animate(p)
```

#### 交互式图表
```r
# 使用plotly
library(plotly)
p <- ggplot(data, aes(x = var1, y = var2, text = label)) +
  geom_point()
ggplotly(p, tooltip = "text")
```

### 7. 专业图表类型

#### 统计图表
- **QQ图**: 正态性检验
- **残差图**: 模型诊断
- **森林图**: 效应量展示
- **生存曲线**: 生存分析

#### 商业图表
- **仪表盘**: KPI监控
- **瀑布图**: 变化分解
- **桑基图**: 流量分析
- **甘特图**: 项目进度

### 8. 图表优化

提升图表质量：

- **数据标签**: 关键数值标注
- **参考线**: 基准线、趋势线
- **注释**: 重要事件标记
- **缩放**: 合适的坐标范围
- **排序**: 逻辑性排列

### 9. 响应式设计

确保图表在不同设备上正常显示：

```r
# 设置图表尺寸
ggsave("plot.png", width = 10, height = 6, dpi = 300)

# 响应式字体大小
theme(text = element_text(size = rel(1.2)))
```

### 10. 可访问性考虑

确保图表对所有用户友好：

- **色盲友好**: 使用色盲安全调色板
- **高对比度**: 确保文字清晰可读
- **替代文本**: 提供图表描述
- **简化设计**: 避免过度复杂

### 11. 质量检查

验证可视化质量：

```bash
*execute-checklist analysis-checklist
```

### 12. 文档生成

创建可视化报告：

```bash
*create-doc analysis-report-tmpl
```

## 输出文件

可视化完成后应生成：

- **可视化脚本** (.R 或 .Rmd)
- **图表文件** (.png, .pdf, .svg)
- **交互式图表** (.html)
- **可视化报告** (.html 或 .pdf)

## 质量标准

- 图表清晰易读
- 颜色搭配合理
- 标题和标签完整
- 数据表示准确
- 设计风格一致

## 工具推荐

- **静态图表**: ggplot2, lattice
- **交互式图表**: plotly, htmlwidgets
- **专业图表**: ggpubr, corrplot
- **地图可视化**: leaflet, tmap
- **网络图**: igraph, networkD3
- **仪表盘**: shiny, flexdashboard

## 常见图表模板

### 学术论文图表
- 高分辨率、黑白兼容
- 简洁的设计风格
- 标准的统计图表

### 商业报告图表
- 品牌色彩方案
- 专业的外观
- 重点突出的设计

### 网页交互图表
- 响应式设计
- 用户友好的交互
- 快速加载优化

## 注意事项

- 选择合适的图表类型
- 避免3D效果和过度装饰
- 确保数据完整性
- 考虑文化差异
- 测试不同设备显示效果
