---
type: "agent_requested"
description: "R"
---
Rule 1: 文件组织原则
❌ 禁止自动保存功能：完全删除所有ggsave()自动保存图片的代码
❌ 删除测试文件：删除test_functions.R文件和所有测试相关代码段
💬 注释数据保存：将所有CSV数据保存代码（write.csv(), saveProcessedData()等）完全注释掉
📝 简化文档结构：删除README.md文件

Rule 2: 文件头部标准格式
每个R脚本文件开头必须包含以下标准注释：
# ==========================================
# 文件名：filename.R
# 主要功能：[详细描述文件的主要功能和用途]
# 依赖文件：source("other_file.R")  # 列出所有依赖的R文件
# 主要函数：
#   - function_name1(): [功能说明]
#   - function_name2(): [功能说明]
# 输入参数：[说明输入参数类型和格式]
# 输出结果：[说明返回值类型和格式]
# 创建日期：YYYY-MM-DD
# ==========================================

Rule 3: 代码简洁性要求
🔇 删除冗余输出：删除所有说明性cat()输出，仅保留必要的结果输出和关键中间过程
🎯 保留核心功能：数据读取、处理、计算、绘图
🔄 函数独立性：确保函数间相互独立，方便单独调用
📤 返回对象：所有绘图函数必须返回ggplot对象供用户手动保存

Rule4： 坐标轴设置

# Y轴必须从0开始，上方预留5%空间
scale_y_continuous(limits = c(0, max_value * 1.2), 
                   expand = expansion(mult = c(0, 0.05))) +
# 坐标轴标签使用中文或英文，根据数据内容确定
labs(x = "类别名称", y = "数值单位")

Rule 5: 主题和样式设置（核心规范）

theme_bw() +  # 必须使用theme_bw()作为基础主题
theme(
  # 背景设置 - 统一白色背景
  plot.background = element_rect(fill = "white", color = NA),
  panel.background = element_rect(fill = "white", color = NA),
  
  # 全局字体设置 - 统一Times New Roman
  text = element_text(family = "Times New Roman", color = "black"),
  
  # 坐标轴文字设置 - 固定字号和颜色
  axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
  axis.text.y = element_text(size = 10, color = "black"),
  
  # 坐标轴标题设置 - 粗体，固定字号
  axis.title = element_text(size = 12, face = "bold", color = "black"),
  
  # 网格线和边框设置 - 完整边框，无网格线
  panel.grid = element_blank(),  # 必须移除所有网格线
  axis.line = element_blank(),   # 移除轴线，避免与边框重复
  panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),
  
  # 刻度线设置 - 朝内，统一粗细
  axis.ticks = element_line(color = "black", linewidth = 0.6),
  axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内
  
  # 边距设置 - 标准边距
  plot.margin = margin(10, 10, 0, 10)  # 上右下左边距

)
Rule 6: 函数独立性要求
🔗 最小依赖：每个函数只依赖必要的包和其他函数
📥 清晰输入：参数类型和格式明确定义
📤 明确输出：返回值类型和结构清晰说明
🧪 可测试性：函数可以独立运行和测试