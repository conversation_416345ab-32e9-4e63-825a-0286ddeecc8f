"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)


#构建数据
pubble_data <- readxl::read_excel("\\第5章 多变量图形的绘制\\pubble_data.xlsx")

											  												 												  
####################################图5-2-3（a）“宽”数据等值线图绘制示例1
ggplot(data = pubble_data,aes(x=x,y = y)) +
  geom_point(aes(size=values),shape=21,fill="#459DFF",stroke=0.5) +
  scale_size(range = c(4,12)) +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 45),
                     breaks = seq(0,45,5)) +
  scale_x_continuous(expand = c(0,0),limits = c(5,45),
                     breaks = seq(5,45,5)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        #panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-3 气泡图数值大小、颜色属性映射的气泡图绘制示例_a.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-3 气泡图数值大小、颜色属性映射的气泡图绘制示例_a.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
	   
####################################图5-2-3（b）“宽”数据等值线图绘制示例2

ggplot(data = pubble_data,aes(x=x,y = y)) +
  geom_point(aes(size=values,fill=values02),shape=21,stroke=0.5)+
  scale_size(range = c(4,12)) +
  scale_fill_gradientn(colours = parula(100),breaks=seq(0.2,0.8,0.2),
                       guide = guide_colorbar(frame.colour = "black",
                                              ticks.colour = "black",
                                             )) +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 45),
                     breaks = seq(0,45,5)) +
  scale_x_continuous(expand = c(0,0),limits = c(5,45),
                     breaks = seq(5,45,5)) +
  labs(x='X Axis title', y='Y Axis title') +
  theme_bw() +
  theme(
        text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black",size = 16),
        axis.ticks.length=unit(0.2, "cm"),
        #panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-3 气泡图数值大小、颜色属性映射的气泡图绘制示例_b.png",
       width =5.5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-2-3 气泡图数值大小、颜色属性映射的气泡图绘制示例_b.pdf",
       width =5.5, height = 4.5,device = cairo_pdf)
   