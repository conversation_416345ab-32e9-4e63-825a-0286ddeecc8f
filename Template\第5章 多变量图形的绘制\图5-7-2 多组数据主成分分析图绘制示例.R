"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)
library(factoextra)
library(FactoMineR)


head(iris, 3)
iris.pca <- PCA(iris[,-5], graph = FALSE)
iris.pca

####################################图5-7-2（a）未添加置信椭圆区间的主成分分析图绘制示例

fviz_pca_ind(iris.pca,
             geom.ind = "point", # show points only (nbut not "text")
             col.ind = iris$Species, # color by groups
             palette = "aaas",
             pointsize=3,
             legend.title = "Groups"
             ) +
theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-2 多组数据主成分分析图绘制示例_a.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-2 多组数据主成分分析图绘制示例_a.pdf",
       width =5.5, height = 5,device = cairo_pdf)

	   
####################################图5-7-2（b）添加置信椭圆区间的主成分分析图绘制示例

fviz_pca_ind(iris.pca,
             geom.ind = "point", # show points only (nbut not "text")
             col.ind = iris$Species, # color by groups
             palette = "aaas",
             pointsize=3,
             addEllipses = TRUE, # Concentration ellipses
             ellipse.level=0.95,
             legend.title = "Groups"
             ) +
theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(family = "serif",face='bold',size=12),
        #显示更多刻度内容
        plot.margin = margin(5, 5, 5, 5))
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-2 多组数据主成分分析图绘制示例_b.png",
       width =5.5, height = 5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第5章 多变量图形的绘制\\图5-7-2 多组数据主成分分析图绘制示例_b.pdf",
       width =5.5, height = 5,device = cairo_pdf)