"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(qqplotr)


#读取数据

pq_data <- readxl::read_xlsx("F:\\书籍编写\\R语言-学术图表手册\\第3章 单变量图表绘制\\qq_pp_data.xlsx")

####################################图3-3-1（a）正态分布直方图
ggplot(data = pq_data,aes(x=normal_data,y = after_stat(density))) +
  geom_histogram(colour="black",fill="#0874C0",bins = 20) +
  geom_density(color="red") +
  scale_y_continuous(expand = c(0,0)) +
  theme_classic() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_a.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "F\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_a.pdf",
       width =5, height = 4.5,device = cairo_pdf)


####################################图3-3-1（b）正态分布Q-Q图
ggplot(data = pq_data, mapping = aes(sample = normal_data)) +
    stat_qq_band(fill="gray80") +
    stat_qq_line(color="red") +
    stat_qq_point(color="#0874C0",size=3,alpha=0.5) +
    labs(x = "Theoretical Quantiles", y = "Sample Quantiles") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_b.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_b.pdf",
       width =5, height = 4.5,device = cairo_pdf)
	   
####################################图3-3-1（c）正态分布P-P图
ggplot(data = pq_data, mapping = aes(sample = normal_data)) +
    stat_pp_band(fill="gray80") +
    stat_pp_line(color="red") +
    stat_pp_point(color="#0874C0",size=2,alpha=0.5) +
    labs(x = "Probability Points", y = "Cumulative Probability") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_c.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_c.pdf",
       width =5, height = 4.5,device = cairo_pdf)

####################################图3-3-1（d）均匀分布直方图
ggplot(data = pq_data,aes(x=uniform_data,y = after_stat(density))) +
  geom_histogram(colour="black",fill="#EDC219",bins = 20) +
  geom_density(color="red") +
  scale_y_continuous(expand = c(0,0)) +
  theme_classic() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_d.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_d.pdf",
       width =5, height = 4.5,device = cairo_pdf)


####################################图3-3-1（e）均匀分布Q-Q图
ggplot(data = pq_data, mapping = aes(sample = uniform_data)) +
    stat_qq_band(fill="gray80") +
    stat_qq_line(color="red") +
    stat_qq_point(color="#EDC219",size=3,alpha=0.5) +
    labs(x = "Theoretical Quantiles", y = "Sample Quantiles") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_e.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_e.pdf",
       width =5, height = 4.5,device = cairo_pdf)


####################################图3-3-1（f）均匀分布P-P图
ggplot(data = pq_data, mapping = aes(sample = uniform_data)) +
    stat_pp_band(fill="gray80") +
    stat_pp_line(color="red") +
    stat_pp_point(color="#EDC219",size=2,alpha=0.5) +
    labs(x = "Probability Points", y = "Cumulative Probability") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_f.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_f.pdf",
       width =5, height = 4.5,device = cairo_pdf)


####################################图3-3-1（g）指数分布直方图
ggplot(data = pq_data,aes(x=ex_data,y = after_stat(density))) +
  geom_histogram(colour="black",fill="#CC544D",bins = 20) +
  geom_density(color="red") +
  scale_y_continuous(expand = c(0,0)) +
  theme_classic() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_g.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_g.pdf",
       width =5, height = 4.5,device = cairo_pdf)

####################################图3-3-1（h）指数分布Q-Q图
ggplot(data = pq_data, mapping = aes(sample = ex_data)) +
    stat_qq_band(fill="gray80") +
    stat_qq_line(color="red") +
    stat_qq_point(color="#CC544D",size=3,alpha=0.5) +
    labs(x = "Theoretical Quantiles", y = "Sample Quantiles") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_h.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_h.pdf",
       width =5, height = 4.5,device = cairo_pdf)

####################################图3-3-1（i）指数分布P-P图	   
ggplot(data = pq_data, mapping = aes(sample = ex_data)) +
    stat_pp_band(fill="gray80") +
    stat_pp_line(color="red") +
    stat_pp_point(color="#CC544D",size=2,alpha=0.5) +
    labs(x = "Probability Points", y = "Cumulative Probability") +
    theme_classic() +
    theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_i.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-3-1 3种分布的直方图、Q-Q图和P-P图绘制示例_i.pdf",
       width =5, height = 4.5,device = cairo_pdf)
	   
	   