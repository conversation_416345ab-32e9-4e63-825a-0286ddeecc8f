"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)


#读取数据
file <- "F:\\书籍编写\\R语言-学术图表手册\\第4章 双变量图形的绘制\\面积图构建.xlsx"
area_data = read_excel(file)
area_df <-  area_data %>% pivot_longer(cols = starts_with("Area"),names_to = "type", 
                             values_to = "value",cols_vary="slowest")
											  
												  
												  
####################################图4-2-9（a）使用ggplot2绘制的面积图示例

ggplot(data = area_df,aes(x=day, y=value, fill=type)) +
  geom_area(colour="black") +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(0.15,0.88),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-9 不同样式面积图绘制示例对比_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-9 不同样式面积图绘制示例对比_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-9（b）使用ggplot2绘制的面积图示例（position = "identity"）

ggplot(data = area_df,aes(x=day, y=value, fill=type)) +
  geom_area(colour="black",position = "identity") +
  ggprism::scale_fill_prism(palette = "waves") +
  scale_y_continuous(limits = c(0,10),breaks = seq(0,10,2)) +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(0.15,0.88),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-9 不同样式面积图绘制示例对比_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-9 不同样式面积图绘制示例对比_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
   