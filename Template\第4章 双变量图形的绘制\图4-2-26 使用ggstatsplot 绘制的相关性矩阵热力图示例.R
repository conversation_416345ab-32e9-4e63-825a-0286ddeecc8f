"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(psych)

library(ggstatsplot)


#读取数据
heatmap_data <- read_excel("\\第4章 双变量图形的绘制\\相关性热力图_P值.xlsx")

				  
####################################图4-2-26（a）使用ggstatsplot 绘制的相关性矩阵热力图示例1

ggstatsplot::ggcorrmat(heatmap_data,matrix.type = "full") +
  theme(text = element_text(face='bold',size = 12),
       axis.text = element_text(colour = "black",face='bold',size = 15)
       )
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-26 使用ggstatsplot 绘制的相关性矩阵热力图示例_a.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-26 使用ggstatsplot 绘制的相关性矩阵热力图示例_a.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)
	   
####################################图4-2-26（b）ggstatsplot 相关性矩阵热力图示例2

ggstatsplot::ggcorrmat(heatmap_data,matrix.type = "full",type = "spearman",
                       pch = "square cross",
                       ggcorrplot.args = list(outline.color = "black",
                                              lab_col = "red",
                                              lab_size = 3.5,
                                              pch.col = "white",
                                              pch.cex = 8))+
 theme(text = element_text(face='bold',size = 12),
       axis.text = element_text(colour = "black",face='bold',size = 15)
       )
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-26 使用ggstatsplot 绘制的相关性矩阵热力图示例_b.png",
       width = 5.2, height = 4.2, dpi = 900,device =png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-26 使用ggstatsplot 绘制的相关性矩阵热力图示例_b.pdf",
       width = 5.2, height = 4.2, device = cairo_pdf)

