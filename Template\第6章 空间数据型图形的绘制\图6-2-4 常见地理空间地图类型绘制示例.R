"""
测试时间：2024年05月15日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(sf)
library(ggrepel)
library(pals)

#读取数据
map_fig02 <- sf::read_sf("\\第6章 空间数据型图形的绘制\\Virtual_Map1.shp")
point_data = read_excel("\\第6章 空间数据型图形的绘制\\地图监测点数据.xlsx")


####################################图6-2-4（a）多面中心坐标点绘制示例

#数据处理
point_center <- map_fig02 %>% sf::st_centroid()
ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  geom_sf(data = point_center,shape=21,size=2,fill="white",stroke=0.5) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        legend.background = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf) 
	   
####################################图6-2-4（b）多面中心坐标文本添加示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  ggplot2::geom_sf_text(data = map_fig02,aes(label=country),size=3,family="serif") +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        legend.background = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图6-2-4（c）避免文本重叠坐标文本添加示例
library(ggrepel)

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  geom_point(data = map_fig02,aes(geometry=geometry),shape=21,size = 2,fill="red",
             stat = "sf_coordinates") +
  ggrepel::geom_text_repel(data = map_fig02,aes(label=country,geometry = geometry),
                            stat = "sf_coordinates",vjust=2,
                            size=3,family="serif",bg.colour = "white", bg.r = .2) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(
        legend.background = element_blank(),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图6-2-4（d）多系列虚拟监测站点绘制示例

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.5) +
  geom_point(data = point_data,aes(x=lon,y = lat,colour=type,shape=type),size=2.5) + 
  scale_colour_manual(values = c("black","red")) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(0.85,0.2),
        legend.background = element_rect(colour="black",linewidth = 0.25),
        legend.title= element_text(size = 12),
        legend.text = element_text(size = 10),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图6-2-4（e）研究区域、注释信息绘制示例

library(sfheaders)

df <- data.frame(
  lon = c(109.5,109.5,112,112,
          119,119,123.5,123.5,
          119,119,124,124),
  lat = c(43.5,46,46,43.5,
          36.5,38.5,38.5,36.5,
          50,52.5,52.5,50),
  id =c(1,1,1,1,2,2,2,2,3,3,3,3)
)

polygon_sf <- sfheaders::sf_polygon(obj = df, x = "lon",y = "lat",polygon_id = "id")

point_df <- data.frame(
  lon = c(111,121,121),
  lat = c(44.5,37.5,51.5),
  id =c(1,2,3)
)

point_sf <- point_df %>% sfheaders::sf_point(x = "lon",y = "lat")
curve_df <- data.frame(x1 = c(111.5,121,120.5),
                       x2 = c(105,128.5,132), 
                       y1 = c(44.5,37.5,51.5), 
                       y2 = c(35,30,57))
text_data <- data.frame(x=c(105,128.5,132),
                        y=c(34,29,58),
                        label=c("Test Point 01","Test Point 02","Test Point 03"))

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.3) +
  geom_sf(data = polygon_sf,color="red",fill=NA) +
  geom_sf(data = point_sf) +
  geom_curve(data=curve_df,aes(x = x1, y = y1, xend = x2, yend = y2),linewidth=0.3) +
  geom_label(data=text_data,aes(x=x,y=y,label=label),family="serif",size=4) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(95,140),breaks = seq(95,140,5)) +
  scale_y_continuous(expand = c(0, 0),limits = c(25,60)) +
  theme(
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_e.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_e.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图6-2-4（f）单系列监测站点数值映射绘制示例	   

library(readxl)
point_data = read_excel("\\第6章 空间数据型图形的绘制\\地图监测点数据.xlsx")

point_data_select <- point_data %>% dplyr::filter(type=="one")

ggplot() +
  geom_sf(data = map_fig02,fill="#9CCA9C",alpha=0.8,linewidth=0.3) +
  geom_point(data = point_data_select,aes(x=lon,y = lat,fill=values),shape=21,size=2.5) + 
  scale_fill_gradientn(colors = parula(100),guide = guide_colorbar(frame.colour = "black",
                                                                   ticks.colour = "black"),
                      ) +
  labs(x="Longitude",y="Latitude")+
  theme_classic() +
  scale_x_continuous(expand = c(0, 0),limits = c(100,140),breaks = seq(95,140,5)) +
  scale_y_continuous(expand = c(0, 0),limits = c(28,60)) +
  theme(legend.position = c(.98,0.5),
        legend.title= element_text(size = 12),
        legend.text = element_text(size = 10),
        legend.key.height=unit(0.8, "cm"),
        legend.key.width=unit(0.5, "cm"),
        text = element_text(family = "serif",face='bold',size = 15),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        plot.margin = margin(10, 15, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = 0.4),
        axis.line = element_line(colour = "black",linewidth = 0.4))
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_f.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第6章 空间数据型图形的绘制\\图6-2-4 常见地理空间地图类型绘制示例_f.pdf",
       width =4.5, height = 4,device = cairo_pdf)	

	   
	   