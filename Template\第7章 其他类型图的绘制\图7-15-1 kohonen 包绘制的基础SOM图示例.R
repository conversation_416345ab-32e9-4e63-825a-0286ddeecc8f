
"""
测试时间：2024年05月16日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(pals)

library(kohonen)

X0 <- c(1,2,3,3,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,1,1,1,1)
X1 <- c(3,3,1,1,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,1,1,1,1)
X2 <- c(1,2,1,1,1,1,1,1,3,2,2,3,2,2,2,2,2,2,2,1,1,1,1)
X3 <- c(1,2,3,2,2,2,2,2,3,2,2,1,3,2,2,2,2,2,2,1,1,3,1)
X4 <- c(1,2,3,3,3,3,3,3,3,2,2,2,2,2,2,2,1,1,1,1,1,2,3)

dat <- data.frame(X0, X1, X2, X3, X4)
dat <- as.matrix(dat)
som <- kohonen::som(dat, grid = kohonen::somgrid(4, 4, "hexagonal"), rlen = 100, 
                    alpha = c(0.05, 0.01),
                    dist.fcts = "euclidean", keep.data = TRUE)


###############################图7-15-1 (a) kohonen 包 SOM图绘制 (type = “code”)

png(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_a.png",
    width = 6000, height = 6000,res=1200)
plot(som,palette.name = hcl.colors,main = "")
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_a.pdf",
    width = 6, height = 6)
plot(som,palette.name = hcl.colors,main = "")
dev.off()

###############################图7-15-1 (b) ）kohonen 包 SOM图绘制 (type = “property”)

png(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_b.png",
    width = 6000, height = 6000,res=1200)
plot(som, type = "property", property = getCodes(som, 1)[,1], 
     palette.name = hcl.colors, main = "", cex = 1.2)
dev.off()


pdf(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_b.pdf",
    width = 6, height = 6)
plot(som, type = "property", property = getCodes(som, 1)[,1], 
     palette.name = hcl.colors, main = "", cex = 1.2)
dev.off()

###############################图7-15-1 (c) ）kohonen 包 SOM图绘制 (shape = “straight”)

png(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_c.png",
    width = 6000, height = 6000,res=1200)
plot(som, type = "property", property = getCodes(som, 1)[,1], 
     shape = "straight", palette.name = hcl.colors, main = "", cex = 1.2)
dev.off()

pdf(file="\\第7章 其他类型图的绘制\\图7-15-1 kohonen 包绘制的基础SOM图示例_c.pdf",
    width = 6, height = 6)
plot(som, type = "property", property = getCodes(som, 1)[,1], 
     shape = "straight", palette.name = hcl.colors, main = "", cex = 1.2)
dev.off()

