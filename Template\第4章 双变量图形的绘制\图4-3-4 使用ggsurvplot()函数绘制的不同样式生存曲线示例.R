"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(readxl)
library(plotROC)


#读取数据
roc_data <- read.csv("\\第4章 双变量图形的绘制\\roc_data.csv")
roc_data <- roc_data %>% mutate(D.str=case_when(D == 1 ~ "Ill",
                                                D == 0 ~ "Healthy"))
roc_data_long <- plotROC::melt_roc(roc_data, "D", c("M1", "M2","M3","M4"))
																				
set.seed(2529)
D.ex <- rbinom(200, size = 1, prob = .5)
M1 <- rnorm(200, mean = D.ex, sd = .65)
M2 <- rnorm(200, mean = D.ex, sd = 1.5)

test <- data.frame(D = D.ex, D.str = c("Healthy", "Ill")[D.ex + 1], 
                   M1 = M1, M2 = M2, stringsAsFactors = FALSE)




###################################图4-3-2 (a) 使用plotROC绘制的单ROC曲线示例1
ggplot(test, aes(d = D, m = M1)) + 
   plotROC::geom_roc(n.cuts = 5,labelsize = 4, labelround = 2,family="serif") +
   style_roc() +
   theme(
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-2 (b) 使用plotROC绘制的单ROC曲线示例2（置信区间）
ggplot(test, aes(d = D, m = M1)) + 
   plotROC::geom_roc(labelsize = 4,family="serif") +
   geom_rocci() +
   style_roc() +
   theme(
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-2 (c) 使用plotROC绘制的多ROC曲线示例1

ggplot(roc_data_long, aes(d = D, m = M, color = name)) + 
  geom_roc(labelsize = 4,family="serif") + 
  style_roc() +
  ggsci::scale_color_aaas() +
  #ggprism::scale_color_prism(palette = "waves") +
  theme(legend.position = "top",
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-2 (b) 使用plotROC绘制的多ROC曲线示例2（置信区间）
ggplot(roc_data_long, aes(d = D, m = M, linetype = name,color=name)) + 
  geom_roc(labelsize = 4,family="serif") + 
  geom_rocci(alpha.box = 0.2)+
  style_roc() +
  ggsci::scale_color_aaas() +
  #ggprism::scale_color_prism(palette = "waves") +
  theme(legend.position = "top",
        legend.title = element_blank(),
        text = element_text(family = "serif",face='bold',size = 14),
        axis.text = element_text(colour = "black",face='bold',size = 12),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(rep(5,4)))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-2 plotROC包绘制的多种ROC曲线示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)