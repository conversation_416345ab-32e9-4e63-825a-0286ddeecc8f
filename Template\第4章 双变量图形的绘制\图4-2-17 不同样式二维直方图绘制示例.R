"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
hist2d_data <- readr::read_csv("\\第4章 双变量图形的绘制\\hist2d_hexbin_data.csv")

											  
####################################图4-2-17（a）二维直方图绘制示例（bins=10）

ggplot(data = hist2d_data,aes(x = x_values,y = y_values)) +
  geom_bin_2d(bins=10) +
  labs(title = "bins=10",x="X Values", y = "Y Values") +
  scale_fill_gradientn(colours = parula(100)) +
  theme_bw() +
  theme(legend.position = c(.12,.76),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #panel.grid.minor = element_blank(), #去除副网格
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-17（b）二维直方图绘制示例（bins=40）

ggplot(data = hist2d_data,aes(x = x_values,y = y_values)) +
  geom_bin_2d(bins=40) +
  labs(title = "bins=40",x="X Values", y = "Y Values") +
  scale_fill_gradientn(colours = parula(100)) +
  theme_bw() +
  theme(legend.position = c(.12,.76),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #panel.grid.minor = element_blank(), #去除副网格
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-17（c）二维直方图绘制示例（after_stat(density)）
ggplot(data = hist2d_data,aes(x = x_values,y = y_values)) +
  geom_bin_2d(bins=40,aes(fill = after_stat(density))) +
  labs(title = "bins=40 with density",x="X Values", y = "Y Values") +
  scale_fill_gradientn(colours = parula(100)) +
  theme_bw() +
  theme(legend.position = c(.12,.76),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #panel.grid.minor = element_blank(), #去除副网格
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-2-17（d）二维直方图绘制示例（添加频次文本）  

ggplot(data = hist2d_data,aes(x = x_values,y = y_values)) +
  geom_bin_2d(bins=10) +
  #添加个数文本
  stat_bin_2d(bins=10,geom = "text", aes(label = after_stat(count)),
              family = "serif")+
  labs(title = "add counts text in per pixel",x="X Values", y = "Y Values") +
  scale_fill_gradientn(colours = parula(100)) +
  theme_bw() +
  theme(legend.position = c(.12,.76),
        legend.text=element_text(family = "serif",face='bold',size = 13,),
        legend.title=element_text(family = "serif",face='bold',size = 13),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #panel.grid.minor = element_blank(), #去除副网格
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-17 不同样式二维直方图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)   