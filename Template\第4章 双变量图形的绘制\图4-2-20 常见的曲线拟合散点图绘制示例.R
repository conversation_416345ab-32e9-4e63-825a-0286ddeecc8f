"""
测试时间：2024年05月11日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(ggpmisc)


#读取数据
cure_data <- read_excel("\\第4章 双变量图形的绘制\\Curve_Fitting_Methods.xlsx")
					  
####################################图4-2-20（a）LOWESS回归曲线拟合散点图

ggplot(data = cure_data,aes(x = x,y = y)) +
  geom_point(size=3) +
  geom_smooth(aes(fill="LOESS"),method = "loess",linewidth=1,
              color="red",se=FALSE) +
# #修改坐标轴刻度
  scale_x_continuous(limits = c(20,160),breaks = seq(25,150,25),
                     expand = c(0,0)) +
  scale_y_continuous(limits = c(5,120),breaks = seq(20,120,20),
                     expand = c(0,0)) +
  labs(x="Values", y = "Estimated Values") +
  #添加不同线图例
  scale_fill_manual('', values = c('gray')) +
  theme_bw() +
  theme(legend.position = c(0.2,0.95),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-20（b）Quadratic回归曲线拟合散点图

ggplot(data = cure_data,aes(x = x,y = y)) +
  geom_point(size=3) +
  geom_smooth(aes(fill="QUADRATIC"),method = "lm",se=FALSE,
              formula = y~poly(x,2),linewidth=1,color="red") +

# #修改坐标轴刻度
  scale_x_continuous(limits = c(20,160),breaks = seq(25,150,25),
                     expand = c(0,0)) +
  scale_y_continuous(limits = c(5,120),breaks = seq(20,120,20),
                     expand = c(0,0)) +
  labs(x="Values", y = "Estimated Values") +
  #添加不同线图例
  scale_fill_manual('', values = c('gray')) +
  theme_bw() +
  theme(legend.position = c(0.25,0.95),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
####################################图4-2-20（c）Logarithmic回归曲线拟合散点图
ggplot(data = cure_data,aes(x = x,y = y)) +
  geom_point(size=3) +
  geom_smooth(aes(fill="LOGARITHMIC"),method = "lm",se=FALSE,
              formula = y ~ log(x),linewidth=1,color="red") +

# #修改坐标轴刻度
  scale_x_continuous(limits = c(20,160),breaks = seq(25,150,25),
                     expand = c(0,0)) +
  scale_y_continuous(limits = c(5,120),breaks = seq(20,120,20),
                     expand = c(0,0)) +
  labs(x="Values", y = "Estimated Values") +
  #添加不同线图例
  scale_fill_manual('', values = c('gray')) +
  theme_bw() +
  theme(legend.position = c(0.28,0.95),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

####################################图4-2-20（d）Exponential回归曲线拟合散点图
ggplot(data = cure_data,aes(x = x,y = y)) +
  geom_point(size=3) +
  stat_smooth(aes(fill="EXPONENTIAL"),
              method = 'nls', formula = y ~ a*exp(b*x),
              method.args = list(start=c(a=0.1646, b=9.5e-8)), 
              se=FALSE,linewidth=1,color="red") +
# #修改坐标轴刻度
  scale_x_continuous(limits = c(20,160),breaks = seq(25,150,25),
                     expand = c(0,0)) +
  scale_y_continuous(limits = c(5,120),breaks = seq(20,120,20),
                     expand = c(0,0)) +
  labs(x="Values", y = "Estimated Values") +
  #添加不同线图例
  scale_fill_manual('', values = c('gray')) +
  theme_bw() +
  theme(legend.position = c(0.28,0.95),
        legend.background =element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(linewidth = 0.4),
        axis.line = element_line(linewidth = 0.4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-20 常见的曲线拟合散点图绘制示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)						
