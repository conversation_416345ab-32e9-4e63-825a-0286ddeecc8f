"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(reshape2)
library(rstatix)


#读取数据
file <- "\\第4章 双变量图形的绘制\\点线图构建02.xlsx"
dot_line_data = read_excel(file)

#转换成长数据								
dot_line_df <- dot_line_data %>% tidyr::pivot_longer(cols = one:four,
                                                  cols_vary = "slowest",
                                                  names_to = "type", 
                                                  values_to = "values")
#改变绘图属性顺序
dot_line_df$type <- factor(dot_line_df$type, 
                            levels=c("one","two","three","four"), ordered=TRUE)

#计算P值
stat.test <- dot_line_df %>% 
  rstatix::t_test(values ~ type, paired = FALSE) 											  

colors <- c("#2FBE8F","#459DFF","#FF5B9B","#FFCC37")											  
												  										  
												  
												  
####################################图4-2-3（a）使用ggplot2绘制的带显示性P值点线图示例1
ggplot(data = dot_line_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=1.5) +
  geom_point(aes(fill=type,shape=type),size=6,stroke=1) +
  #绘制one three P值横线
  geom_segment(aes(x = 10,xend=10,y =21.7,yend=27.3)) +
  annotate("text",x=11.5,y=24,label="ns",size=6,family='serif',angle=-90) +
  scale_color_manual(values = colors) +
  scale_fill_manual(values = colors) +
  scale_shape_manual(values=c(21,22,23,24)) +
  guides(color=guide_legend(override.aes = list(size=4))) +
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(.15, .83),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-3 带显著性P值点线图示例_a.png",
      width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-3 带显著性P值点线图示例_a.pdf",
       width =5.5, height = 5,device = cairo_pdf)
	   
####################################图4-2-3（b）使用ggplot2绘制的带显示性P值点线图示例2

ggplot(data = dot_line_df,aes(x = day,y = values)) +
  geom_line(aes(color=type),linewidth=1.5) +
  geom_point(aes(fill=type,shape=type),size=6,stroke=1) +
  #绘制one three P值横线
  geom_segment(aes(x = 9.5,xend=9.5,y =21.5,yend=27.5)) +
  annotate("text",x=11,y=24,label="ns",size=6,family='serif',angle=-90) +
   #绘制two four P值横线
  geom_segment(aes(x = 12,xend=12,y =18.5,yend=25)) +
  annotate("text",x=12.8,y=21.5,label="***",size=6,family='serif',angle=-90) +
  scale_color_manual(values = colors) +
  scale_fill_manual(values = colors) +
  scale_shape_manual(values=c(21,22,23,24)) +
  guides(color=guide_legend(override.aes = list(size=4))) +
  scale_x_continuous(limits = c(-2,40),breaks = seq(0,40,10),expand = c(0,0)) +
  scale_y_continuous(limits = c(-6,30),breaks = seq(-5,30,5),expand = c(0,0)) +
  labs(x="Day", y = "Values") +
  theme_bw() +
  theme(legend.position = c(.15, .83),
        legend.title = element_blank(),
        legend.background = element_blank(),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除网格
        #显示更多刻度内容
        plot.margin = margin(10, 10.5, 10, 10),
        axis.ticks = element_line(linewidth = .4),
        axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-3 带显著性P值点线图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-2-3 带显著性P值点线图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
   