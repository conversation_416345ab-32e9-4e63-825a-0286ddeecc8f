"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggprism)

#构建数据集
bar_data01 = data.frame(name = c("A","B","C","D","E"),
                        value =c(3,13,6,18,45))

# 需要对其值进行排序(升序)，降序则使fct_reorder(name,desc(value))
bar_data01<-bar_data01 %>% mutate(name = forcats::fct_reorder(name,value))										

####################################图4-1-19（a）单数据柱形图绘制示例（升序排列）
ggplot(data = bar_data01, aes(x = name,y = value)) +
  geom_bar(aes(fill=name),stat = "identity",width= .65,
           colour="black",linewidth=.5) +
  #添加数值文本
  geom_text(aes(label=value),size=6,vjust=-.3,family="serif",fontface="bold") +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 50)) +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Name",y="Values") +
  theme_classic()+
  theme(legend.position=c(0.1,0.8),
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                 hjust = 0.3),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-19 单数据柱形图示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-19 单数据柱形图示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)


####################################图4-1-19（b）单数据柱形图绘制示例（降序排列）

# 降序则使fct_reorder(name,desc(value))
bar_data01<-bar_data01 %>% mutate(name = forcats::fct_reorder(name,value,.desc = TRUE))


ggplot(data = bar_data01, aes(x = name,y = value)) +
  geom_bar(aes(fill=name),stat = "identity",width= .65,
           colour="black",linewidth=.5) +
  #添加数值文本
  geom_text(aes(label=value),size=6,vjust=-.3,family="serif",fontface="bold") +
  scale_y_continuous(expand = c(0, 0),limits = c(0, 50)) +
  ggprism::scale_fill_prism(palette = "waves") +
  labs(x="Name",y="Values") +
  theme_classic()+
  theme(legend.position=c(0.85,0.8),
        legend.title = element_blank(),
        legend.text = element_text(size = 12),
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.text.x=element_text(colour = "black",face='bold',size = 18,
                                 hjust = 0.3),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-19 单数据柱形图示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-19 单数据柱形图示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)