"""
测试时间：2024年05月08日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#读取数据

ecdf_data <- readxl::read_xlsx("\\第3章 单变量图表绘制\\ecdf_data.xlsx")


####################################图3-4-1（a）双峰正态分布直方图
ggplot(data = ecdf_data,aes(x=ecdf_data,y = after_stat(density))) +
  geom_histogram(colour="black",fill="#CC544D",bins = 20) +
  geom_density(color="red",linewidth=1) +
  scale_y_continuous(expand = c(0,0)) +
  theme_classic() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_a.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_a.pdf",
       width =5, height = 4.5,device = cairo_pdf)


####################################图3-4-1（b）经验分布函数图绘制示例（ggplot2）
ggplot(data = ecdf_data,aes(x = ecdf_data)) +
  stat_ecdf(geom = "step",color="#CC544D",linewidth=1) +
  theme_classic() +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_b.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_b.pdf",
       width =5, height = 4.5,device = cairo_pdf)
	   
####################################图3-4-1（c）经验分布函数图绘制示例（ggpubr）

library(ggpubr)

ggpubr::ggecdf(data = ecdf_data,x = "ecdf_data",color = "#0874C0",size = 1,
              ggtheme=theme_classic()) +
  theme(
        text = element_text(family = "serif",face='bold',size = 18),
        axis.text = element_text(colour = "black",face='bold',size = 15),
        axis.ticks.length=unit(.2, "cm"),
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_c.png",
       width = 5, height = 4.5, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第3章 单变量图表绘制\\图3-4-1 正态分布数据的经验分布函数图绘制示例_c.pdf",
       width =5, height = 4.5,device = cairo_pdf)