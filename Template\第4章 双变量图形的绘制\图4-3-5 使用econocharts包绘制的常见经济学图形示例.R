"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(readxl)
library(econocharts)


###################################图4-3-5 (a) 供给曲线（supply curve）绘制示例
pupply_p_cus <- supply(ncurves = 1,            # Number of supply curves to be plotted
       type = "convex",        # Type of the curve ("line" or "convex")
       x = c(3, 5, 6),         # Y-axis values where to create intersections
       linecol = "red",            # Color of the curves
       generic = TRUE,         # Intersection labels are in a generic form (X_A, Y_A)
       geom = "label",         # Label type of the intersection points
       geomfill = "lightblue", # If geom = "label", is the background color of the label
       main = "Supply curve")  # Title of the plot

pupply_p_cus$p 
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-5 (b) 需求曲线（demand curve）绘制示例
demand_p_cus <- demand(
    type = "convex",        # Type of the curve ("line" or "convex")
    x = c(3, 5, 6),         # Y-axis values where to create intersections
    linecol = 4,            # Color of the curves
    generic = TRUE,         # Intersection labels are in a generic form (X_A, Y_A)
    geom = "label",         # Label type of the intersection points
    geomfill = "lightblue", # If geom = "label", is the background color of the label
    main = "Demand curves"

)

pdemand_p_cus$p
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-5 (c) 供给/需求曲线绘制示例
# Add custom curves
supply1 <- data.frame(Hmisc::bezier(c(1, 3, 9),
                                    c(9, 3, 1))) 

supply2 <- data.frame(Hmisc::bezier(c(2.5, 4.5, 10.5),
                                    c(10.5, 4.5, 2.5))) 

demand1 <- data.frame(Hmisc::bezier(c(1, 8, 9),
                                    c(1, 5, 9))) 
# Supply and demand curves and arrows
sdcurve_cus <- sdcurve(supply1, demand1, supply2, demand1,
        names = c("D[1]", "S[1]","D[2]", "S[1]")) +
  annotate("segment", x = 2.5, xend = 3.5, y = 7, yend = 7, # Add arrows
           arrow = arrow(length = unit(0.3, "lines")), colour = "grey50") +
  annotate("segment", x = 1, xend = 1, y = 3.5, yend = 4.5,               
           arrow = arrow(length = unit(0.3, "lines")), colour = "grey50") +
  annotate("segment", x = 5, xend = 6, y = 1, yend = 1,               
           arrow = arrow(length = unit(0.3, "lines")), colour = "grey50")

sdcurve_cus
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)


###################################图4-3-5 (d) 无差异曲线（indifference curve）绘制示例
p <- indifference(ncurves = 2,  # Two curves
                  x = c(2, 4),  # Intersections
                  main = "Indifference curves", # Title
                  xlab = "Good X",              # X-axis label
                  ylab = "Good Y",              # Y-axis label
                  linecol = 2,  # Color of the curves
                  pointcol = 2) # Color of the intersection points
# Add a new point
int <- bind_rows(curve_intersect(data.frame(x = 1:1000, y = rep(3, nrow(p$curve))), p$curve + 1))
p$p + geom_point(data = int, size = 3, color = 2) +
      annotate(geom = "text", x = int$x + 0.25, y = int$y + 0.25, label = "C")
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
###################################图4-3-5 (e) 生产可能性边界（Production-Possibility Frontier，PDF）绘制示例
p <- ppf(x = 4:6,                   # Intersections
         main = "PPF",              # Title
         geom = "text",             # Intersection labels as text
         generic = TRUE,            # Generic axis tick labels
         labels = c("A", "B", "C"), # Custom labels
         xlab = "Product B",        # X-axis label
         ylab = "Product A",        # Y-axis label
         acol = 3)                  # Color of the area

# Add more layers to the plot
p$p + geom_point(data = data.frame(x = 5, y = 5), size = 3) +
  geom_point(data = data.frame(x = 2, y = 2), size = 3) +
  annotate(geom = "text", x = 2.25, y = 2.25, label = "D") +
  annotate(geom = "text", x = 5.25, y = 5.25, label = "E") +
  annotate("segment", x = 3.1, xend = 4.25, y = 5, yend = 5,
           arrow = arrow(length = unit(0.5, "lines")), colour = 3, lwd = 1) +
  annotate("segment", x = 4.25, xend = 4.25, y = 5, yend = 4,
           arrow = arrow(length = unit(0.5, "lines")), colour = 3, lwd = 1)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_e.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_e.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-5 (f) 拉弗曲线（Laffer curve）绘制示例 
laffer_p <- laffer(xlab = "t",         # X-axis label
       ylab = "T",         # Y-axis label  
       acol = "lightblue", # Color of the area
       alpha = 0.3,        # Transparency of the area
       pointcol = 4)       # Color of the optimum point
laffer_p$p
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_f.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-5 使用econocharts包绘制的常见经济学图形示例_f.pdf",
       width =4.5, height = 4,device = cairo_pdf)	   
	   