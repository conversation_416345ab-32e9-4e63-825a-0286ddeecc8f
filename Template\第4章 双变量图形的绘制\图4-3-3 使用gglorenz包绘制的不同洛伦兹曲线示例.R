"""
测试时间：2024年05月13日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(pals)
library(ggpubr)
library(readxl)
library(gglorenz)


#读取数据
head(billionaires)
data2 <- billionaires %>% filter(Industry %in% c("Technology", "Real Estate")) 


###################################图4-3-3 (a) ）使用gglorenz绘制的洛伦兹曲线示例1
ggplot(data = billionaires,aes(TNW)) +
    stat_lorenz(desc = TRUE) +
    geom_abline(linetype = "dashed") +
    annotate_ineq(billionaires$TNW,family="serif",fontface="bold",
                  x = 0.15,y = 0.9,size=5,color = "red") +
    labs(x = "Cumulative Percentage of the Top 500 Billionaires",
         y = "Cumulative Percentage of Total Net Worth") +
    hrbrthemes::scale_x_percent() +
    hrbrthemes::scale_y_percent() +
    hrbrthemes::theme_ipsum_rc(base_family = "serif",
                               axis_title_size = 12,
                               axis_title_face = "bold",
                               plot_margin = margin(rep(10,4))) +
    theme(
          axis.text = element_text(colour = "black",face='bold',size = 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_a.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_a.pdf",
       width =4.5, height = 4,device = cairo_pdf)
	   
###################################图4-3-3 (b) ）使用gglorenz绘制的洛伦兹曲线示例2
gggplot(data = data2,aes(x = TNW, colour = Industry)) +
    stat_lorenz(desc = TRUE) +
    geom_abline(linetype = "dashed") +
    ggsci::scale_color_aaas() +
    labs(x = "Cumulative Percentage of the Top 500 Billionaires",
         y = "Cumulative Percentage of Total Net Worth") +
    hrbrthemes::scale_x_percent() +
    hrbrthemes::scale_y_percent() +
    hrbrthemes::theme_ipsum_rc(base_family = "serif",
                               axis_title_size = 10,
                               axis_title_face = "bold",
                               plot_margin = margin(rep(10,4))) +
    theme(legend.position = "top",
          axis.text = element_text(colour = "black",face='bold',size = 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_b.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_b.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-2 (c) ）使用gglorenz绘制的洛伦兹曲线示例3

billionaires %>%
    filter(Industry %in% c("Technology", "Real Estate")) %>% 
    mutate(Industry = forcats::as_factor(Industry)) %>% 
    ggplot(aes(x = TNW, fill = Industry)) +
    stat_lorenz(geom = "polygon", alpha = 0.8) +
    geom_abline(linetype = "dashed") +
    ggsci::scale_fill_aaas() +
    labs(x = "Cumulative Percentage of the Top 500 Billionaires",
         y = "Cumulative Percentage of Total Net Worth") +
    hrbrthemes::scale_x_percent() +
    hrbrthemes::scale_y_percent() +
    hrbrthemes::theme_ipsum_rc(base_family = "serif",
                               axis_title_size = 10,
                               axis_title_face = "bold",
                               plot_margin = margin(rep(10,4))) +
    theme(legend.position = "top",
          axis.text = element_text(colour = "black",face='bold',size = 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_c.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_c.pdf",
       width =4.5, height = 4,device = cairo_pdf)

###################################图4-3-2 (d) ）使用gglorenz绘制的洛伦兹曲线示例4

data2 <- data2 %>% add_row(Industry = "Perfect Equality", TNW = 1) 

ggplot(data = data2,aes(x = TNW, fill = Industry)) +
    stat_lorenz(geom = "area", alpha = 0.8,colour="black") +
    ggsci::scale_fill_aaas() +
    labs(x = "Cumulative Percentage of the Top 500 Billionaires",
         y = "Cumulative Percentage of Total Net Worth") +
    hrbrthemes::scale_x_percent() +
    hrbrthemes::scale_y_percent() +
    hrbrthemes::theme_ipsum_rc(base_family = "serif",
                               axis_title_size = 10,
                               axis_title_face = "bold",
                               plot_margin = margin(rep(10,4))) +
    theme(legend.position = "top",
          axis.text = element_text(colour = "black",face='bold',size = 10))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_d.png",
       width =4.5, height = 4, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-3-3 使用gglorenz包绘制的不同洛伦兹曲线示例_d.pdf",
       width =4.5, height = 4,device = cairo_pdf)