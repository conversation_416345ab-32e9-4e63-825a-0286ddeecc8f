"""
测试时间：2024年05月09日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)


#构建数据
dot_data <- tibble(x = c(79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105), 
                   y = c(1, 0, 0, 2, 1, 2, 7, 3, 7, 9, 11, 12, 15, 8, 10, 13, 11, 8, 9, 2, 3, 2, 1, 3, 0, 1, 1)) 
dot_data <- tidyr::uncount(dot_data, y)

										

####################################图4-1-2（a）使用geom_dotplot() 绘制的点图示例（灰色）
ggplot(data = dot_data) +
  geom_dotplot(aes(x = x),method = 'histodot', binwidth = 1,
               fill="gray",stroke = 1) +
  #scale_y_continuous(expand = c(0, 0),limits = c(0,1)) +
  labs(x="Class",y="Counts") +
  # Make this as high as the tallest column
  #coord_fixed(ratio = 25) +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-2 点图绘制示例_a.png",
       width =4.5, height = 3, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-2 点图绘制示例_a.pdf",
       width =4.5, height = 3,device = cairo_pdf)


####################################图4-1-2（b）使用geom_dotplot() 绘制的点图示例（彩色）
ggplot(data = dot_data) +
  geom_dotplot(aes(x = x),method = 'histodot', binwidth = 1,
               fill="#2FBE8F",stroke = 1) +
  #scale_y_continuous(expand = c(0, 0),limits = c(0,1)) +
  labs(x="Class",y="Counts") +
  # Make this as high as the tallest column
  #coord_fixed(ratio = 25) +
  theme_bw() +
  theme(
        text = element_text(family = "serif",face='bold',size = 20),
        axis.text = element_text(colour = "black",face='bold',size = 18),
        axis.ticks.length=unit(.2, "cm"),
        panel.grid.minor = element_blank(), #去除副网格
        #显示更多刻度内容
        plot.margin = margin(10, 10, 10, 10),
        axis.ticks = element_line(colour = "black",linewidth = .4),
        axis.line = element_line(colour = "black",linewidth = .4))

ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-2 点图绘制示例_b.png",
       width =4.5, height = 3, bg="white",dpi = 900,device=png)
ggsave(filename = "\\第4章 双变量图形的绘制\\图4-1-2 点图绘制示例_b.pdf",
       width =4.5, height = 3,device = cairo_pdf)