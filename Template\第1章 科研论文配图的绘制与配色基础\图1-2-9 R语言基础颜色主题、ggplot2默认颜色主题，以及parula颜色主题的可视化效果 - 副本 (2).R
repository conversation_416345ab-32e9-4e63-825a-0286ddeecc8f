"""
测试时间：2024年05月07日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)

#获取绘图数据
data_file <- "\\第1章 科研论文配图的绘制与配色基础\\tips.csv"
tips <- readr::read_csv(data_file)

####图1-2-9a　rainbow 基础颜色主题

ggplot(data = tips,aes(x = day,y = total_bill,fill=day))+
  geom_violin(trim=FALSE,show.legend = FALSE) +
  geom_boxplot(width=0.1,show.legend = FALSE) +
  labs(x="Class", y = "Values")+
  theme_classic()+
 #使用R默认颜色系rainbow
  scale_fill_manual(values = rainbow(4)) +
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(-10, 60),
                     breaks = seq(-10, 60, by = 10))+
  theme(text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_a.png",
       width = 4, height = 4.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_a.pdf",
       width = 4, height = 4.2,device = cairo_pdf)

####图1-2-9b　ggplot2 默认颜色主题
ggplot(data = tips,aes(x = day,y = total_bill,fill=day))+
  geom_violin(trim=FALSE,show.legend = FALSE) +
  geom_boxplot(width=0.1,show.legend = FALSE) +
  labs(x="Class", y = "Values")+
  theme_classic()+
  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(-10, 60),
                     breaks = seq(-10, 60, by = 10))+
  theme(text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_b.png",
       width = 4, height = 4.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_b.pdf",
       width = 4, height = 4.2,device = cairo_pdf)

####图1-2-9c　parula 颜色主题
library(pals)
ggplot(data = tips,aes(x = day,y = total_bill,fill=day))+
  geom_violin(trim=FALSE,show.legend = FALSE) +
  geom_boxplot(width=0.1,show.legend = FALSE) +
  labs(x="Class", y = "Values")+
  scale_fill_manual(values = parula(4))+
  theme_classic()+

  #定制化刻度样式
  scale_y_continuous(expand = c(0, 0), limits = c(-10, 60),
                     breaks = seq(-10, 60, by = 10))+
  theme(text = element_text(family = "serif",size = 18),
        axis.text = element_text(colour = "black"),
        axis.ticks.length=unit(.2, "cm"))
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_c.png",
       width = 4, height = 4.2, dpi = 900,device=png)
ggsave(filename = "\\第1章 科研论文配图的绘制与配色基础\\图1-2-9　R语言基础颜色主题、ggplot2默认颜色主题，以及parula颜色主题的可视化效果_c.pdf",
       width = 4, height = 4.2,device = cairo_pdf)

