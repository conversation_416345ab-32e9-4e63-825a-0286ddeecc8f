# R包依赖管理检查清单

[[LLM: 这是R包依赖管理专用检查清单。执行时：
1. 系统性检查所有依赖相关方面
2. 为每项提供通过/失败/部分通过的评估
3. 记录依赖决策的理由和风险
4. 生成依赖管理评估报告
5. 确保依赖安全性和可维护性]]

## 检查清单概述

**包名称**: {{package_name}}
**检查日期**: {{check_date}}
**检查者**: {{checker_name}}
**包版本**: {{package_version}}

---

## 1. 依赖声明检查 (权重: 25%)

[[LLM: 检查DESCRIPTION文件中的依赖声明是否正确完整]]

### 1.1 DESCRIPTION文件完整性
- [ ] **Imports字段**: 所有运行时必需的包都在Imports中声明
- [ ] **Suggests字段**: 可选功能和测试依赖在Suggests中声明
- [ ] **Depends字段**: 仅用于R版本要求和必须attach的包
- [ ] **LinkingTo字段**: C++包依赖正确声明（如适用）

### 1.2 版本要求合理性
- [ ] **最低版本**: 版本要求基于实际功能需求
- [ ] **版本范围**: 避免过于严格的版本限制
- [ ] **R版本要求**: R版本要求合理且经过测试
- [ ] **向后兼容**: 考虑了向后兼容性

### 1.3 依赖分类正确性
- [ ] **核心功能依赖**: 核心功能依赖在Imports中
- [ ] **测试依赖**: 测试相关包在Suggests中
- [ ] **文档依赖**: 文档生成包在Suggests中
- [ ] **示例依赖**: 示例代码依赖适当声明

### 1.4 命名空间使用
- [ ] **显式导入**: 使用pkg::function()或@importFrom
- [ ] **避免全量导入**: 避免不必要的@import
- [ ] **NAMESPACE一致**: NAMESPACE文件与DESCRIPTION一致
- [ ] **冲突处理**: 处理了函数名冲突

**第1部分得分**: ___/16 (通过标准: ≥13)

---

## 2. 依赖安全性检查 (权重: 30%)

[[LLM: 检查依赖包的安全性和可信度]]

### 2.1 包来源验证
- [ ] **CRAN包**: 优先使用CRAN上的稳定包
- [ ] **Bioconductor包**: 生物信息学包来自Bioconductor
- [ ] **GitHub包**: 避免或谨慎使用开发版本包
- [ ] **来源记录**: 非标准来源的包有明确记录

### 2.2 维护状态检查
- [ ] **活跃维护**: 依赖包在过去2年内有更新
- [ ] **非孤儿包**: 依赖包不是CRAN孤儿包
- [ ] **维护者信誉**: 包维护者有良好声誉
- [ ] **社区支持**: 包有活跃的用户社区

### 2.3 安全漏洞检查
- [ ] **已知漏洞**: 检查依赖包是否有已知安全漏洞
- [ ] **安全更新**: 及时更新有安全修复的依赖
- [ ] **漏洞监控**: 建立依赖包安全监控机制
- [ ] **风险评估**: 评估安全风险并记录

### 2.4 许可证兼容性
- [ ] **许可证识别**: 所有依赖包许可证明确
- [ ] **兼容性检查**: 依赖许可证与项目许可证兼容
- [ ] **传染性许可**: 注意GPL等传染性许可证影响
- [ ] **商业使用**: 确认商业使用的许可证合规性

**第2部分得分**: ___/16 (通过标准: ≥13)

---

## 3. 依赖最小化原则 (权重: 20%)

[[LLM: 检查是否遵循最小依赖原则]]

### 3.1 必要性评估
- [ ] **功能必需**: 每个依赖都有明确的功能需求
- [ ] **替代方案**: 评估了是否有更轻量的替代方案
- [ ] **自实现可能**: 考虑了简单功能的自实现
- [ ] **依赖理由**: 每个依赖都有充分的使用理由

### 3.2 重依赖避免
- [ ] **传递依赖**: 检查并最小化传递依赖数量
- [ ] **大型包避免**: 避免为小功能引入大型包
- [ ] **重复功能**: 避免多个包提供相同功能
- [ ] **依赖树分析**: 分析完整的依赖树

### 3.3 条件依赖
- [ ] **可选功能**: 可选功能使用条件依赖
- [ ] **平台特定**: 平台特定依赖适当处理
- [ ] **版本条件**: 基于R版本的条件依赖
- [ ] **优雅降级**: 缺少可选依赖时优雅降级

### 3.4 依赖隔离
- [ ] **功能模块**: 不同功能模块的依赖分离
- [ ] **测试隔离**: 测试依赖不影响核心功能
- [ ] **文档隔离**: 文档生成依赖独立
- [ ] **示例隔离**: 示例代码依赖不强制

**第3部分得分**: ___/16 (通过标准: ≥13)

---

## 4. 依赖稳定性检查 (权重: 25%)

[[LLM: 检查依赖的稳定性和可靠性]]

### 4.1 版本稳定性
- [ ] **稳定版本**: 使用稳定发布版本，避免开发版
- [ ] **版本锁定**: 关键依赖有适当的版本锁定
- [ ] **更新策略**: 有明确的依赖更新策略
- [ ] **回归测试**: 依赖更新后进行回归测试

### 4.2 API稳定性
- [ ] **API变更**: 评估依赖包API变更风险
- [ ] **弃用警告**: 处理依赖包的弃用警告
- [ ] **向前兼容**: 考虑依赖包的向前兼容性
- [ ] **迁移计划**: 有依赖包迁移的应急计划

### 4.3 性能影响
- [ ] **启动时间**: 依赖不显著影响包加载时间
- [ ] **内存使用**: 依赖的内存占用合理
- [ ] **运行性能**: 依赖不成为性能瓶颈
- [ ] **性能测试**: 包含依赖性能的基准测试

### 4.4 兼容性测试
- [ ] **R版本兼容**: 在多个R版本上测试依赖兼容性
- [ ] **平台兼容**: 在不同操作系统上测试
- [ ] **包版本兼容**: 测试不同依赖包版本的兼容性
- [ ] **持续集成**: CI/CD中包含依赖兼容性测试

**第4部分得分**: ___/16 (通过标准: ≥13)

---

## 5. 总体评估

### 5.1 得分汇总
- 依赖声明检查: ___/16 (25%)
- 依赖安全性检查: ___/16 (30%)
- 依赖最小化原则: ___/16 (20%)
- 依赖稳定性检查: ___/16 (25%)

**总分**: ___/64
**加权得分**: ___/100

### 5.2 风险等级
- **低风险** (90-100分): 依赖管理优秀，风险极低
- **中低风险** (80-89分): 依赖管理良好，风险可控
- **中等风险** (70-79分): 依赖管理基本合格，需要改进
- **中高风险** (60-69分): 依赖管理有问题，需要重大改进
- **高风险** (<60分): 依赖管理不当，存在重大风险

**本次评估风险等级**: {{risk_level}}

### 5.3 依赖清单
**直接依赖** ({{direct_count}}个):
{{REPEAT_START}}
- {{package_name}} ({{version_requirement}}) - {{usage_reason}}
{{REPEAT_END}}

**关键传递依赖** ({{indirect_count}}个):
{{REPEAT_START}}
- {{package_name}} ({{current_version}}) - 通过{{parent_package}}
{{REPEAT_END}}

### 5.4 风险识别
**高风险依赖**:
{{REPEAT_START}}
- {{package_name}}: {{risk_description}}
{{REPEAT_END}}

**潜在问题**:
{{REPEAT_START}}
- {{issue_description}}
{{REPEAT_END}}

### 5.5 改进建议
**立即行动**:
{{REPEAT_START}}
- {{urgent_action}}
{{REPEAT_END}}

**短期改进**:
{{REPEAT_START}}
- {{short_term_improvement}}
{{REPEAT_END}}

**长期规划**:
{{REPEAT_START}}
- {{long_term_plan}}
{{REPEAT_END}}

### 5.6 监控计划
- [ ] 建立依赖包安全监控
- [ ] 定期检查依赖包更新
- [ ] 监控依赖包维护状态
- [ ] 评估新依赖的引入

---

**检查完成时间**: {{completion_time}}
**建议复查时间**: {{review_time}}
**下次检查日期**: {{next_check_date}}

[[LLM: 依赖管理检查完成后，生成执行摘要：
1. 总体依赖风险等级
2. 最关键的依赖风险
3. 优先改进建议
4. 依赖管理成熟度评估
5. 是否建议发布包]]
