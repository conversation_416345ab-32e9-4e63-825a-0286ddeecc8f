"""
测试时间：2024年05月14日 

作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。

获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。

"""

library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
library(pals)

library(circlize)


chord_df01 <- readxl::read_excel("\\第5章 多变量图形的绘制\\Chord Diagram data01.xlsx")

color_list = c("#EF0000","#18276F","#FEC211","#3BC371",
    "#666699","#134B24","#FF6666","#6699CC","#CC6600","#009999")

####################################图5-8-2（a）默认颜色的“长”数据和弦图绘制示例

png(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_a.png",
               width = 5000, height = 5100,res=1800)
			   
pdf(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_a.pdf",
               width = 5, height = 5,family="serif")			   
			   
par(family="serif",cex = 0.6, mar = rep(0,4))
options(scipen=999)
chordDiagramFromDataFrame(chord_df01)
dev.off()
	   
####################################图5-8-2（b）修改颜色的“长”数据和弦图绘制示例

png(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_b.png",
               width = 5000, height = 5100,res=1800)
png(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_b.png",
               width = 5000, height = 5100,res=1800)
			   
par(family="serif",cex = 0.6, mar = rep(0,4))
options(scipen=999)
chordDiagramFromDataFrame(chord_df01,grid.col = color_list)
dev.off()			   
			   

####################################图5-8-2（c）连接弧样式和弦图绘制示例
png(file="F\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_c.png",
               width = 5000, height = 5100,res=1800)
pdf(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_c.pdf",
               width = 5, height = 5,family="serif")
			   
par(family="serif",cex = 0.6, mar = rep(0,4))
options(scipen=999)
chordDiagramFromDataFrame(chord_df01,grid.col = color_list,directional = 1, 
             direction.type = c("diffHeight", "arrows"),link.arr.type = "big.arrow")
dev.off()

####################################图5-8-2（d）添加数据流向指示箭头和弦图绘制示例			   

png(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_d.png",
               width = 5000, height = 5100,res=1800)
			   
pdf(file="\\第5章 多变量图形的绘制\\图5-8-2 R语言“长”数据样式和弦图绘制示例_d.pdf",
               width = 5, height = 5,family="serif")			   
			   
par(family="serif",cex = 0.6, mar = rep(0,4))
options(scipen=999)
chordDiagramFromDataFrame(chord_df01,grid.col = color_list,  
                          directional = 1, direction.type = "arrows",
                          link.arr.lwd =.2,link.arr.length = 0.1)
dev.off()

